﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>权限页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style type="text/css">
		#returnUserGroup {
			display: none;
			cursor: pointer;
			font-size: 14px;
		}
			#returnUserGroup:hover {
				text-decoration: underline;
			}

		.mytable tbody tr:hover {
			background-color: #F0F0F0 !important;
			color: #666 !important;
		}

		.transfer-list {
			display: flex;
			padding: 0;
		}

			.transfer-list span {
				width: 100%;
				text-align: center;
				display: inline-block;
				/*cursor: pointer;*/
			}

				/*.transfer-list span:hover {
					background-color: rgb(26, 188, 156) !important;
					color: white !important;
				}

				.transfer-list span:first-child {
					border-right: 1px solid #eee;
				}*/

		.selectRole {
			background-color: rgb(26, 188, 156) !important;
			color: white !important;
		}

		#isUserGroup, #isUser {
			display: none;
		}
		/* 设置下拉框的高度与表格单元相同 */
		td .xm-select-demo,
		td .layui-form-select {
			margin-top: -10px;
			margin-left: -15px;
			margin-right: -15px;
		}

		.layui-form-select dl dd {
			color: #000;
		}

		.laytable-cell-checkbox, .laytable-cell-numbers, .laytable-cell-radio, .laytable-cell-space {
			padding-left: 10px;
		}

		#unRegister-search {
			display: flex;
			padding: 7px 10px;
			justify-content: space-between;
		}

			#unRegister-search .layui-form-select {
				flex: 1 auto;
			}

		#list-container {
			height: calc(100% - 90px);
			overflow: auto;
			position: relative;
		}

			#list-container .table-body {
				overflow: hidden; /* 隐藏滚动条 */
				max-height: 100%; /* 设置最大高度 */
			}

			#list-container table {
				width: 100%;
				position: absolute;
				top: 0;
				left: 0;
			}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/xm-select.js"></script>
	<script type="text/javascript" src="js/request.js"></script>

	<script type="text/javascript">
		function onResize()
		{
			const windowInfo = {
				width: window.innerWidth,
				height: window.innerHeight
			}

			$('.layui-transfer-box').each(function (index, item)
			{
				$(item).css('height', windowInfo.height - 80);
			});
			//$('.layui-transfer-box:eq(0)').css('width', (windowInfo.width - 430) / 2);
			$('.layui-transfer-box:eq(2)').css('width', (windowInfo.width - 430) / 2);
			$('.layui-transfer-box:eq(3)').css('width', (windowInfo.width - 430) / 2);

			var parentHeight = $('.layui-transfer-box')[0].offsetHeight;
			$('#UnRegister-container').css('height', parentHeight - 92);
			$('#register-container').css('height', parentHeight - 92);
			//ReloadAlarmTable(table.cache.alarmlist);
			tableHeight = parentHeight - 92;
			renderTable('#unregistertoollist');
			renderTable('#registertoollist');
			table.reload('registertoollist', {
				data: register_listData,
				cols: [toolColumnsName('#registertoollist')],
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
			table.reload('unregistertoollist', {
				data: UnRegister_listData,
				cols: [toolColumnsName('#unregistertoollist')],
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
		}

		window.addEventListener('resize', onResize);

		function toolColumnsName(id)
		{
			var columnsArray = [
				{ type: 'checkbox', width: 40, event: 'singleclick', align: 'center' },
				{ field: 'Name', minWidth: 120, title: '名称', align: 'center' },
			];

			if (id === '#registertoollist' && typeof (RoleId) !== "object")
			{
				columnsArray.push(
					{
						field: 'Data', title: '权限', width: 90, templet: function (d)
						{
							if (d.Data.indexOf(CS.VideoMenu.$Work) >= 0)
								return "操作";

							return "查看";
						}
					},)
				columnsArray.push({
					width: 80, title: lang("操作"), toolbar: "#unregisterOp", event: 'singleclick', align: 'center', fixed: 'right'
				})
			}

			return columnsArray;
		}

		var UnRegister_listData = [];
		var unregisterChecked = [];
		var UnRegister_searchResult = [];
		var register_listData = [];
		var registerChecked = [];
		var register_searchResult = [];
		var currentElement = window.undefined;
		var editDialog = window.undefined;
		var listTypeName = '全部角色';
		var defaultResigter = [];
		var userAllData = [];
		var RoleId = new Uri(window.location.href).getQuery('Role');
		var isAdmin = window.CS.isConfigUser();

		var TableUtils = {
			// 检查ID是否存在于数组中
			isIdExist: function (array, id)
			{
				for (var i = 0; i < array.length; i++)
				{
					if (array[i].ID === id) return true;
				}
				return false;
			},

			addCheckedItem: function (array, item)
			{
				if (!this.isIdExist(array, item.ID))
				{
					array.push(item);
				}
			},

			removeCheckedItem: function (array, dataId)
			{
				var newChecked = [];
				for (var i = 0; i < array.length; i++)
				{
					if (array[i].ID !== dataId)
					{
						newChecked.push(array[i]);
					}
				}
				return newChecked;
			}
		};

		layui.config({ base: 'lib/layui/' });
		layui.extend({ notice: '../layui_exts/notice/notice' });
		layui.use(['element', 'table', 'layer', 'notice', 'jquery', 'form', 'laypage', 'excel'], function ()
		{
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, notice = layui.notice
				, excel = layui.excel;
			lang.call(this, $);
			notice.options =
			{
				closeButton: true,//显示关闭按钮
				debug: false,//启用debug
				positionClass: "toast-bottom-right",//弹出的位置,
				showDuration: "300",//显示的时间
				hideDuration: "500",//消失的时间
				timeOut: "3000",//停留的时间
				extendedTimeOut: "1000",//控制时间
				showEasing: "swing",//显示时的动画缓冲方式
				hideEasing: "linear",//消失时的动画缓冲方式
				iconClass: 'toast-info', // 自定义图标，有内置，如不需要则传空 支持layui内置图标/自定义iconfont类名
				onclick: null, // 点击关闭回调
			};
			column = toolColumnsName();

			renderTable('#unregistertoollist');
			table.on('checkbox(unregistertoolTable)', function (obj)
			{
				var currentPageData = table.cache.unregistertoollist;
				if (obj.checked)
				{
					if (obj.type == 'all')
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							TableUtils.addCheckedItem(unregisterChecked, currentPageData[i]);
						}
					} else
					{
						TableUtils.addCheckedItem(unregisterChecked, obj.data);
					}
				} else
				{
					if (obj.type == 'all')
					{
						unregisterChecked = unregisterChecked.filter(function (item)
						{
							for (var i = 0; i < currentPageData.length; i++)
							{
								if (currentPageData[i].ID === item.ID) return false;
							}
							return true;
						});
					} else
					{
						unregisterChecked = TableUtils.removeCheckedItem(unregisterChecked, obj.data.ID);
					}
				}
				if (unregisterChecked.length > 0)
				{
					$('#registerbtn').removeClass('layui-btn-disabled')
				} else
				{
					$('#registerbtn').addClass('layui-btn-disabled')
				}
			});
			renderTable('#registertoollist');
			table.on('tool(registertoolTable)', function (obj)
			{
				var data = obj.data;

				if (obj.event === 'edit')
				{
					editDialog = layer.open({
						title: lang('修改权限')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#editForm')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function ()
						{
							var vs = {};
							vs.ad = data.Data;
							if (vs.ad.indexOf(CS.VideoMenu.$Work) >= 0)
								vs.op = CS.VideoMenu.$View + ',' + CS.VideoMenu.$Part + ',' + CS.VideoMenu.$Work;
							else if (vs.ad.indexOf(CS.VideoMenu.$Part) >= 0)
								vs.op = CS.VideoMenu.$View + ',' + CS.VideoMenu.$Part;
							else
								vs.op = CS.VideoMenu.$View;
							for (var i in CS.VideoMenu)
							{
								if (!isNaN(CS.VideoMenu[i]))
									vs['equip[' + i + ']'] = vs.ad.indexOf(CS.VideoMenu['$' + i]) >= 0;
							}
							form.val('form-edit', vs);
						}
						, btn1: function (index)
						{
							var choseId = $('#rolelist .selectRole').attr('id');
							var user = userAllData.filter(function (item) { return item.ID == choseId });
							if (user.length != 0 && !user[0].noDefault)
							{ //// 修改权限时 是默认用户情况
								for (var i = 0; i < register_listData.length; i++)
								{
									if (register_listData[i].ID == data.ID)
									{
										register_listData[i].authData = getAuthData(form.val('form-edit'));
									} else
									{
										register_listData[i].authData = register_listData[i].Data;
									}
								}
								OnRegisterTool(register_listData);
								layer.close(index);
								return;
							}
							AjaxApi.request({
								url: "../Auth/Grant.do",
								data: {
									Video: data.Video,
									Name: data.Name,
									User: data.User,
									Role: data.Role,
									Data: getAuthData(form.val('form-edit')),
									Log: '将 ' + data.Name + '重新分配权限 '
								},
								dataType: 'text',
								success: function (result)
								{
									layer.close(editDialog);
									FillUnregiterToolAndRegisterTool(currentElement.id);
								}
							});
						}
					});
				}
			});

			table.on('checkbox(registertoolTable)', function (obj)
			{
				var currentPageData = table.cache.registertoollist;
				if (obj.checked)
				{
					if (obj.type == 'all')
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							TableUtils.addCheckedItem(registerChecked, currentPageData[i]);
						}
					} else
					{
						TableUtils.addCheckedItem(registerChecked, obj.data);
					}
				} else
				{
					if (obj.type == 'all')
					{
						registerChecked = registerChecked.filter(function (item)
						{
							for (var i = 0; i < currentPageData.length; i++)
							{
								if (currentPageData[i].ID === item.ID) return false;
							}
							return true;
						});
					} else
					{
						registerChecked = TableUtils.removeCheckedItem(registerChecked, obj.data.ID);
					}
				}
				if (registerChecked.length > 0)
				{
					$('#unregisterbtn').removeClass('layui-btn-disabled')
				} else
				{
					$('#unregisterbtn').addClass('layui-btn-disabled')
				}
			});

			onResize();
			getAllAreaAndGroup();
			form.on("select(unRegister-area)", function (data)
			{
				$("#unRegister-group").val('');
				form.render('select');
				addSelectIcon();
				onUnRegisterToolSearch(data.value, 'area');
			});
			form.on("select(unRegister-group)", function (data)
			{
				$("#unRegister-area").val('');
				form.render('select');
				addSelectIcon();
				onUnRegisterToolSearch(data.value, 'group');
			});
			form.on("select(register-area)", function (data)
			{
				$("#register-group").val('');
				form.render('select');
				addSelectIcon();
				onRegisterToolSearch(data.value, 'area');
			});
			form.on("select(register-group)", function (data)
			{
				$("#register-area").val('');
				form.render('select');
				addSelectIcon();
				onRegisterToolSearch(data.value, 'group');
			});
		});

		$(document).on('ready', function ()
		{
			if (!!window.RCS && !!window.RCS.Plugs && window.RCS.Plugs.Realm == 'LCD')
				$('.name').text('Unit');

			if (typeof (RoleId) == "object")
			{
				 $('#isUserGroup').show();
				 // if (isAdmin)
					FillRoleDataTable();
			} else
			{
				var name = new Uri(window.location.href).getQuery('RoleName');
				$('#isUser').html(name + ' 用户组');
				$('#isUser').show();
				$('#returnUserGroup').show();
				$('#authPage').hide();
				getDefaultAuth();
				FillUserDataTable(RoleId);
			}

			$('#returnUserGroup').on('click', function ()
			{
				window.location.href = 'UserGroup.html';
			})
		});
		var tableHeight = 0;
		function renderTable(id)
		{
			table.render({
				elem: id
				, even: true
				, loading: false
				, defaultToolbar: []
				, page: {
					// limits: [20, 30, 50],
					limit: 20,
					layout: ['count', 'prev', 'page', 'next']
				}
				, height: tableHeight
				, cols: [toolColumnsName(id)]
				, data: []
			});
		}

		function FillRoleDataTable()
		{
			AjaxApi.request({
				url: "/Role/List.do",
				data: {},
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					var userGroupDatas = getRoleUserGroup(res.data, 'userGroup');
					setField(userGroupDatas, 'role');
					clearAllStauts();
				}
			})
		}
		function FillUserDataTable(RoleId)
		{
			AjaxApi.request({
				url: "../User/List.do",
				data: { Flags: 0, Take: 99999, Role: RoleId },
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					var annex = res.annex;
					for (var i = 0; i < res.data.length; i++)
					{
						for (var j = 0; j < annex.length; j++)
						{
							var a = annex[j];
							if (a.User == res.data[i].ID && a.Role == RoleId && a.Type == window.CS.AuthData.TYPE_ROLE)
							{
								res.data[i].noDefault = annex[j].Value2;
								break;
							}
						}
					}
					userAllData = res.data;
					setField(res.data);
					clearAllStauts();
				}
			})
		}
		// 获取默认权限机台
		function getDefaultAuth()
		{
			if (!!window.defaultResult)
			{
				var v = $.eval(window.defaultResult);
				return v.data;
			}

			AjaxApi.request({
				url: "/ListAuth.do",
				data: { Flags: 0, Take: 99999, Type: window.CS.AuthData.TYPE_AUTH, Role: RoleId, User: '' },
				dataType: 'text',
				success: function (result)
				{
					window.defaultResult = result;
					getDefaultAuth();
				}
			})
		}

		// RAF优化动画效果
		var windowRequestAnimationFrame =
			window.requestAnimationFrame ||
			window.webkitRequestAnimationFrame ||
			window.mozRequestAnimationFrame ||
			window.oRequestAnimationFrame ||
			window.msRequestAnimationFrame ||
			function (callback)
			{
				return setTimeout(callback, 1000 / 60);
			}; //时间刻度自行设置
		var windowCancelAnimationFrame = window.cancelAnimationFrame || function (name) { clearTimeout(name) };
		// 虚拟滚动列表
		var listData = [];
		var searchResult = []; // 筛选左侧列表集合
		var clickListId = '';

		var container;
		var itemHeight = 38;
		var visibleItemCount = 0;
		var lastStartIndex = 0;
		var lastEndIndex = 0;
		var scrollRAF;
		function setField(data)
		{
			listData = data;
			container = document.getElementById("list-container");
			visibleItemCount = Math.ceil(container.clientHeight / itemHeight) + 2;
			lastEndIndex = lastStartIndex + visibleItemCount;
			container.addEventListener("scroll", function ()
			{
				if (scrollRAF)
				{
					windowCancelAnimationFrame(scrollRAF);
				}
				scrollRAF = windowRequestAnimationFrame(updateVisibleItems);
			});
			getWrapperScrollHeight(listData);
			RenderRoleTable(listData.slice(lastStartIndex, lastEndIndex));
		}
		function getWrapperScrollHeight(data)
		{
			var wrapper = document.getElementById("list-wrapper");
			wrapper.style.height = data.length * 38 + "px";
		}
		// 获取可视范围内的数据
		function updateVisibleItems()
		{
			var scrollTop = container.scrollTop;
			var startIndex = Math.floor(scrollTop / itemHeight);
			var endIndex = Math.min(startIndex + visibleItemCount, listData.length);
			var wrapper = document.getElementsByClassName("layui-table")[0];
			lastStartIndex = startIndex;
			lastEndIndex = endIndex;

			if (searchResult.length > 0)
			{
				RenderRoleTable(searchResult.slice(startIndex, endIndex));
			} else
			{
				RenderRoleTable(listData.slice(startIndex, endIndex));
			}
			wrapper.style.top = startIndex * itemHeight + "px";
		}

		function RenderRoleTable(showdata)
		{
			$('#rolelist').empty();
			$('#rolelist').off('click', 'tr');
			var tb = '<tbody>';

			for (var i = 0; i < showdata.length; i++)
			{
				var tr = '';
				if (i % 2 == 1)
				{
					tr = '<tr id="' + showdata[i].ID + '" style="background-color:#F8F8F8;" data-index="' + i + '">';
				}
				else
				{
					tr = '<tr id="' + showdata[i].ID + '" data-index="' + i + '">';
				}

				tr += '<td style="border: 0px;" data-field="Name" data-key="1-0-0" align="center" lay-event="singleclick">';

				if (typeof (RoleId) == "object")
				{
					tr += '<div>' + showdata[i].Name.substring(5, showdata[i].Name.length) + '</div>';
				} else
				{
					tr += '<div>' + showdata[i].Name + '</div>';
				}

				tr += '</td>'
				tr += '</tr>';
				tb += tr;
			}

			tb += '</tbody>';
			$('#rolelist').append(tb);
			if (!!clickListId)
			{ //选中的li重新加上背景色
				$('#' + clickListId).addClass('selectRole')
			}
			// $('#rolelist').data('data', tabledata);
			$('#rolelist').on('click', 'tr', function ()
			{
				roleSelected(this);
			});
		}
		// 选择角色
		function clearAllStauts()
		{
			UnRegister_selectData = []; // 清空未注册机台选中集合
			unregisterChecked = []; // 清空勾选集合
			unregister_searchResult = [];
			$("#UnRegister-search").val("");
			register_selectData = []; // 清空已注册机台选中集合
			registerChecked = []; // 清空勾选集合
			register_searchResult = [];
			$("#register-search").val("");

			$('#unregisterbtn').addClass('layui-btn-disabled');
			$('#registerbtn').addClass('layui-btn-disabled');
			$('#checkAllUnRegister').removeClass('layui-form-checked');
			$('#checkAllRegister').removeClass('layui-form-checked');

			$("#unRegister-group").val('');
			$("#unRegister-area").val('');
			$("#register-group").val('');
			$("#register-area").val('');
			form.render('select');
			addSelectIcon();
		}
		function roleSelected(elem)
		{
			currentElement = elem;
			clearAllStauts();

			$('#rolelist tr').each(function (index, item)
			{
				if (index % 2 == 1)
				{
					$('#' + item.id).css('background-color', '#F8F8F8');
				}
				else
				{
					$('#' + item.id).css('background-color', 'white');
				}

				$('#' + item.id).css('color', '#666');
				$('#' + item.id).removeClass('selectRole');
			})

			$('#' + elem.id).addClass('selectRole');
			clickListId = elem.id
			FillUnregiterToolAndRegisterTool(elem.id);
		}

		function onRoleSearch(elem)
		{
			searchResult = [];
			var table_data = listData;
			if (table_data == null)
				return;

			var search = elem.value;

			for (var i = 0; i < table_data.length; i++)
			{
				if (table_data[i].Name.toLowerCase().indexOf(search.toLowerCase()) >= 0)
				{
					searchResult.push(table_data[i]);
				}
			}
			container.scrollTop = 0; // 重置滚轮高度
			getWrapperScrollHeight(searchResult);
			RenderRoleTable(searchResult);
		}

		// 获取注册、未注册机台数据
		function FillUnregiterToolAndRegisterTool(id)
		{
			register_listData = [];
			UnRegister_listData = [];
			var ajax = {};
			ajax.dataType = 'text';
			if (typeof (RoleId) == "object")
			{
				ajax.url = '../ListVideo.do?Flags=0&Take=65536';
			} else
			{
				ajax.url = '../ListVideo.do?Flags=0&Take=65536&ConditionName=' + window.CS.AuthData.TYPE_ALLOC + '&ConditionValue=' + RoleId;
			}
			window.currentRole = id;
			ajax.success = function (result)
			{
				var res = $.eval(result);
				if (res.code != "Success")
					return;

				var ajax = {};
				ajax.dataType = 'text';
				ajax.data = { Flags: 0, Take: 65535 };
				if (typeof (RoleId) == "object")
				{
					ajax.data.Type = window.CS.AuthData.TYPE_ALLOC;
					ajax.data.Role = window.currentRole;
				} else
				{
					ajax.data.Type = window.CS.AuthData.TYPE_AUTH;
					ajax.data.User = window.currentRole;
					ajax.data.Role = RoleId;
				}
				ajax.url = '/ListAuth.do';
				window.currentAll = res.data;
				ajax.success = function (result)
				{
					var res = $.eval(result);
					var user = userAllData.filter(function (item) { return item.ID == id });
					if (typeof (RoleId) !== "object")
					{
						if (user.length == 0 || user[0].noDefault)
							register_listData = res.data;
						else
							register_listData = getDefaultAuth();
					} else
					{
						register_listData = res.data;
					}
					for (var i = register_listData.length - 1; i >= 0; i--)
					{
						register_listData[i].Target = register_listData[i]['Video'];
						
						var info = window.currentAll.filter(function (item)
						{
							return item.ID == register_listData[i].Target
						});
						if (info.length > 0)
						{
							register_listData[i].Area = info[0].Area;
							register_listData[i].Unit = info[0].Unit;
							register_listData[i].Name = info[0].Name;
						}
					}

					var unregisterTool_data = window.currentAll.filter(function (item, index, arr)
					{
						for (var i = 0; i < register_listData.length; i++)
						{
							if (item.ID == register_listData[i].Target)
								return false;

							if (item.Parent.length > 0)
								return false;
						}

						return true;
					});

					table.reload('registertoollist', {
						data: register_listData,
						cols: [toolColumnsName('#registertoollist')],
						page: {
							layout: ['count', 'prev', 'page', 'next']
							, curr: 1
						}
					});
					
					table.reload('unregistertoollist', {
						data: unregisterTool_data,
						cols: [toolColumnsName('#unregistertoollist')],
						page: {
							layout: ['count', 'prev', 'page', 'next']
							, curr: 1
						}
					});
					UnRegister_listData = unregisterTool_data;
				};
				AjaxApi.request(ajax);
			};
			AjaxApi.request(ajax);
		}

		function getAuthData(info)
		{
			var vs = ',';
			for (var k in CS.VideoMenu)
			{
				var i = CS.VideoMenu[k];
				if (!isNaN(i) && info['equip[' + k + ']'] == 'on')
					vs = vs + k + ',';
			}
			vs[0] = info.op;
			if (info.op.indexOf(CS.VideoMenu.$Work) > 0)
			{
				vs = vs + info.op + ',';
				return vs;
			}
			if (info.op.indexOf(CS.VideoMenu.$Part) > 0)
			{
				vs = vs + info.op + ',';
				return vs;
			}
			vs = vs + CS.VideoMenu.$View + ',';
			return vs;
		}
		function bindOrUnbind(url, data, item)
		{
			return new Promise(function (resolve, reject)
			{
				AjaxApi.request({
					url: url,
					data: data,
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.code !== 'Success')
						{
							reject(res.msg);
						}
						resolve(item);
					},
					error: function (error)
					{
						reject(error);
					}
				});
			});
		}
		// 未注册机台搜索
		function onUnRegisterToolSearch(value, typeName)
		{
			UnRegister_searchResult = []
			var table_data = UnRegister_listData;
			if (table_data == null)
				return;

			for (var i = 0; i < table_data.length; i++)
			{
				if (typeName == 'area' && table_data[i].Area == value)
				{
					UnRegister_searchResult.push(table_data[i]);
				}
				if (typeName == 'group' && table_data[i].Unit == value)
				{
					UnRegister_searchResult.push(table_data[i]);
				}
			}

			table.reload('unregistertoollist', {
				data: value == '' ? table_data : UnRegister_searchResult,
				cols: [toolColumnsName('#unregistertoollist')],
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
		}
		// 未注册》》》注册
		function OnRegisterTool(defaultData)
		{
			var currentIndex = 0;
			var choseId = $('#rolelist .selectRole').attr('id');
			if (typeof (RoleId) !== "object" && !defaultData)
			{
				editDialog = layer.open({
					title: lang('权限设置')
					, type: 1
					, resize: false
					, id: 'edit1'
					, content: $('#editForm')
					, btn: [lang('确认'), lang('取消')]
					, btnAlign: 'c'
					, success: function () { }
					, btn1: function (index)
					{
						var user = userAllData.filter(function (item) { return item.ID == choseId });
						if (user.length != 0 && !user[0].noDefault)
						{
							for (var i = 0; i < register_listData.length; i++)
							{
								var item = register_listData[i];
								item.ID = item.Video;
								item.authData = item.Data;
								unregisterChecked.push(item)
							}
						} /// 默认用户时将已注册机台全部重新添加
						taskBegin();
						layer.close(index);
					}
				})
			} else
			{
				if (typeof (RoleId) !== "object") unregisterChecked = defaultData;
				taskBegin();
			}
			
			function processNext()
			{
				if (currentIndex >= unregisterChecked.length)
				{
					return Promise.resolve();
				}

				var item = unregisterChecked[currentIndex];
				var authData = '';
				var url = '../Auth/Grant.do', data = {};
				var video = '';
				if (!defaultData)
				{
					video = item.ID;
				} else
				{
					video = item.Video;
				}
				if (typeof (RoleId) == "object")// 超级管理员
				{
					authData = ',View,Part,Work,';
					data = {
						Video: video,
						Name: item.Name,
						Role: choseId,
						Auth: window.CS.AuthData.TYPE_CHIEF,
						Data: authData,
						Type: window.CS.AuthData.TYPE_ALLOC,
						Log: '将 ' + item.Name + '分配给用户组 ' + choseId
					}
				}
				else
				{
					authData = item.authData ? item.authData : getAuthData(form.val('form-edit'));
					data = {
						Video: video,
						Name: item.Name,
						User: choseId,
						Role: RoleId,
						Data: authData,
						Log: '将 ' + item.Name + '分配给用户 ' + choseId
					}
				}

				return bindOrUnbind(url, data, item)
					.then(function (result)
					{
						/*var idx = UnRegister_listData.findIndex(function (val) { return val.ID === item.ID });
						register_listData.push(result);
						UnRegister_listData.splice(idx, 1);*/
						currentIndex++;
						return processNext();
					})
					.catch(function (error)
					{
						notice.error('解除绑定' + item.Name + ' ' + error);
						currentIndex++;
						return processNext();
					});
			}

			function taskBegin()
			{
				processNext().then(function ()
				{
					clearAllStauts();
					if (typeof (RoleId) !== "object")
					{
						var userIdx = userAllData.findIndex(function (item) { return item.ID == choseId });
						userAllData[userIdx].noDefault = 1;
					}
					FillUnregiterToolAndRegisterTool(currentElement.id);
					if (!!window.top.FillFavTreeTable)
						window.top.FillFavTreeTable();
				});
			}
			
		}

		// 已注册机台搜索
		function onRegisterToolSearch(value, typeName)
		{
			register_searchResult = []
			var table_data = register_listData;
			if (table_data == null)
				return;

			for (var i = 0; i < table_data.length; i++)
			{
				if (typeName == 'area' && table_data[i].Area == value)
				{
					register_searchResult.push(table_data[i]);
				}
				if (typeName == 'group' && table_data[i].Unit == value)
				{
					register_searchResult.push(table_data[i]);
				}
			}
			table.reload('registertoollist', {
				data: value == '' ? table_data : register_searchResult,
				cols: [toolColumnsName('#registertoollist')],
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
		}
		// 已注册》》》未注册
		function OnUnRegisterTool()
		{
			var currentIndex = 0;

			var choseId = $('#rolelist .selectRole').attr('id');
			var user = userAllData.filter(function (item) { return item.ID == choseId });
			if (user.length != 0 && !user[0].noDefault)
			{
				var reverseData = register_listData.filter(function (item)
				{
					for (var i = 0; i < registerChecked.length; i++)
					{
						if (registerChecked[i].ID == item.ID)
						{
							return false;
						}
					}
					return true;
				})
				clearAllStauts();
				if (reverseData.length == 0)
				{
					AjaxApi.request({
						url: '../Grant.do?Role=' + RoleId + '&User=' + user[0].ID + '&Delete=1&Reallocate=1',
						success: function ()
						{
							clearAllStauts();
							var userIdx = userAllData.findIndex(function (item) { return item.ID == choseId });
							userAllData[userIdx].noDefault = 1;
							FillUnregiterToolAndRegisterTool(currentElement.id);
							if (!!window.top.FillFavTreeTable)
								window.top.FillFavTreeTable();
						}
					})
				} else
				{
					reverseData = reverseData.map(function(item) {
						item.authData = item.Data;
						return item;
					})
					OnRegisterTool(reverseData);
				}
				return;
			}
			taskProcess();
			function processNext()
			{

				if (currentIndex >= registerChecked.length)
				{
					return Promise.resolve();
				}

				var item = registerChecked[currentIndex];
				var url = '../Auth/Grant.do', data = {};
				var choseId = $('#rolelist .selectRole').attr('id');
				if (typeof (RoleId) == "object")
				{
					data = {
						Delete: 1,
						Video: item.Video,
						Role: choseId,
						Type: window.CS.AuthData.TYPE_ALLOC,
						Log: '删除 ' + item.Name
					}
				}
				else
				{
					data = {
						Delete: 1,
						Video: item.Video,
						User: choseId,
						Role: RoleId,
						Log: '删除 ' + item.Name
					}
				}

				return bindOrUnbind(url, data, item)
					.then(function (result)
					{
						var idx = register_listData.findIndex(function (val) { return val.ID === item.ID });
						UnRegister_listData.push(result);
						register_listData.splice(idx, 1);
						currentIndex++;
						return processNext();
					})
					.catch(function (error)
					{
						notice.error('解除绑定' + item.Name + ' ' + error);
						currentIndex++;
						return processNext();
					});
			}

			function taskProcess()
			{
				processNext().then(function ()
				{
					clearAllStauts();
					FillUnregiterToolAndRegisterTool(currentElement.id);
					if (!!window.top.FillFavTreeTable)
						window.top.FillFavTreeTable();
				});
			}
			
		}
		// 获取所有的区域、机台组
		function getAllAreaAndGroup()
		{
			AjaxApi.request({
				url: "../Tool/ListConfig.do",
				data: { TypeIn: [window.CS.ConfigData.TYPE_AREA, window.CS.ConfigData.TYPE_TOOLGROUP], Flags: 0, Take: 1024 },
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);

					res.arealist = [];
					res.toolgrouplist = [];
					for (var i = 0; i < res.data.length; i++)
					{
						if (res.data[i].Type == window.CS.ConfigData.TYPE_AREA)
							res.arealist.push(res.data[i]);
						else if (res.data[i].Type == window.CS.ConfigData.TYPE_TOOLGROUP)
							res.toolgrouplist.push(res.data[i]);
					}
					arealist = buildTree(res.arealist);
					toolgrouplist = buildTree(res.toolgrouplist);
					for (var i = 0; i < arealist.length; i++)
					{
						if (arealist[i].Parent)
						{
							arealist[i].icon =
								'<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
						} else
						{
							arealist[i].icon =
								'<i class="layui-icon layui-icon-layer"></i>';
						}
					}
					for (var i = 0; i < toolgrouplist.length; i++)
					{
						if (toolgrouplist[i].Parent)
						{
							toolgrouplist[i].icon =
								'<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
						} else
						{
							toolgrouplist[i].icon =
								'<i class="layui-icon layui-icon-layer"></i>';
						}
					}
					setAreaAndGroup();
				}
			});
		}
		// 设置区域、机台组
		function setAreaAndGroup()
		{
			var areaSelect = $('select[name="unRegister-area"]');
			var areaSelect1 = $('select[name="register-area"]');
			for (var i = 0; i < arealist.length; i++)
			{
				areaSelect.append(new Option(arealist[i].Name, arealist[i].ID));
				areaSelect1.append(new Option(arealist[i].Name, arealist[i].ID));
			}

			var groupSelect = $('select[name="unRegister-group"]');
			var groupSelect1 = $('select[name="register-group"]');
			for (var i = 0; i < toolgrouplist.length; i++)
			{
				groupSelect.append(new Option(toolgrouplist[i].Name, toolgrouplist[i].ID));
				groupSelect1.append(new Option(toolgrouplist[i].Name, toolgrouplist[i].ID));
			}
			form.render('select');
			addSelectIcon();
		}
		function addSelectIcon()
		{
			var areaSelect = $('select[name="unRegister-area"]');
			var groupSelect = $('select[name="unRegister-group"]');
			var areaSelect1 = $('select[name="register-area"]');
			var groupSelect1 = $('select[name="register-group"]');
			// 添加自定义图标
			var areaDl = areaSelect.next("div.layui-form-select").find("dl");
			areaDl.find("dd:not(.layui-select-tips)").each(function (index)
			{
				if (arealist[index])
				{
					var icon = arealist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
			var groupDl = groupSelect.next("div.layui-form-select").find("dl");
			groupDl.find("dd:not(.layui-select-tips)").each(function (index)
			{
				if (toolgrouplist[index])
				{
					var icon = toolgrouplist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
			var areaDl1 = areaSelect1.next("div.layui-form-select").find("dl");
			areaDl1.find("dd:not(.layui-select-tips)").each(function (index)
			{
				if (arealist[index])
				{
					var icon = arealist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
			var groupDl1 = groupSelect1.next("div.layui-form-select").find("dl");
			groupDl1.find("dd:not(.layui-select-tips)").each(function (index)
			{
				if (toolgrouplist[index])
				{
					var icon = toolgrouplist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
		}
		function buildTree(items)
		{
			var result = [];
			var map = {};
			var i, item;

			for (i = 0; i < items.length; i++)
			{
				item = items[i];
				map[item.ID] = item;
			}

			for (i = 0; i < items.length; i++)
			{
				item = items[i];
				if (item.Parent)
				{
					var parent = map[item.Parent];
					if (parent)
					{
						if (!parent.children)
						{
							parent.children = [];
						}
						parent.children.push(item);
					}
				} else
				{
					result.push(item);
				}
			}

			function flattenTree(arr)
			{
				var flatArray = [];

				function flatten(nodes)
				{
					var i;
					for (i = 0; i < nodes.length; i++)
					{
						flatArray.push(nodes[i]);
						if (nodes[i].children && nodes[i].children.length)
						{
							flatten(nodes[i].children);
							delete nodes[i].children;
						}
					}
				}

				flatten(arr);
				return flatArray;
			}

			return flattenTree(result);
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<div style="margin:10px;">
		<fieldset class="layui-elem-field layui-field-title">
			<legend>
				<span id="returnUserGroup">返回</span>
				<span id="authPage">权限设置</span>
			</legend>
		</fieldset>
		<div style="height: calc(100% - 66px); min-width: 1074px; min-height: 600px;">
			<div class="layui-form-item" style="display: flex;">
				<div class="layui-inline">
					<div class="layui-transfer-box" style="width: 258px !important; min-height: 600px;">
						<div class="layui-transfer-header transfer-list">
							<span id="isUserGroup">用户组</span>
							<span id="isUser">用户</span>
						</div>

						<div class="layui-transfer-search">
							<i class="layui-icon layui-icon-search"></i>
							<input type="text" class="layui-input" placeholder="请输入" oninput="onRoleSearch(this)">
						</div>

						<div id="list-container">
							<div id="list-wrapper">
								<table style="border: 0px;" class="layui-table" id="rolelist" lay-filter="rolelistevent" lay-skin="row"></table>
							</div>
						</div>
					</div>
				</div>

				<div class="layui-inline">
					<div class="layui-transfer-box Line" style="width: 3px; height: calc(100% - 60px); border-radius: 5px; border: none; min-height: 600px;"></div>
				</div>

				<div class="layui-inline">
					<div class="layui-transfer-box" style="min-width: 340px; min-height: 600px;">
						<div class="layui-transfer-header" style="padding-left: 15px;">
							<div id="checkAllUnRegister">
								<span>未注册<span class="name">机台</span></span>
							</div>
						</div>

						<form id="unRegister-search" class="layui-form">
							<select id="unRegister-area" name="unRegister-area" lay-filter="unRegister-area" lay-search="">
								<option value="">请选择区域</option>
							</select>
							<select id="unRegister-group" name="unRegister-group" lay-filter="unRegister-group" lay-search="">
								<option value="">请选择机台组</option>
							</select>
						</form>

						<div id="UnRegister-container">
							<table style="border: 0px;" id="unregistertoollist" lay-filter="unregistertoolTable"></table>
						</div>
					</div>

					<div class="layui-transfer-active">
						<button type="button" id="registerbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0" onclick="OnRegisterTool();">
							<i class="layui-icon layui-icon-next"></i>
						</button>
						<button type="button" id="unregisterbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1" onclick="OnUnRegisterTool();">
							<i class="layui-icon layui-icon-prev"></i>
						</button>
					</div>

					<div class="layui-transfer-box" style="min-width: 340px; min-height: 600px;">
						<div class="layui-transfer-header" style="padding-left: 15px;">
							<div id="checkAllRegister">
								<span>已注册<span class="name">机台</span></span>
							</div>
						</div>

						<form id="unRegister-search" class="layui-form">
							<select id="register-area" name="register-area" lay-filter="register-area" lay-search="">
								<option value="">请选择区域</option>
							</select>
							<select id="register-group" name="register-group" lay-filter="register-group" lay-search="">
								<option value="">请选择机台组</option>
							</select>
						</form>

						<div id="register-container">
							<div id="register-wrapper">
								<table style="border: 0px;" id="registertoollist" lay-filter="registertoolTable"></table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<script type="text/html" id="unregisterOp">
				<a class="layui-btn layui-btn-xs" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			</script>
		</div>
		<form id="editForm" class="layui-form" lay-filter="form-edit" style="display: none; padding-top: 20px; padding-right: 20px;">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;">操作权限</label>
				</div>
				<div class="layui-inline">
					<input type="radio" name="op" value="View" title="查看" checked="checked" />
					<input type="radio" name="op" value="View,Part,Work" title="操作" />
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;"><span class="name">机台</span>权限</label>
				</div>
				<div class="layui-inline">
					<div style="display: inline;"><input type="checkbox" name="equip[Oust]" lay-skin="primary" title="中断操作" /></div>
					<div style="display: inline;"><input type="checkbox" name="equip[SimpleLock]" lay-skin="primary" title="普通锁定" /></div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;">RPA脚本</label>
				</div>
				<div class="layui-inline">
					<div style="display: inline;"><input type="checkbox" name="equip[RunScript]" lay-skin="primary" title="运行" /></div>
					<div style="display: inline;"><input type="checkbox" name="equip[StopScript]" lay-skin="primary" title="停止" /></div>
				</div>
			</div>
		</form>
	</div>
</body>
</html>