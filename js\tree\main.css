/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
    width: 6px;
    height: 8px;
}

::-webkit-scrollbar-button {
    display: none;
}

::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.6);
    cursor: pointer;
    border-radius: 4px;
}

::-webkit-scrollbar-corner {
    display: none;
}

::-webkit-resizer {
    display: none;
}

.vxe-table--render-default.size--small .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default.size--small .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default.size--small .vxe-header--column:not(.col--ellipsis) {
  padding: 3px 0;
  background-color: #1f2d3d;
  color: #fff;
  font-weight: 500;
}
.vxe-table--render-default.border--default .vxe-table--header-wrapper,
.vxe-table--render-default.border--full .vxe-table--header-wrapper,
.vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: transparent;
}
.vxe-table--render-wrapper {
  background-color: transparent;
}
.vxe-table--render-default .vxe-table--body-wrapper table,
.vxe-table--render-default .vxe-table--footer-wrapper table {
  background-color: transparent;
}
.vxe-table--render-default {
  color: #fff;
}

.vxe-table--render-default .vxe-cell {
  padding-left: 3px;
  padding-right: 3px;
}

.vxe-table--render-default .vxe-cell--checkbox .vxe-checkbox--icon {
  font-weight: 500;
  font-size: 1.14em;
}

.vxe-table--render-default .vxe-body--row.row--hover,
.vxe-table--render-default .vxe-body--row.row--hover.row--stripe {
  background-color: #1abc9c;
}

.vxe-table--render-default .vxe-cell--checkbox.is--checked,
.vxe-table--render-default .vxe-cell--checkbox.is--checked .vxe-checkbox--icon,
.vxe-table--render-default .vxe-cell--checkbox.is--indeterminate,
.vxe-table--render-default
  .vxe-cell--checkbox.is--indeterminate
  .vxe-checkbox--icon {
  color: #5fb878;
}

.vxe-table--render-default .vxe-tree--node-btn {
  color: #5fb878;
}

.vxe-table--render-default .vxe-icon-caret-right {
    color: #fff;
}

.vxe-icon-star {
    color: #fff;
}

.vxe-icon-star-fill {
    color: #5fb878;
}

.vxe-button [class*=vxe-icon-] {
    font-size: 1.4em;
}
/*边框*/
.vxe-table--render-default.border--full .vxe-body--column,
.vxe-table--render-default.border--full .vxe-footer--column,
.vxe-table--render-default.border--full .vxe-header--column {
    background-image: none;
    border-bottom: 1px dotted #445954;
    border-right: 1px dotted #445954;
    /* background-image: linear-gradient(#445954, #445954), linear-gradient(#445954, #445954);*/
}

.vxe-table--render-default .vxe-table--border-line {
    border: 1px dotted #445954;
}
.vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line {
    border: none;
}

button, input, optgroup, option, select, textarea {
    font-family: inherit;
    font-size: inherit;
    font-style: inherit;
    font-weight: inherit;
    outline: 0;
}
/* 依此类推 */

ul li {
    margin: 0;
    padding: 0;
    list-style: none;
}
.tab-title {
    display: flex;
    padding: 0;
    margin: 0;
    list-style: none;
    border-bottom: 1px solid #fff;
}

    .tab-title li {
        font-size: 14px;
        line-height: 20px;
        border-radius: 2px 2px 0 0;
        flex: 1;
        text-align: center;
        padding: 0 15px;
        margin: 0 2px;
        cursor: pointer;
        background-color: #1abc9c;
        color: #fff;
    }

    .tab-title .active {
        color: red;
        border: 1px solid #fff;
        border-bottom: none;
    }

.tab-content {
    margin-top: 3px;
    width: 100%;
    color: #fff;
}

.inputValue {
    width: 98%;
    height: 20px;
    margin-left: 1%;
    margin-bottom: 2px;
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 2px;
    box-sizing: border-box;
}