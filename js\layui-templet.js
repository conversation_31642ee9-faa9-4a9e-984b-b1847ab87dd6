﻿function dateTemplet(d)
{
	if (!d[this.field])
		return '-';

	return new Date(parseInt(d[this.field])).toString('yyyy-mm-dd HH:mm');
};

function templetPlus(d)
{
	var c = parseInt(d);
	if (c > 0)
		return dateFormat1(c);
	return "-";
}
function templet(d)
{
	var c = parseInt(d[this.field]);
	if (c > 0) {
		return dateFormat1(c);
	}
	return " ";
}
/**
  * 英式日期格式：日，月，年.
  * @param string d 时间戳.
  */
function templet2(d) {
	var c = parseInt(d[this.field]);
	if (c > 0)
		return dateFormat3(c);
	return " ";
}

function templetName(d) {
	var c = d[this.field];
	if (!!c)
		return c.substring(c.indexOf('/') + 1);
	return '';
}

/*
 * yyyy-mm-dd hh:mi:ss
 */
function dateFormat1(t)
{
	return new Date(t).toString(!!window.langs && window.langs.name == 'EN' ? 'mm/dd/yyyy HH:nn:ss' : 'yyyy-mm-dd HH:nn:ss');
}

/*
 * yyyy-mm-dd
 */
function dateFormat2(t)
{
	return new Date(t).toString(!!window.langs && window.langs.name == 'EN' ? 'mm/dd/yyyy' : 'yyyy-mm-dd');
}

//自定义宽度
function selfWidth(res, column, minWidth)
{
	var ii = column.length;
	if (ii === 0)
	{
		return;
	}
	var thArr = [];
	for (let i = 0; i < ii; i++)
		thArr.push(0);
	var tableCellIndex = 1;
	if (res.tableCellIndex !== undefined)
	{
		tableCellIndex = res.tableCellIndex;
	}

	res.data.forEach(function (item, index)
	{
		var tr = $(".layui-table-main").find("tbody tr[data-index='" + index + "']");
		for (var j = 0; j < ii; j++)
		{
			var len = tableCellWidth(tr, j, tableCellIndex);
			if (len > thArr[j])
				thArr[j] = len;
		}
	});
	var cellSty = '';
	var tW = 0;
	for (let i = 0; i < ii - 1; i++)
	{
		var w = thArr[i] * column[i];
		if (minWidth[i] > w)
			w = minWidth[i];
		cellSty += '.laytable-cell-' + tableCellIndex + '-0-' + i + '{width:' + w + 'px;}';
		tW += w;
	}
	var iii = ii - 1;
	var w1 = thArr[iii] * column[iii];
	if (minWidth[iii] > w1)
		w1 = minWidth[iii];
	tW += w1;

	var oldW = top.innerWidth - 223;
	if (tW < oldW)
	{
		w1 = w1 + oldW - tW;
	}

	cellSty += '.laytable-cell-' + tableCellIndex + '-0-' + iii + '{width:' + w1 + 'px;}';
	$('div[lay-filter="LAY-table-' + tableCellIndex + '"]').find('style').html(cellSty);
}
var re = /[\u4E00-\u9FA5]/g; //测试中文字符的正则
//计算列宽度，根据字符串长度计算
function tableCellWidth(tr, index, tableCellIndex)
{
	var obj = tr.find('.laytable-cell-' + tableCellIndex + '-0-' + index).text();
	var len = 0;
	if (re.test(obj))
	{
		len = obj.length - obj.match(re).length + obj.match(re).length * 2;
	} else
	{
		len = obj.length;
	}
	return len;
}

function multiSelect(elemId, isShow,bindData,callback)
{
	return xmSelect.render({
		el: '#' + elemId,
		toolbar: {
			ALL: lang("全选1"),
			CLEAR: lang("清空1"),
			show: isShow,
			showIcon: isShow
		},
		prop: {
			name: 'Name',
			value: 'Value'
		},
		tips: lang('请选择筛选条件'),
		data: bindData,
		on: function (data)
		{
			//arr:  当前多选已选中的数据
			var arr = data.arr;
			//change, 此次选择变化的数据,数组
			var change = data.change;
			//isAdd, 此次操作是新增还是删除
			var isAdd = data.isAdd;
			if (!!callback)
				callback(arr)
		},
		model: {
			label: {
				type: 'block',
				block: {
					//最大显示数量, 0:不限制
					showCount: 2,
					//是否显示删除图标
					showIcon: true,
				}
			}
		}
	})
}
