﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>权限页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style type="text/css">

		.mytable tbody tr:hover {
			background-color: #F0F0F0 !important;
			color: #666 !important;
		}

		.transfer-list {
			display: flex;
			padding: 0;
		}

			.transfer-list span {
				width: 100%;
				text-align: center;
				display: inline-block;
				cursor: pointer;
			}

				.transfer-list span:hover {
					background-color: rgb(26, 188, 156) !important;
					color: white !important;
				}

				.transfer-list span:first-child {
					border-right: 1px solid #eee;
				}

		.selectRole {
			background-color: rgb(26, 188, 156) !important;
			color: white !important;
		}
		/* 设置下拉框的高度与表格单元相同 */
		td .xm-select-demo,
		td .layui-form-select {
			margin-top: -10px;
			margin-left: -15px;
			margin-right: -15px;
		}

		.layui-form-select dl dd {
			color: #000;
		}

		.laytable-cell-checkbox, .laytable-cell-numbers, .laytable-cell-radio, .laytable-cell-space {
			padding-left: 10px;
		}

        #unRegister-search {
            display: flex;
            padding: 7px 10px;
            justify-content: space-between;
        }

            #unRegister-search .layui-form-select {
                flex: 1 auto;
            }

		#list-container {
			height: calc(100% - 90px);
			overflow: auto;
			position: relative;
		}

			#list-container .table-body {
				overflow: hidden; /* 隐藏滚动条 */
				max-height: 100%; /* 设置最大高度 */
			}

			#list-container table {
				width: 100%;
				position: absolute;
				top: 0;
				left: 0;
			}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/xm-select.js"></script>
	<script type="text/javascript" src="js/request.js"></script>

	<script type="text/javascript">
		function onResize() {
			const windowInfo = {
				width: window.innerWidth,
				height: window.innerHeight
			}

			$('.layui-transfer-box').each(function (index, item) {
				$(item).css('height', windowInfo.height - 80);
			});
			//$('.layui-transfer-box:eq(0)').css('width', (windowInfo.width - 430) / 2);
			$('.layui-transfer-box:eq(2)').css('width', (windowInfo.width - 430) / 2);
			$('.layui-transfer-box:eq(3)').css('width', (windowInfo.width - 430) / 2);

			var parentHeight = $('.layui-transfer-box')[0].offsetHeight;
			$('#UnRegister-container').css('height', parentHeight - 92);
			$('#register-container').css('height', parentHeight - 92);
			//ReloadAlarmTable(table.cache.alarmlist);
			tableHeight = parentHeight - 92;
			renderTable('#unregistertoollist');
			renderTable('#registertoollist');
			table.reload('registertoollist', {
				data: register_listData,
				cols: [toolColumnsName('#registertoollist')],
			});
			table.reload('unregistertoollist', {
				data: UnRegister_listData,
				cols: [toolColumnsName('#unregistertoollist')]
			});
		}

		window.addEventListener('resize', onResize);

		function toolColumnsName(id) {
			var columnsArray = [
				{ type: 'checkbox', width: 40, event: 'singleclick', align: 'center' },
				{ field: 'Name', minWidth: 120, title: '名称', align: 'center' },
				{
					field: 'Data', title: '权限', width: 90, templet: function (d) {
						if (d.Data.indexOf(CS.VideoMenu.$Work) >= 0)
							return "完全操作";

						if (d.Data.indexOf(CS.VideoMenu.$Part) >= 0)
							return "部分操作";

						return "查看";
					}
				},
				/*{ field: '', title: '性别', minWidth: 160, templet: '#mutliSelect' },*/
			];

			if (id === '#registertoollist') {
				columnsArray.push({
					width: 80, title: lang("操作"), toolbar: "#unregisterOp", event: 'singleclick', align: 'center', fixed: 'right'
				})
			}

			return columnsArray;
		}

		var UnRegister_listData = [];
		var unregisterChecked = [];
		var UnRegister_searchResult = [];
		var register_listData = [];
		var registerChecked = [];
		var register_searchResult = [];
		var currentElement = undefined;
		var editDialog = undefined;
		var listTypeName = '全部角色';

		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel'], function () {
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, excel = layui.excel;
			lang.call(this, $);
			column = toolColumnsName();

			FillRoleDataTable();

			renderTable('#unregistertoollist');
			table.on('checkbox(unregistertoolTable)', function (obj) {
				if (obj.checked) {
					if (obj.type == 'all') {
						unregisterChecked = table.cache.unregistertoollist;
					} else {
						unregisterChecked.push(obj.data);
					}
				} else {
					if (obj.type == 'all') {
						unregisterChecked = [];
					} else {
						var i = 0;
						$.each(unregisterChecked, function (key, value) {
							if (value.id == obj.data.id) {
								unregisterChecked.splice(i, 1);
								return false;
							}
							i++;
						})
					}
				}
				if (unregisterChecked.length > 0) {
					$('#registerbtn').removeClass('layui-btn-disabled')
				} else {
					$('#registerbtn').addClass('layui-btn-disabled')
				}
			});
			renderTable('#registertoollist');
			table.on('tool(registertoolTable)', function (obj) {
				var data = obj.data;

				if (obj.event === 'edit') {
					editDialog = layer.open({
						title: lang('修改权限')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#editForm')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function () {
							var vs = {};
							vs.ad = data.Data;
							if (vs.ad.indexOf(CS.VideoMenu.$Work) >= 0)
								vs.op = CS.VideoMenu.$View + ',' + CS.VideoMenu.$Part + ',' + CS.VideoMenu.$Work;
							else if (vs.ad.indexOf(CS.VideoMenu.$Part) >= 0)
								vs.op = CS.VideoMenu.$View + ',' + CS.VideoMenu.$Part;
							else
								vs.op = CS.VideoMenu.$View;
							for (var i in CS.VideoMenu) {
								if (!isNaN(CS.VideoMenu[i]))
									vs['equip[' + i + ']'] = vs.ad.indexOf(CS.VideoMenu['$' + i]) >= 0;
							}
							form.val('form-edit', vs);
						}
						, btn1: function (value, index) {
							AjaxApi.request({
								url: "/Auth/EditAuthOp.do",
								data: { id: data.ID, value: getAuthData(form.val('form-edit')) },
								dataType: 'text',
								success: function (result) {
									layer.close(editDialog);
									FillUnregiterToolAndRegisterTool(currentElement.id);
								}
							});
						}
					});
				}
			});
			table.on('checkbox(registertoolTable)', function (obj) {
				if (obj.checked) {
					if (obj.type == 'all') {
						registerChecked = table.cache.registertoollist;
					} else {
						registerChecked.push(obj.data);
					}
				} else {
					if (obj.type == 'all') {
						registerChecked = [];
					} else {
						var i = 0;
						$.each(registerChecked, function (key, value) {
							if (value.id == obj.data.id) {
								registerChecked.splice(i, 1);
								return false;
							}
							i++;
						})
					}
				}
				if (registerChecked.length > 0) {
					$('#unregisterbtn').removeClass('layui-btn-disabled')
				} else {
					$('#unregisterbtn').addClass('layui-btn-disabled')
				}
			});

			onResize();
			getAllAreaAndGroup();
			form.on("select(unRegister-area)", function (data) {
				$("#unRegister-group").val('');
				form.render('select');
				addSelectIcon();
				onUnRegisterToolSearch(data.value, 'area');
			});
			form.on("select(unRegister-group)", function (data) {
				$("#unRegister-area").val('');
				form.render('select');
				addSelectIcon();
				onUnRegisterToolSearch(data.value, 'group');
			});
			form.on("select(register-area)", function (data) {
				$("#register-group").val('');
				form.render('select');
				addSelectIcon();
				onRegisterToolSearch(data.value, 'area');
			});
			form.on("select(register-group)", function (data) {
				$("#register-area").val('');
				form.render('select');
				addSelectIcon();
				onRegisterToolSearch(data.value, 'group');
			});
		});

		$(document).on('ready', function () {
			if (!!window.RCS && !!window.RCS.Plugs && window.RCS.Plugs.Realm == 'LCD')
				$('.name').text('Unit')

			$('.transfer-list span').eq(0).addClass('selectRole');
			$('.transfer-list span').on('click', function () {
				$('.transfer-list span').removeClass('selectRole');
				$(this).addClass('selectRole');

				listData = [];
				searchResult = []; // 筛选左侧列表集合
				clickListId = '';
				clearAllStauts();
				table.reload('registertoollist', {
					data: [],
					cols: [toolColumnsName('#registertoollist')],
					page: {
						curr: 1
					}
				});
				table.reload('unregistertoollist', {
					data: [],
					cols: [toolColumnsName('#unregistertoollist')],
					page: {
						curr: 1
					}
				});
				listTypeName = $(this).text();
				if (listTypeName == '全部角色') {
					FillRoleDataTable();
				} else {
					FillUserDataTable();
				}

			})
		});
		var tableHeight = 0;
		function renderTable(id) {
			table.render({
				elem: id
				, even: true
				, loading: false
				, defaultToolbar: []
				, page: {
					limits: [20, 30, 50],
					limit: 20,
				}
				, height: tableHeight
				, cols: [toolColumnsName(id)]
				, data: []
			});
		}

		function FillRoleDataTable() {
			AjaxApi.request({
				url: "/Role/List.do",
				data: {},
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);
					// RenderRoleTable(res.data, res.data, 'role');
					listType = 'role';
					setField(res.data, 'role');
					clearAllStauts();
				}
			})
		}
		function FillUserDataTable() {
			AjaxApi.request({
				url: "/User/GetUserListAll.do",
				data: { pageCount: 0, limitCount: 9999 },
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);
					// RenderRoleTable(res.data, res.data, 'user');
					listType = 'user';
					setField(res.data);
					clearAllStauts();
				}
			})
		}

		// RAF优化动画效果
		var windowRequestAnimationFrame =
			window.requestAnimationFrame ||
			window.webkitRequestAnimationFrame ||
			window.mozRequestAnimationFrame ||
			window.oRequestAnimationFrame ||
			window.msRequestAnimationFrame ||
			function (callback) {
				return setTimeout(callback, 1000 / 60);
			}; //时间刻度自行设置
		var windowCancelAnimationFrame = window.cancelAnimationFrame || function (name) { clearTimeout(name) };
		// 虚拟滚动列表
		var listData = [];
		var searchResult = []; // 筛选左侧列表集合
		var clickListId = '';
		var listType = 'role';

		var container;
		var itemHeight = 38;
		var visibleItemCount = 0;
		var lastStartIndex = 0;
		var lastEndIndex = 0;
		var scrollRAF;
		function setField(data) {
			listData = data;
			container = document.getElementById("list-container");
			visibleItemCount = Math.ceil(container.clientHeight / itemHeight) + 2;
			lastEndIndex = lastStartIndex + visibleItemCount;
			container.addEventListener("scroll", function () {
				if (scrollRAF) {
					windowCancelAnimationFrame(scrollRAF);
				}
				scrollRAF = windowRequestAnimationFrame(updateVisibleItems);
			});
			getWrapperScrollHeight(listData);
			RenderRoleTable(listData.slice(lastStartIndex, lastEndIndex));
		}
		function getWrapperScrollHeight(data) {
			var wrapper = document.getElementById("list-wrapper");
			wrapper.style.height = data.length * 38 + "px";
		}
		// 获取可视范围内的数据
		function updateVisibleItems() {
			var scrollTop = container.scrollTop;
			var startIndex = Math.floor(scrollTop / itemHeight);
			var endIndex = Math.min(startIndex + visibleItemCount, listData.length);
			var wrapper = document.getElementsByClassName("layui-table")[0];
			lastStartIndex = startIndex;
			lastEndIndex = endIndex;

			if (searchResult.length > 0) {
				RenderRoleTable(searchResult.slice(startIndex, endIndex));
			} else {
				RenderRoleTable(listData.slice(startIndex, endIndex));
			}
			wrapper.style.top = startIndex * itemHeight + "px";
		}

		function RenderRoleTable(showdata) {
			$('#rolelist').empty();
			$('#rolelist').off('click', 'tr');
			var tb = '<tbody>';

			for (var i = 0; i < showdata.length; i++) {
				var tr = '';
				if (i % 2 == 1) {
					tr = '<tr id="' + showdata[i].ID + '" style="background-color:#F8F8F8;" data-index="' + i + '">';
				}
				else {
					tr = '<tr id="' + showdata[i].ID + '" data-index="' + i + '">';
				}

				tr += '<td style="border: 0px;" data-field="Name" data-key="1-0-0" align="center" lay-event="singleclick">';

				if (listType == 'role') {
					tr += '<div>' + showdata[i].Name.substring(5, showdata[i].Name.length) + '</div>';
				} else {
					tr += '<div>' + showdata[i].Name + '</div>';
				}

				tr += '</td>'
				tr += '</tr>';
				tb += tr;
			}

			tb += '</tbody>';
			$('#rolelist').append(tb);
			if (!!clickListId) { //选中的li重新加上背景色
				$('#' + clickListId).addClass('selectRole')
			}
			// $('#rolelist').data('data', tabledata);
			$('#rolelist').on('click', 'tr', function () {
				roleSelected(this);
			});
		}
		// 选择角色
		function clearAllStauts() {
			UnRegister_selectData = []; // 清空未注册机台选中集合
			unregisterChecked = []; // 清空勾选集合
			unregister_searchResult = [];
			$("#UnRegister-search").val("");
			register_selectData = []; // 清空已注册机台选中集合
			registerChecked = []; // 清空勾选集合
			register_searchResult = [];
			$("#register-search").val("");

			$('#unregisterbtn').addClass('layui-btn-disabled');
			$('#registerbtn').addClass('layui-btn-disabled');
			$('#checkAllUnRegister').removeClass('layui-form-checked');
			$('#checkAllRegister').removeClass('layui-form-checked');

			$("#unRegister-group").val('');
			$("#unRegister-area").val('');
			$("#register-group").val('');
			$("#register-area").val('');
			form.render('select');
			addSelectIcon();
		}
		function roleSelected(elem) {
			currentElement = elem;
			clearAllStauts();

			$('#rolelist tr').each(function (index, item) {
				if (index % 2 == 1) {
					$('#' + item.id).css('background-color', '#F8F8F8');
				}
				else {
					$('#' + item.id).css('background-color', 'white');
				}

				$('#' + item.id).css('color', '#666');
				$('#' + item.id).removeClass('selectRole');
			})

			$('#' + elem.id).addClass('selectRole');
			clickListId = elem.id
			FillUnregiterToolAndRegisterTool(elem.id);
		}

		function onRoleSearch(elem) {
			searchResult = [];
			var table_data = listData;
			if (table_data == null)
				return;

			var search = elem.value;

			for (var i = 0; i < table_data.length; i++) {
				if (table_data[i].Name.toLowerCase().indexOf(search.toLowerCase()) >= 0) {
					searchResult.push(table_data[i]);
				}
			}
			container.scrollTop = 0; // 重置滚轮高度
			getWrapperScrollHeight(searchResult);
			RenderRoleTable(searchResult);
		}

		// 获取注册、未注册机台数据
		function FillUnregiterToolAndRegisterTool(id) {
			var ajax = {};
			ajax.dataType = 'text';
			ajax.data = { pagecount: 0, limitCount: 9999, toolnameLike: '' };
			if (!window.RCS || !window.RCS.Plugs || window.RCS.Plugs.Realm != 'LCD')
				ajax.url = '/Equipment/GetAllEquipment.do';
			else
				ajax.url = '/Unit/GetUnit.do';
			window.currentRole = id;
			ajax.success = function (result) {
				var res = $.eval(result);
				if (res.code != "Success")
					return;

				var ajax = {};
				ajax.dataType = 'text';
				if (listTypeName == '全部角色') {
					ajax.data = { role: window.currentRole };
				} else {
					ajax.data = { user: window.currentRole };
				}
				// ajax.data = { id: window.currentRole };
				ajax.url = '/Auth/GetAllRegisterTool.do';
				window.currentAll = res.data;
				ajax.success = function (result) {
					var res = $.eval(result);
					var key = 'Video';
					if (!!window.RCS && !!window.RCS.Plugs && window.RCS.Plugs.Realm == 'LCD')
						key = 'Unit';
					register_listData = res.data
					for (var i = register_listData.length - 1; i >= 0; i--)
						register_listData[i].Target = register_listData[i][key];
					table.reload('registertoollist', {
						data: res.data,
						cols: [toolColumnsName('#registertoollist')],
						page: {
							curr: 1
						}
					});
					var unregisterTool_data = window.currentAll.filter(function (item, index, arr) {
						for (var i = 0; i < register_listData.length; i++) {
							if (item.ID == register_listData[i].Target)
								return false;

							if (item.Parent.length > 0)
								return false;
						}

						return true;
					});
					table.reload('unregistertoollist', {
						data: unregisterTool_data,
						cols: [toolColumnsName('#unregistertoollist')],
						page: {
							curr: 1
						}
					});
					UnRegister_listData = unregisterTool_data;
				};
				AjaxApi.request(ajax);
			};
			AjaxApi.request(ajax);
		}

		function getAuthData(info) {
			var vs = ',';
			for (var k in CS.VideoMenu) {
				var i = CS.VideoMenu[k];
				if (!isNaN(i) && info['equip[' + k + ']'] == 'on')
					vs = vs + k + ',';
			}
			vs[0] = info.op;
			if (info.op == CS.VideoMenu.$Work)
				vs = vs + CS.VideoMenu.$Work + ',';
			else if (info.op == CS.VideoMenu.$Part)
				vs = vs + CS.VideoMenu.$Part + ',';
			else
				vs = vs + CS.VideoMenu.$View + ',';
			return vs;
		}
		// 未注册机台搜索
		function onUnRegisterToolSearch(value, typeName) {
			UnRegister_searchResult = []
			var table_data = UnRegister_listData;
			if (table_data == null)
				return;

			for (var i = 0; i < table_data.length; i++) {
				if (typeName == 'area' && table_data[i].Area == value) {
					UnRegister_searchResult.push(table_data[i]);
				}
				if (typeName == 'group' && table_data[i].Unit == value) {
					UnRegister_searchResult.push(table_data[i]);
				}
			}

			table.reload('unregistertoollist', {
				data: value == '' ? table_data : UnRegister_searchResult,
				cols: [toolColumnsName('#unregistertoollist')],
				page: {
					curr: 1
				}
			});
		}
		// 未注册》》》注册
		function OnRegisterTool() {
			if (UnRegister_listData.length == 0) {
				layer.msg(lang('无数据'), { icon: 1 });
				return;
			}

			if (!unregisterChecked.length) {
				if (!window.RCS || !window.RCS.Plugs || window.RCS.Plugs.Realm != 'LCD')
					layer.msg(lang('请选择机台'), { icon: 1 });
				else
					layer.msg(lang('请选择Unit'), { icon: 1 });
				return;
			}

			editDialog = layer.open({
				title: lang('权限设置')
				, type: 1
				, resize: false
				, id: 'edit'
				, content: $('#editForm')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
				, success: function () {
					var vs = {};
					vs.op = 'v';
					for (var i in CS.VideoMenu)
						vs['equip[' + i + ']'] = false;
					form.val('form-edit', vs);
				}
				, btn1: function (value, index) {
					var ajax = {};
					ajax.us = UnRegister_listData
					ajax.url = '/Auth/ReigsterTool.do';
					if (listTypeName == '全部角色') {
						ajax.data = { ids: '', names: '', data: getAuthData(form.val('form-edit')), role: $('#rolelist .selectRole').attr('id') };
					} else {
						ajax.data = { ids: '', names: '', data: getAuthData(form.val('form-edit')), user: $('#rolelist .selectRole').attr('id') };
					}

					for (var i = 0; i < unregisterChecked.length; i++) {
						var ii = ajax.us.findIndex(function (item) { return item.ID == unregisterChecked[i].ID });
						var uv = ajax.us[ii];
						ajax.data.ids += ',' + uv.ID;
						ajax.data.names += ',' + uv.Name;
						register_listData.push(uv)
						ajax.us.splice(ii, 1)
					}
					ajax.dataType = 'text';
					ajax.success = function (result) {
						var res = $.eval(result);
						layer.close(editDialog);
						if (res.code != "Success") {
							layer.msg(res.msg, { icon: 2 });
							return;
						}

						$('#registerbtn').addClass('layui-btn-disabled');
						unregisterChecked = []; // 清空勾选集合
						registerChecked = []; // 清空勾选集合
						UnRegister_searchResult = []; // 清空筛选集合
						$('#UnRegister-search').val('');
						FillUnregiterToolAndRegisterTool(currentElement.id);
						if (!!window.top.FillFavTreeTable)
							window.top.FillFavTreeTable();
					};
					AjaxApi.request(ajax);
				}
			});

		}

		// 已注册机台搜索
		function onRegisterToolSearch(value, typeName) {
			register_searchResult = []
			var table_data = register_listData;;
			if (table_data == null)
				return;

			for (var i = 0; i < table_data.length; i++) {
				if (typeName == 'area' && table_data[i].Area == value) {
					register_searchResult.push(table_data[i]);
				}
				if (typeName == 'group' && table_data[i].Unit == value) {
					register_searchResult.push(table_data[i]);
				}
			}
			table.reload('registertoollist', {
				data: value == '' ? table_data : register_searchResult,
				cols: [toolColumnsName('#registertoollist')],
				page: {
					curr: 1
				}
			});
		}
		// 已注册》》》未注册
		function OnUnRegisterTool() {
			$('#unregisterbtn').addClass('layui-btn-disabled');

			var unregister_data = { ids: '' };
			var table_data = register_listData;
			if (table_data.length == 0)
				return;

			for (var i = 0; i < registerChecked.length; i++) {
				var infoIndex = table_data.findIndex(function (item) { return item.ID == registerChecked[i].ID });
				unregister_data.ids += ',' + table_data[infoIndex].ID;
				if (listTypeName == '全部角色') {
					unregister_data.role = $('#rolelist .selectRole').attr('id');
				} else {
					unregister_data.user = $('#rolelist .selectRole').attr('id');
				}

				UnRegister_listData.push(table_data[infoIndex])
				register_listData.splice(infoIndex, 1)
			}

			var ajax = {};
			ajax.dataType = 'text';
			ajax.data = unregister_data;
			ajax.url = '/Auth/UnRegisterTool.do';
			ajax.success = function (result) {
				var res = $.eval(result);
				if (res.code == "Success") {
					unregisterChecked = []; // 清空勾选集合
					registerChecked = []; // 清空勾选集合
					register_searchResult = []; // 清空筛选集合
					$('#register-search').val('')
					FillUnregiterToolAndRegisterTool(currentElement.id);

					window.top.FillFavTreeTable();
					if (window.login.Role == '') {

					}
				}
			};
			AjaxApi.request(ajax);
		}
		// 获取所有的区域、机台组
		function getAllAreaAndGroup() {
			AjaxApi.request({
				url: "/Tool/GetAreaAndGroupListAll.do",
				data: {},
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);

					arealist = buildTree(res.arealist);
					toolgrouplist = buildTree(res.toolgrouplist);
					for (var i = 0; i < arealist.length; i++) {
						if (arealist[i].Parent) {
							arealist[i].icon =
								'<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
						} else {
							arealist[i].icon =
								'<i class="layui-icon layui-icon-layer"></i>';
						}
					}
					for (var i = 0; i < toolgrouplist.length; i++) {
						if (toolgrouplist[i].Parent) {
							toolgrouplist[i].icon =
								'<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
						} else {
							toolgrouplist[i].icon =
								'<i class="layui-icon layui-icon-layer"></i>';
						}
					}
					setAreaAndGroup();
				}
			})
		}
		// 设置区域、机台组
		function setAreaAndGroup() {
			var areaSelect = $('select[name="unRegister-area"]');
			var areaSelect1 = $('select[name="register-area"]');
			for (var i = 0; i < arealist.length; i++) {
				areaSelect.append(new Option(arealist[i].Name, arealist[i].ID));
				areaSelect1.append(new Option(arealist[i].Name, arealist[i].ID));
			}

			var groupSelect = $('select[name="unRegister-group"]');
			var groupSelect1 = $('select[name="register-group"]');
			for (var i = 0; i < toolgrouplist.length; i++) {
				groupSelect.append(new Option(toolgrouplist[i].Name, toolgrouplist[i].ID));
				groupSelect1.append(new Option(toolgrouplist[i].Name, toolgrouplist[i].ID));
			}
			form.render('select');
			addSelectIcon();
		}
		function addSelectIcon() {
			var areaSelect = $('select[name="unRegister-area"]');
			var groupSelect = $('select[name="unRegister-group"]');
			var areaSelect1 = $('select[name="register-area"]');
			var groupSelect1 = $('select[name="register-group"]');
			// 添加自定义图标
			var areaDl = areaSelect.next("div.layui-form-select").find("dl");
			areaDl.find("dd:not(.layui-select-tips)").each(function (index) {
				if (arealist[index]) {
					var icon = arealist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
			var groupDl = groupSelect.next("div.layui-form-select").find("dl");
			groupDl.find("dd:not(.layui-select-tips)").each(function (index) {
				if (toolgrouplist[index]) {
					var icon = toolgrouplist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
			var areaDl1 = areaSelect1.next("div.layui-form-select").find("dl");
			areaDl1.find("dd:not(.layui-select-tips)").each(function (index) {
				if (arealist[index]) {
					var icon = arealist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
			var groupDl1 = groupSelect1.next("div.layui-form-select").find("dl");
			groupDl1.find("dd:not(.layui-select-tips)").each(function (index) {
				if (toolgrouplist[index]) {
					var icon = toolgrouplist[index].icon;
					$(this).html(
						'<span class="option-icon">' +
						icon +
						'</span><span class="option-text" style="padding-left: 10px;">' +
						$(this).text() +
						"</span>"
					);
				}
			});
		}
		function buildTree(items)
		{
			var result = [];
			var map = {};
			var i, item;

			for (i = 0; i < items.length; i++)
			{
				item = items[i];
				map[item.ID] = item;
			}

			for (i = 0; i < items.length; i++)
			{
				item = items[i];
				if (item.Parent)
				{
					var parent = map[item.Parent];
					if (parent)
					{
						if (!parent.children)
						{
							parent.children = [];
						}
						parent.children.push(item);
					}
				} else
				{
					result.push(item);
				}
			}

			function flattenTree(arr)
			{
				var flatArray = [];

				function flatten(nodes)
				{
					var i;
					for (i = 0; i < nodes.length; i++)
					{
						flatArray.push(nodes[i]);
						if (nodes[i].children && nodes[i].children.length)
						{
							flatten(nodes[i].children);
							delete nodes[i].children;
						}
					}
				}

				flatten(arr);
				return flatArray;
			}

			return flattenTree(result);
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<div style="margin:10px;">
		<fieldset class="layui-elem-field layui-field-title">
			<legend><span>权限设置</span></legend>
		</fieldset>
		<div style="height: calc(100% - 66px); min-width: 1074px; min-height: 600px;">
			<div class="layui-form-item" style="display: flex;">
				<div class="layui-inline">
					<div class="layui-transfer-box" style="width: 258px !important; min-height: 600px;">
						<div class="layui-transfer-header transfer-list">
							<span>全部角色</span>
							<span>全部用户</span>
						</div>

						<div class="layui-transfer-search">
							<i class="layui-icon layui-icon-search"></i>
							<input type="text" class="layui-input" placeholder="请输入" oninput="onRoleSearch(this)">
						</div>

						<div id="list-container">
							<div id="list-wrapper">
								<table style="border: 0px;" class="layui-table" id="rolelist" lay-filter="rolelistevent" lay-skin="row"></table>
							</div>
						</div>
					</div>
				</div>

				<div class="layui-inline">
					<div class="layui-transfer-box Line" style="width: 3px; height: calc(100% - 60px); border-radius: 5px; border: none; min-height: 600px;"></div>
				</div>

				<div class="layui-inline">
					<div class="layui-transfer-box" style="min-width: 340px; min-height: 600px;">
						<div class="layui-transfer-header" style="padding-left: 15px;">
							<div id="checkAllUnRegister">
								<span>未注册<span class="name">机台</span></span>
							</div>
						</div>

						<form id="unRegister-search" class="layui-form">
							<select id="unRegister-area" name="unRegister-area" lay-filter="unRegister-area" lay-search="">
								<option value="">请选择区域</option>
							</select>
							<select id="unRegister-group" name="unRegister-group" lay-filter="unRegister-group" lay-search="">
								<option value="">请选择机台组</option>
							</select>
						</form>

						<div id="UnRegister-container">
							<table style="border: 0px;" id="unregistertoollist" lay-filter="unregistertoolTable"></table>
						</div>
					</div>

					<div class="layui-transfer-active">
						<button type="button" id="registerbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0" onclick="OnRegisterTool();">
							<i class="layui-icon layui-icon-next"></i>
						</button>
						<button type="button" id="unregisterbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1" onclick="OnUnRegisterTool();">
							<i class="layui-icon layui-icon-prev"></i>
						</button>
					</div>

					<div class="layui-transfer-box" style="min-width: 340px; min-height: 600px;">
						<div class="layui-transfer-header" style="padding-left: 15px;">
							<div id="checkAllRegister">
								<span>已注册<span class="name">机台</span></span>
							</div>
						</div>

						<form id="unRegister-search" class="layui-form">
							<select id="register-area" name="register-area" lay-filter="register-area" lay-search="">
								<option value="">请选择区域</option>
							</select>
							<select id="register-group" name="register-group" lay-filter="register-group" lay-search="">
								<option value="">请选择机台组</option>
							</select>
						</form>

						<div id="register-container">
							<div id="register-wrapper">
								<table style="border: 0px;" id="registertoollist" lay-filter="registertoolTable"></table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<script type="text/html" id="unregisterOp">
				<a class="layui-btn layui-btn-xs" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			</script>
		</div>
		<form id="editForm" class="layui-form" lay-filter="form-edit" style="display: none; padding-top: 20px; padding-right: 20px;">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;">操作权限</label>
				</div>
				<div class="layui-inline">
					<input type="radio" name="op" value="View" title="查看" checked="checked" />
					<input type="radio" name="op" value="Part" title="部分操作" />
					<input type="radio" name="op" value="Work" title="完全操作" />
				</div>
			</div>

			<div class="layui-form-item god-a-v">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;"><span class="name">机台</span>权限</label>
				</div>
				<div class="layui-inline">
					<div style="display: inline;" class="god-a-vm"><input type="checkbox" name="equip[Chief]" lay-skin="primary" title="管理员" /></div>
					<div style="display: inline;"><input type="checkbox" name="equip[Oust]" lay-skin="primary" title="中断操作" /></div>
					<div style="display: inline;"><input type="checkbox" name="equip[SimpleLock]" lay-skin="primary" title="普通锁定" /></div>
					<div style="display: inline;" class="god-a-vh"><input type="checkbox" name="equip[StrictLock]" lay-skin="primary" title="严格锁定" /></div>
				</div>
			</div>

			<div class="layui-form-item god-a-t">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;">&nbsp;</label>
				</div>
				<div class="layui-inline">
					<input type="checkbox" name="equip[Edit]" lay-skin="primary" title="编辑" />
					<input type="checkbox" name="equip[Snapshot]" lay-skin="primary" title="截图" />
					<input type="checkbox" name="equip[Record]" lay-skin="primary" title="录屏" />
				</div>
			</div>

			<div class="layui-form-item god-a-s">
				<div class="layui-inline">
					<label class="layui-form-label" style="width:119px;">RPA脚本</label>
				</div>
				<div class="layui-inline">
					<div style="display: inline;"><input type="checkbox" name="equip[RunScript]" lay-skin="primary" title="运行" /></div>
					<div style="display: inline;" class="god-a-sp"><input type="checkbox" name="equip[PauseScript]" lay-skin="primary" title="暂停" /></div>
					<div style="display: inline;"><input type="checkbox" name="equip[StopScript]" lay-skin="primary" title="停止" /></div>
					<div style="display: inline;" class="god-a-sc"><input type="checkbox" name="equip[SetScript]" lay-skin="primary" title="变更" /></div>
				</div>
			</div>
		</form>
	</div>
</body>
</html>
