﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
	<script type="python">
print '<!-- 0000000 -->'
import datetime
from System import DateTime
from System import Environment
from PickGold import Common
v = 'blue'
c = 'red'
def foo():
	import datetime
	v = 'green'
	print datetime.datetime.now(), '<br/>'
	return 'bbb'
	</script>
    <title><%= Common.AssemblyTitle %></title>
	<script type="python" src="common.py"></script>
</head>
<body>
	a() = <%#test
a = 100
b = 50
d = foo()
print d, b / a %><br/>
	v = <span style="color: <%= c %>;"><%= v, datetime.datetime.now() %></span><br/>
	DateTime.Now = <span style="color: <%= v %>;"><%= DateTime.Now, Environment.UserName %></span><br/>
	<%--
	document end
	--%>
<br/>
Version: <%= Common.ProductVersion %><br/>
123
</body>
</html>
