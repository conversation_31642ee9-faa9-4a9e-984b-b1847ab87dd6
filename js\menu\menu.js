﻿var RCS_Menu = [
    {
        id: "ToolManagement",
        name: "机台管理",
        icon: "layui-icon-util",
        type: "group", // group表示有子菜单，item表示单独菜单项
        children: [
            {
                id: "AreaSetup",
                name: "区域设置",
                icon: "layui-icon-transfer",
                url: "Area.html",
                operations: ["AreaOptionSet"]
            },
            {
                id: "ToolSetup",
                name: "机台设置",
                icon: "layui-icon-template-1",
                url: "Tool.html",
                operations: ["ToolOptionSet"]
            },
            {
                id: "EquipmentSetup",
                name: "Device设置",
                icon: "layui-icon-component",
                url: "Equipment.html",
                operations: ["EquipmentSet"],
                className: "stic" // 特殊样式类
            },
           /* {
                id: "ToolGroupSetup",
                name: "机台组设置",
                icon: "layui-icon-template-1",
                url: "ToolGroup.html",
                operations: ["ToolGroupOptionSet"]
            },
            {
                id: "ToolMachineSetup",
                name: "Device设置-1",
                icon: "layui-icon-component",
                url: "ToolMachine.html",
                operations: ["ToolMachineSet"],
                className: "bmot"
            },
            {
                id: "LayoutSetup",
                name: "Layout设置",
                icon: "layui-icon-layouts",
                url: "LayoutList.html",
                operations: ["LayoutSet"]
            }*/
        ]
    },
    {
        id: "AlarmSetup",
        name: "警报设置",
        icon: "layui-icon-tips",
        url: "AlarmManager.html",
        operations: ["AlarmOptionSet"],
        type: "item"
    },
    {
        id: "LogManagement",
        name: "日志管理",
        icon: "layui-icon-chart-screen",
        type: "group",
        permission: "LogManagement",
        children: [
            {
                id: "ToolLog",
                name: "机台日志",
                icon: "layui-icon-log",
                url: "LogEquip.html",
                permission: "ToolLog"
            },
            {
                id: "SystemLog",
                name: "系统日志",
                icon: "layui-icon-log",
                url: "LogSystem.html",
                permission: "SystemLog"
            }
        ]
    },
    {
        id: "SummarySearch",
        name: "汇总查询",
        icon: "layui-icon-template-1",
        url: "Summary.html",
        type: "item"
    },
    {
        id: "SystemSetup",
        name: "系统管理",
        icon: "layui-icon-group",
        type: "group",
        children: [
            {
                id: "UserSetup",
                name: "用户设置",
                icon: "layui-icon-username",
                url: "User.html",
                operations: ["UserOptionSet"]
            },
            {
                id: "RoleSetup",
                name: "角色设置",
                icon: "layui-icon-user",
                url: "Role.html",
                operations: ["RoleOptionSetup"]
            },
            {
                id: "UserGroupSetup",
                name: "用户组设置",
                icon: "layui-icon-friends",
                url: "UserGroup.html",
                operations: ["UserGroupSet"]
            },
            {
                id: "AuthSetup",
                name: "权限设置",
                icon: "layui-icon-auz",
                url: "Auth.client.html",
            },
            {
                id: "ParamSetup",
                name: "参数设置",
                icon: "layui-icon-set",
                url: "Parameter.html",
            }
        ]
    },
    {
        id: "NoticeSetup",
        name: "公告设置",
        icon: "layui-icon-note",
        action: "onNoticeShow()", // 特殊处理函数
        special: true
    }
]

var RCS_ShowTable = true;

var menuModule = {
    loadMenu: function ()
    {
        var menuData = this.loadMenuJson();
        return menuData || RCS_Menu;
    },

    saveMnenu: function (menuData)
    {
        saveMenuJson(menuData);
    },

    loadMenuJson: function ()
    {
        try
        {
            /*var cachedMenu = localStorage.getItem('RCS_Menu_curr');
            if (cachedMenu)
            {
                return JSON.parse(cachedMenu);
            }*/

            return AjaxApi.request({
                url: "./js/menu/menu.json",
                type: 'GET',
                success: function (response)
                {
                    var result = typeof response === 'string' ? JSON.parse(response) : response;
                    var menuData = result.RCS_Menu_curr;
                    if (!menuData || menuData.length === 0)
                    {
                        menuData = result.RCS_Menu_default;
                    }
                    // localStorage.setItem('RCS_Menu_curr', JSON.stringify(menuData));
                    return menuData;
                }
            });
        } catch (e)
        {
            console.error("加载菜单配置失败:", e);
            return null;
        }
    },

    saveMenuJson: function (menuData)
    {
        try
        {
            // localStorage.setItem('RCS_Menu_curr', JSON.stringify(menuData));

            return AjaxApi.request({
                url: "/Menu/SaveMenu.do",
                type: 'POST',
                data: JSON.stringify({
                    RCS_Menu_curr: menuData
                }),
                contentType: 'application/json',
                success: function (result)
                {
                    console.log('菜单配置保存成功');
                }
            });
        } catch (e)
        {
            console.error("保存菜单配置失败:", e);
            return false;
        }
    },
}

window.MenuModule = menuModule;