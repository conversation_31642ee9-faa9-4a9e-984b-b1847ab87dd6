﻿/*!
 * matrix.js v24.8.13
 *
 * Copyright 2028 Cowin
 * Author: <PERSON>
 * Editor: <PERSON>
 *
 * Date: 2024-08-13
 */

if (!window.RCS)
{

}
else
{
	if (!!window.RCS.ConfigData && !!window.RCS.ConfigData.Robot)
		window.Robot = window.RCS.ConfigData.Robot;
}

window.editSweep = function (id, video, value)
{
	var ajax = {};
	ajax.url = '../Video.do?ID=' + video + '&Sweep=' + id + '&Value=' + value + '&_=' + Date.now();
	ajax.dataType = 'text';
	ajax.success = function (result)
	{
		if (result.indexOf('Success') > 0 || result.indexOf('Sweep://') >= 0)
			setTimeout(window.loadSweep, 1000);
	};
	ajax.error = function (q)
	{
		layer.msg('Error:\r\n' + q.responseText, { icon: 2 });
	};
	$.ajax(ajax);
};
window.loadSweep = function ()
{
	var ajax = {};
	ajax.url = '../Video.do?User=0&Video=' + $('#div-worker').attr('name') + '&_=' + Date.now();
	ajax.dataType = 'text';
	ajax.success = function (result)
	{
		var video = $('#div-worker').data('video');
		var json = $.eval(result);
		console.log(result);
		for (var i = 0; i < json.length; i++)
		{
			if (json[i].ID == video.ID)
			{
				video = json[i];
				result = null;
				break;
			}
		}
		if (!!result)
		{
			$('#div-worker-0').html('<h1 style="text-align: center; margin-top: 100px;">No Record</h1>');
			return;
		}

		result = video.SweepData;
		if (!result)
		{
			$('#div-worker-0').html('<h1 style="text-align: center; margin-top: 100px;">No Data</h1>');
			return;
		}

		var index = result.indexOf('://');
		if (index <= 0 || result.indexOf('/', index + 3) < 0)
		{
			$('#div-worker-0').html('<h1 style="text-align: center; margin-top: 100px;">Invalid Data<br/>\r\n' + result + '</h1>');
			return;
		}

		video.AuthData = json[0].Data;
		if (json[0]['$:type'] != 'Auth')
			video.AuthData = null;
		index = result.indexOf('/', index + 3);
		result = result.substring(index + 1, result.length);
		video.Sweepers = result.split('&');
		if (!video.Sweepers || !video.Sweepers.length)
		{
			$('#div-worker-0').html('<h1 style="text-align: center; margin-top: 100px;">Empty Data</h1>');
			return;
		}

		$('#div-worker-0').empty();
		if (!window.doRPA)
			video.Robot = '';
		else
			video.Robot = window.doRPA();
		for (var i = video.Sweepers.length - 1; i >= 0; --i)
		{
			result = video.Sweepers[i];
			json = { Data: result };
			video.Sweepers[i] = json;
			index = result.indexOf('=');
			if (index > 0)
			{
				json.ID = result.substring(0, index);
				result = result.substring(index + 1, result.length);
			}
			json.Values = result.split('/');
			if (!json.Values || json.Values.length < 5)
				continue;

			json.Video = video.ID;
			json.Name = json.Values[json.Values.length - 1];
			json.Name = json.Name.substring(1, json.Name.length);
			json.Robot = video.Robot.indexOf(json.Name);
			json.Value = parseInt(json.Values[1]);
			json.Edit = parseInt(json.Values[0]);
			json.Time = new Date(json.Edit).toLocaleString();
			json.Alive = json.Values[2];
			json.Alive = json.Alive.substring(1, json.Alive.length);
			json.User = json.Values[4];
			json.User = json.User.substring(1, json.User.length);
			json.Auth = video.AuthData;
			json.Lock = video.LockData;
		}
		for (var i = video.Sweepers.length - 1; i >= 0; --i)
		{
			if (!video.Sweepers[i].Alive)
				video.Sweepers.splice(i, 1);
		}
		$('#div-worker-0').html('<div id="div-worker-0-0"></div>');
		json = {
			elem: '#div-worker-0-0',
			even: true,
			loading: false,
			sort: true,
			defaultToolbar: [],
			data: video.Sweepers,
		};
		json.cols = [[
			//{ title: lang("会话"), field: "Alive", align: 'center' },
			{ title: lang("用户"), field: "Name", align: 'center' },
			{
				title: lang("状态"), field: "Value", align: 'center', templet: function (d)
				{
					if (d.Value > window.CS.ExtraData.STATUS_WAITING)
						return '<img style="width: 16px; height: 16px;" src="imgs/Work20.png" alt="操作中"/> 操作中';

					if (d.Value == window.CS.ExtraData.STATUS_WAITING)
						return '<img style="width: 16px; height: 16px;" src="imgs/Work10.png" alt="排队中"/> 排队中';

					return '<img style="width: 16px; height: 16px;" src="imgs/Work-0.png" alt="观看中"/> 观看中';
				}
			},
			{ title: lang("时间"), field: "Time", align: 'center' }
		]];
		if (!window.godCSS || !window.godCSS('.god-a-vl'))
			json.cols[0].push({
				title: lang("操作"), templet: function (d)
				{
					if (d.Robot >= 0)
						return '-';

					var t = ' - <input class="layui-btn layui-btn-xs god-a-vl" type="button" value="驱逐" onclick="javascript: editSweep.call(this, \'' + d.ID + '\', \'' + d.Video + '\', 0);"/>';
					if (!!d.Lock && d.Value == window.CS.ExtraData.STATUS_WAITING)
					{
						if (d.Lock.indexOf('/L' + window.CS.ExtraData.STRICT_LOCK + '/') > 0)
						{
							if (d.Lock.indexOf(window.login.ID) >= 0)
								t = '<input class="layui-btn layui-btn-xs god-a-vl" type="button" value="授权" onclick="javascript: editSweep.call(this, \'' + d.ID + '\', \'' + d.Video + '\', 1);"/> - ' + t;
						}
						else if (d.Auth.indexOf(CS.VideoMenu.$SimpleLock) > 0)
						{
							t = '<input class="layui-btn layui-btn-xs god-a-vl" type="button" value="授权" onclick="javascript: editSweep.call(this, \'' + d.ID + '\', \'' + d.Video + '\', 1);"/> - ' + t;
						}
					}
					return t;
				}, align: 'center'
			});
		table.render(json);
		if (!!window.godCSS)
			window.godCSS.call();
	};
	ajax.error = function (q)
	{
		layer.msg('Error:\r\n' + q.responseText, { icon: 2 });
	};
	$.ajax(ajax);
};
window.doWork = function (id)
{
	var video = $(this).data('video');
	if (!document.getElementById('div-worker'))
		$(document.body).append('<div id="div-worker" style="display: none;"><div id="div-worker-0"></div></div>');
	$('#div-worker').attr('name', video.ID);
	$('#div-worker').data('video', video);
	layer.open({
		title: video.Name + ' 当前操作员列表'
		, type: 1
		, resize: false
		, area: ['60%', '80%']
		, content: $('#div-worker')
		, shadeClose: false
		, success: loadSweep
		, end: function ()
		{
			$('#div-worker').hide();
		}
	});
};
/*<div class="layui-btn-container" style="float:left;">
			<button class="layui-btn layui-btn-sm m_AuthManagerBC" lay-event="AddData" id="btnBC">{{lang('增加')}}</button>
		</div>
 * */