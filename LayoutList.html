﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>Layout 设置</title>
    <link rel="stylesheet" href="lib/layui/css/layui.css" />
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/layout-index.css" />
    <script type="text/javascript">
        window.webName = 'layout';
        if (window.top == window.self)
            window.location.href = 'index.html';
        else
            window.top.layout = window;
        if (!!window.parent)
            window.parent.layout = window;
    </script>
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script type="text/javascript" src="lib/layui/layui.js"></script>
    <script type="text/javascript" src="lib/pickgold.js"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
    <script type="text/javascript" src="js/layout/loading.js"></script>
    <script type="text/javascript" src="js/request.js"></script>
</head>
<body style="background-color: white; overflow-x: auto;">
    <form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; min-height: 600px; margin: 5px; ">
        <fieldset class="layui-elem-field">
            <legend><span>Layout 设置</span></legend>
            <div class="layui-field-box" style="text-align:center;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <img id="recipe_img" style="width:1px; height:1px;" />
                        <lable class="layui-form-label" style="width:85px;">layout名称：</lable>
                    </div>
                    <div class="layui-inline">
                        <input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入layout名称"
                               oninput="cleanSpelChar(this)">
                    </div>
                    <div class="layui-inline">
                        <button type="submit" class="layui-btn" lay-filter="layoutSearch" lay-submit=""><span>查询</span></button>
                    </div>
                    <div class="layui-inline">
                        <button type="button" class="layui-btn"><span>重置</span></button>
                    </div>
                </div>
            </div>
        </fieldset>
        <table class="layui-hide" id="layoutList" lay-filter="layoutTable"></table>
        <script type="text/html" id="layoutToolbar">
            <div class="layui-btn-container" style="float:left;">
                <div id="normaldiv" class="layui-inline">
                    <div id="divBC" class="layui-inline">
                        <button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
                        <button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
                    </div>
                </div>
            </div>
        </script>
        <script type="text/html" id="operation">
            <a class="layui-btn layui-btn-xs" name="eventVeiw" lay-event="view">{{lang('查看')}}</a>
            <a class="layui-btn layui-btn-xs" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
            <a class="layui-btn layui-btn-danger layui-btn-xs" name="eventDel" lay-event="del">{{lang('删除')}}</a>
        </script>
        <div id="footer" style="text-align:center;"></div>
    </form>
    <form id="addLayout" class="layui-form" lay-filter="form-addlayout" style="display: none; padding-top: 20px; padding-right: 20px;">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:80px;">名称</label>
            </div>
            <div class="layui-inline edit">
                <input type="text" class="layui-input" name="toolname" autocomplete="off" placeholder="请输入名称"
                       oninput="cleanSpelChar(this)">
            </div>
        </div>
    </form>
</body>
</html>
<script>
    var limitPageCount = 20;
    var pageCount = 1;
    var checkedItems = [];
    layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage'], function () {
        $ = layui.$
        , form = layui.form
        , table = layui.table
        , layer = layui.layer
        , laypage = layui.laypage;

        column = toolColumnsName();
        //创建Table的列名
        layoutTable = table.render({
            elem: '#layoutList'
            , even: true
            , sort: true
            , loading: false
			, toolbar: hasButtonPermission('LayoutSet') ? '#layoutToolbar' : []
            , defaultToolbar: []
            , id: "layoutList"
            , limit: limitPageCount
            , height: 'full-185'
            , page: false
            , cols: [column]
            , data: []
        });
        getTable();
        //头工具栏事件
        table.on('toolbar(layoutTable)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'isAdd':
                    AddList()
                    break;
                case 'BatchDelete':
                    BatchDelete();
                    break;
            };
        });
        //监听行工具事件
        table.on('tool(layoutTable)', function (obj) {
            var data = obj.data;

            if (obj.event === 'del') {
                Delete(data);
            }
            else if (obj.event === 'edit')
            {
                window.location.href = "/web/Layout.html?id=" + data.id + "&name=" + data.name + "&isEdit=1";
            }
            else if (obj.event === 'view') {
				window.location.href = "/web/Layout.html?id=" + data.id + "&name=" + data.name + "&isEdit=0"
            }
        });
        table.on('checkbox(layoutTable)', function (obj) {
            if (obj.checked) {
                if (obj.type == 'all') {
                    checkedItems = table.cache.layoutList;
                } else {
                    checkedItems.push(obj.data);
                }
            } else {
                if (obj.type == 'all') {
                    checkedItems = [];
                } else {
                    var i = 0;
                    $.each(checkedItems, function (key, value) {
                        if (value.id == obj.data.id) {
                            checkedItems.splice(i, 1);
                            return false;
                        }
                        i++;
                    })
                }
            }
        });
    });

    function toolColumnsName() {
        var columnsArray = [
            { title: lang("选中"), type: 'checkbox', event: 'singleclick', align: 'center' },
            {
                title: lang("序号"), width: 80, field: "xuhao", align: 'center', templet: function (e) {
                    return limitPageCount * (pageCount - 1) + e.LAY_INDEX
                }
            },
            { field: 'id', title: 'ID', align: 'center', sort: true },
            { field: 'name', title: '名称', align: 'center' },
            { field: 'city', title: '备注', align: 'center' },
            { field: 'city', title: '更新时间', align: 'center' },
            {
                width: 180, title: lang("操作"), toolbar: "#operation", event: 'singleclick',
				align: 'center', fixed: 'right', hide: !hasButtonPermission('LayoutSet')
            }
        ];

        return columnsArray;
    }

    function AddList() {
        var addDialog = layer.open({
            title: lang('增加')
            , type: 1
            , resize: false
            , id: "isAdd"
            , content: $('#addLayout')
            , btn: [lang('确认'), lang('取消')]
            , btnAlign: 'c'
            , success: function () {
               
            }
            , btn1: function (index) {
               
            }
            , end: function () {
                $('#addLayout')[0].reset();
                layer.close(addDialog);
            }
        });
    }

    function Delete(data) {
        layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
            /*  AjaxApi.request({
                 url: "/Equipment/DeleteEquipment.do",
                 data: { id: data.ID, type: data.Type, name: data.Name },
                 dataType: 'text',
                 success: function (result) {
                     var res = $.eval(result);
                     if (res.code == "Success") {
                         layer.msg(lang('删除成功'), { icon: 1 });
                         FillDataTable();
                     }
                     else {
                         layer.msg(lang('删除失败'), { icon: 2 });
                     }
                 },
                 error: function (r) {
                     layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
                 }
             })*/
        });
    }

    function BatchDelete() {
        if (checkedItems.length == 0) {
            layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
            return;
        }
        /* layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
             AjaxApi.request({
                 url: "/Equipment/DeleteEquipment.do",
                 data: { id: data.ID, type: data.Type, name: data.Name },
                 dataType: 'text',
                 success: function (result) {
                     var res = $.eval(result);
                     if (res.code == "Success") {
                         layer.msg(lang('删除成功'), { icon: 1 });
                         FillDataTable();
                     }
                     else {
                         layer.msg(lang('删除失败'), { icon: 2 });
                     }
                 },
                 error: function (r) {
                     layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
                 }
             })
         });*/
    }

    function getTable() {
        var data = [
            { id: 11111, name: 44444, sex: 333, city: 99999 }
        ];
        laypage.render({
            elem: 'footer'
            , count: data.length
            , theme: '#FF5722'
            , limit: limitPageCount
            , prev: lang('上一页')
            , next: lang('下一页')
            , layout: ['count', 'prev', 'page', 'next', 'skip']
            , jump: function (obj, first) {
                pageCount = obj.curr;
                if (first) {
                    //加载table数据
                    table.reload('layoutList', {
                        data: data,
                        cols: [column]
                    });
                }
                else {
                    setTable({ pageCount: pageCount - 1, limitCount: limitPageCount });
                }
                laypageEN();//翻页翻译成英文方法
            }
        });
    }

    function setTable(data) { }
</script>
