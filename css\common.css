﻿body {
	/*background-color: gray;*/
	margin: 0px;
	padding: 0px;
	overflow-x:hidden;
	/*margin-bottom:20px;*/
}

.myFont {
	font-size: 16px;
}

.layui-table-tool div[lay-event="LAYTABLE_COLS"],
.layui-table-tool div[lay-event="LAYTABLE_PRINT"],
button[lay-event=getCheckLength],
button[lay-event=isAll] {
	/*display: none;*/
}

.layui-layer-page .layui-layer-content {
	/*overflow: visible !important;*/
}

.layui-table-click {
	background-color: transparent;
}

.layui-table tbody tr:hover {
	background-color: rgb(26,188,156) !important;
	color: white !important;
}

.square {
	float: left;
	width: 20px;
	height: 20px;
	display: inline;
}

.square1 {
	background: #FF0000;
}

.square2 {
	background: #FFFF00;
}

.square3 {
	background: #C55A11;
}

.square4 {
	background: #CC00FF;
}

.square5 {
	background: #00B0F0;
}

.square6 {
	background: #fff;
}

.tableTdCss {
	width: 140px;
	height: 40px;
	padding-left: 10px;
	font-size: 12px;
}
.layui-table, .layui-table-view {
	margin: 0;
}

.layui-laypage {
	margin: 3px 0;
}

.layui-table-page > div {
	text-align: center;
}

.layui-layer-content {
	/*overflow-y: auto !important;
	overflow-x: hidden !important;*/
}
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
	width: 6px;
	height: 8px;
}

::-webkit-scrollbar-button {
	display: none;
}

::-webkit-scrollbar-thumb {
	background: rgba(144, 147, 153, 0.6);
	cursor: pointer;
	border-radius: 4px;
}

::-webkit-scrollbar-corner {
	display: none;
}

::-webkit-resizer {
	display: none;
}

.god {}
/* 权限（音乐） */
.god-a-m {}
/* 权限（机台） */
.god-a-v {}
/* 权限（机台/管理员） */
.god-a-vm {}
/* 权限（机台/普通锁定） */
.god-a-vl {}
/* 权限（机台/严格锁定） */
.god-a-vh {}
/* 权限（工具） */
.god-a-t {}
/* 权限（脚本-RPA） */
.god-a-s {}
/* 权限（脚本/更改） */
.god-a-sc {}
/* 权限（脚本/启动） */
.god-a-su {}
/* 权限（脚本/暂停） */
.god-a-sp {}
/* 权限（脚本/停止） */
.god-a-sd {}
/* 设备（录屏） */
.god-v-r, .god-v-ra, .god-v-rn, .god-v-rw, .god-v-rc, .god-v-rs {}
/* 设备（同步) Sync: Mouse, Pilot */
.god-v-s, .god-v-sm, .god-v-sp {}
/* 设备（扩展功能） */
.god-v-e {}
/* 设备（忽略权限） */
.god-v-a {}
/* 设备（代理） */
.god-v-p {}
/* 元素显示（Matrix） */
.god-e-m, .god-e-mi {}
