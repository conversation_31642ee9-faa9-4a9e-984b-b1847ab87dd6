﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title>登录页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css" />
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="../user/loginMenu.docss" />
	<style type="text/css">
		form {
			width: 400px;
			height: 320px;
			left: 50%;
			top: 50%;
			/*margin-left: -50%;
			margin-top: -50%;*/
			background-color: white;
			border-radius: 8px;
			box-shadow: 0 3px 18px rgba(100, 0, 0, .5);
		}

		.form-title {
			text-align: center;
			width: 99%;
			height: 100px;
			margin: 0px;
			border-radius: 8px;
			background-image: url(imgs/logo.png);
			background-position: left top;
			background-repeat: no-repeat;
			background-size: contain;
		}

		.layui-form-select {
			width: 230px;
		}

		#eye {
			width: 25px;
			height: 15px;
			position: absolute;
			right: 65px;
			margin-top: 12px;
			top: 1px;
			text-align: center;
		}

		#remember-wrapper .layui-form-checkbox[lay-skin=primary] {
			padding-left: 20px;
		}

		#remember-wrapper .layui-form-checkbox[lay-skin=primary] span {
			font-size: 12px;
		}

		#remember-wrapper .layui-form-checkbox[lay-skin=primary] i {
			width: 12px;
			height: 12px;
			line-height: 12px;
			top: 2px;
		}

		.Version, .Title, .Client, .a-exe {
		}

        #client-label, #tool-box {
            display: none;
        }
	</style>
	<script type="text/javascript">
		if (window.top != window.self)
			top.location.href = window.location.href;
		window.webName = 'login';
	</script>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<!--<script type="text/javascript" src="js/mouse-love.js"></script>-->
	<!--<script type="text/javascript" src="js/request.js"></script>-->
	<!-- 引入鼠标悬浮特效 -->
	<!--<script type="text/javascript" src="js/background.js"></script>-->
	<!--<script type="text/javascript" src="js/canvas-particle.js"></script>-->
	<script type="text/javascript">
		window.ldb('.page', window.webName);
		$(document).ready(function ()
		{
			document.title = lang(window.CS.company || '') + lang(window.CS.title || window.CS.AssemblyTitle) + ' —— ' + lang('登录');
			if (!window.Data)
			{
				$('#DomainLogon').remove();
				$('#a-shortcut').remove();
				return;
			}

			var t = window.Data('DomainLogon', '');
			if (!t || t == '')
			{
				$('#DomainLogon').remove();
			}
			else
			{
				$('#DomainLogon').data('Account', t);
				t = window.Data('DomainLogon', window.CS.Plugs.PublicKey);
				$('#DomainLogon').data('Password', t);
			}
			t = window.Data('OpenVideo', '?Sn=&Close=1');
			t = location.search;
			if (t.indexOf('?dev=') >= 0 || t.indexOf('&dev=') >= 0 || window.Data('Dev', '?') == 'Dev')
			{
				$('#devTool').show();
				$('#devTool').data('dev', true);
				$('#devTool').data('debug', true);
			}
			//t = window.Data('CT', null);
			//if (!t || parseInt(t) < 10)
			//	$('#titleBar').show();
			//var iTop = (window.screen.height - 30 - 600) / 2; //获得窗口的垂直位置;
			//var iLeft = (window.screen.width - 10 - 800) / 2; //获得窗口的水平位置;
			//window.Data('width', '800');
			//window.Data('height', '600');
			//window.Data('top', iTop + '');
			//window.Data('left', iLeft + '');
		});
	</script>
</head>
<body style="background-image: url(imgs/login_bg1.jpg); background-size: cover; background-position: center center; background-attachment: fixed; background-repeat: no-repeat; background-color: skyblue;">
	<div id="mydiv">
		<div style="width: 100vw; height: 100vh;">
			<div style="position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);">
				<form id="frm-log" class="layui-form" action="" target="_blank" method="post" style="display: none;">
					<div class="layui-form-item form-title">
						<div style=" display: flex; flex-direction: column; height: 100%; justify-content: center; padding-left: 130px;">
							<h1 style="font-size: 20px; font-weight: bold; color: rgb(46, 104, 179);" class="Title">远程控制管理系统（RCM）</h1>
							<h6 style="text-align: right; font-size: 12px; padding-right: 20px; color: cadetblue; margin-top: 3px;" class="Version">Version：*******</h6>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label myFont">账号:</label>
						<div class="layui-input-block">
							<input type="text" name="username" id="username" lay-filter="username" class="layui-input" style="position: absolute; z-index: 2; width: 200px;"
								   lay-verify="required" lay-reqtext="用户名是必填项，岂能为空？" lay-vertype="tips" placeholder="请输入账号">
							<select type="text" id="user-list" lay-filter="user-list" class="layui-select"><option value=""></option></select>
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label myFont">密码:</label>
						<div class="layui-input-block">
							<input class="layui-input layui-icon" type="password" name="pwd" id="userpwd" lay-verify="required" lay-reqtext="密码不可为空！" lay-vertype="tips" placeholder="请输入密码" autocomplete="off" style="width: 230px;" />
							<!--<img id="eye"  src="imgs/closeEye.png" />-->
						</div>
					</div>
					<div style="position: absolute; left: 60px;">
						<input type="checkbox" name="remember" lay-skin="primary" title="记住密码">
						<input type="checkbox" id="DomainLogon" name="DomainLogon" lay-filter="DomainLogon" lay-skin="primary" title="当前用户登录">
					</div>
					<div class="layui-form-item" style="margin-top: 45px;">
						<div class="layui-input-block">
							<button type="submit" id="btn_submit" lay-filter="Btnlogin" lay-submit="" class="layui-btn" style="width:auto;margin-left:55px;">
								<i class="layui-icon">&#xe66f;</i> <span>登录</span>
							</button>
							<button type="button" id="btn_cancel" class="layui-btn layui-btn-primary" style="width:auto;visibility:hidden">
								<i class="layui-icon">&#xe662;</i> <span>退出</span>
							</button>
						</div>
					</div>

					<div class="layui-form-item">
						<label id="client-label" class="Client" style="float: left; width: 200px; height: 25px; color: cadetblue; padding-top: 9px; padding-left: 10px;">Version：*******</label>
						<div id="tool-box" style="float: left; width: 200px; height: 25px; color: cadetblue; padding-top: 5px; padding-left: 10px;">
							<a style="width: 24px; height: 24px; padding-left: 10px;" class="a-exe" href="../rcs.exe" target="_blank" title="下载客户端exe">
								<img src="imgs/exe.png" alt="" />
							</a>
							<a style="width: 24px; height: 24px; padding-left: 5px;" class="a-zip" href="../rcs.zip" target="_blank" title="下载客户端zip">
								<img src="imgs/zip.png" alt="" />
							</a>
							<a style="width: 24px; height: 24px; padding-left: 5px;" class="a-help" href="help.html" target="_blank" title="帮助">
								<img src="imgs/help_icon.png" alt="" />
							</a>
						</div>
						<div class="layui-input-block" style="text-align: right; padding-right: 4px; line-height: 30px;">
							<a id="a-shortcut" style="width: 24px; height: 24px; padding: 2px; line-height: 16px; text-decoration: underline; cursor: pointer; " onclick="javascript: window.Data('Shortcut', '');">创建快捷方式</a>
						</div>
					</div>
				</form>

				<form id="frm-env" class="layui-form" action="?" style="display: none;">
					<div class="layui-form-item" style="text-align:center;width:99%;">
						<label class="layui-form-label myFont" style="width:auto;">请选择登录环境 —— <span class="Title">智能远程控制管理系统（iRCM）</span></label>
					</div>
					<div class="layui-form-item">&nbsp;</div>
					<div class="layui-form-item">
						<div class="layui-input-block">
							<button type="button" id="btn-local" style="width: 180px;" onclick="javascript: $('#frm-env').toggle(); $('#frm-log').toggle();" class="layui-btn layui-btn-danger">
								<i class="layui-icon">&#xe665;</i> <span id="span-env-a">正式环境</span>
							</button>
						</div>
					</div>
					<div class="layui-form-item">
						<div class="layui-input-block">
							<button type="button" id="btn-remote" style="width:180px;" onclick="javascript: location.href = this.form.action;" class="layui-btn">
								<i class="layui-icon">&#xe631;</i> <span id="span-env-b">测试环境</span>
							</button>
						</div>
					</div>

					<div class="layui-form-item" style="height: 7px; line-height: 7px;">&nbsp;</div>
					<div class="layui-form-item">
						<label id="client-label" class="Client" style="float: left; width: 200px; height: 25px; color: cadetblue; padding-top: 9px; padding-left: 10px;">Version：*******</label>
						<div id="tool-box" style="float: left; width: 200px; height: 25px; color: cadetblue; padding-top: 5px; padding-left: 10px;">
							<a style="width: 24px; height: 24px; padding: 2px; line-height: 16px; text-decoration: underline;" class="a-exe" href="#" target="_blank">客户端</a>
							<a style="width: 24px; height: 24px; padding: 2px; line-height: 16px; text-decoration: underline;" href="help.html" target="_blank">帮助</a>
						</div>
						<div class="layui-input-block" style="text-align: right; padding-right: 4px;">
							<a name="a_ZH" class="layui-btn" style="width: 24px; height: 24px; padding: 2px; line-height: 16px;" href="local.config.js?lang=zh-CN">中</a>
							<a name="a_EN" class="layui-btn" style="width: 24px; height: 24px; padding: 2px; line-height: 16px;" href="local.config.js?lang=EN">EN</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<div id="devTool" style="color: red; position: absolute; display: none; right: 0; bottom: 0px; z-index: 999;">
		<span onclick="javascript: this.innerHTML = navigator.userAgent;"></span>
		|
		<a href="javascript: void(0);" onclick="javascript: top.window.Data('Dev', location.href);">Developer Tool</a>
		|
		<a href="javascript: window.location.reload();">Reload</a>
	</div>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" id="function parseSearch()">
		function parseSearch()
		{
			var qs = $.parseUrl(location.search);// http://***********/web/login.html?dev=1&test=***********
			if (!!qs.dev)
			{
				$('#devTool').show();
				$('#devTool').data('dev', true);
				$('#devTool').data('debug', true);
			}
			if (!!qs.test && !!document.getElementById('frm-env'))
			{
				qs.search = location.search;
				if (!qs.real)
					qs.search = qs.search + '&real=' + location.hostname;
				//if (location.hostname != qs.test || !qs.real)//当前时正式地址
				//{
				//	qs.hostname = qs.test;
				//}
				//else//当前是测试地址
				//{
				//	qs.hostname = qs.real;
				//	$('#span-env-a').data('data-text', $('#span-env-a').text());
				//	$('#span-env-a').text($('#span-env-b').text());//改变 上按钮 文字
				//	$('#span-env-b').text($('#span-env-a').data('data-text'));//改变 下按钮 文字
				//	$('#frm-log').css('background-color', 'yellow');//改变 登录框 背景色
				//}
				//qs.address = location.protocol + '//' + qs.hostname + ':' + location.port + location.pathname + qs.search + location.hash;
				//$('#frm-env').attr('action', qs.address);
				//$('#frm-env').toggle();
				if (location.hostname != qs.test || !qs.real)//当前时正式地址
				{
					qs.hostname = qs.test;
					qs.address = location.protocol + '//' + qs.hostname + ':' + location.port + location.pathname + qs.search + location.hash;
					$('#frm-env').attr('action', qs.address);
					$('#btn_submit').css('background-color', '#FF794E');//改变 登录按钮 背景色
					$('#frm-env').toggle();
					$('.Version').html('Product Version ' + window["local.config"].ProductVersion.replace(/\.\d+$/, ''));
					if (!!window.Data)
					{
						$('#client-label').show();
						$('.Client').html('Client Version ' + window.Data('Version', '').replace(/\.\d+$/, ''));
					}
					else
					{
						$('.a-exe').attr('href', '../rcs.exe')
						$('#tool-box').show();
					}
					$('.Version').css('color', '#FF794E');
					$('.Client').css('color', '#FF794E');
				}
				else//当前是测试地址
				{
					$('#frm-log').toggle();
					$('.Version').html('Test Version ' + window["local.config"].ProductVersion.replace(/\.\d+$/, ''));
					if (!!window.Data)
					{
						$('#client-label').show();
						$('.Client').html('Client Version ' + window.Data('Version', '').replace(/\.\d+$/, ''));
					}
					else
					{
						$('.a-exe').attr('href', '../rcs.exe')
						$('#tool-box').show();
					}
				}
			}
			else
			{
				$('#frm-log').toggle();
				$('.Version').html('Product Version ' + window["local.config"].ProductVersion.replace(/\.\d+$/, ''));
				if (!!window.Data)
				{
					$('#client-label').show();
					$('.Client').html('Client Version ' + window.Data('Version', '').replace(/\.\d+$/, ''));
				}
				else
				{
					$('.a-exe').attr('href', '../rcs.exe')
					$('#tool-box').show();
				}
				$('.Version').css('color', '#FF794E');
				$('.Client').css('color', '#FF794E');
			}
			$('.Title').html(lang(window.CS.title || window.CS.AssemblyTitle));
		}
	</script>
	<script type="text/javascript">
		layui.use(['form', 'laydate', 'dropdown'], function ()
		{
			var form = layui.form
				, $ = layui.$
				, layer = layui.layer
				, dropdown = layui.dropdown;
			window.lang.call(this, $);
			if (!!window.langs && window.langs.name == 'EN')
				$('a[name="a_EN"]').hide();
			else
				$('a[name="a_ZH"]').hide();
			$('#username').attr('lay-reqtext', lang('用户名是必填项，岂能为空？'));
			$('#userpwd').attr('lay-reqtext', lang('密码不可为空！'));
			parseSearch();
			function isChoseRemember(name)
			{
				var savedPassword = getPassword(name);
				$("#userpwd").val(savedPassword);
				if (savedPassword)
				{
					$("input[name='remember']").prop('checked', true);
					form.render('checkbox');
				} else
				{
					$("input[name='remember']").prop('checked', false);
					form.render('checkbox');
				}
			}
			var userPwd = window.ldb('user-list');
			if (!!userPwd)
			{
				var dl = {
					elem: '#username',
					trigger: 'focus',
					style: 'width: 230px;',
					click: function (v)
					{
						$(this.elem).val(v.title);
						setTimeout(function () { $('#userpwd').focus(); }, 100);
					}
				}
				if (userPwd[0] == '{')
				{
					dl.data = [];
					userPwd = JSON.parse(userPwd);
					for (var key in userPwd)
						dl.data.push(key);
				}
				else
				{
					dl.data = userPwd.split(/\s+/);
				}
				$(dl.elem).val(dl.data[0]);
				isChoseRemember(dl.data[0]);
				$(dl.elem).data('dropdown', dl);
				$('#user-list').data('user-list', dl.data);
				for (var i = 0; i < dl.data.length; i++)
				{
					$('#user-list').append('<option value="' + dl.data[i] + '">' + dl.data[i] + '</option>');
					dl.data[i] = { title: dl.data[i] };
				}
				form.on('select(user-list)', function (data, e)
				{
					if (!!$('#DomainLogon').prop('checked'))
					{
						$('#username').val($('#DomainLogon').data('Account'));
						return;
					}

					var un = $(data.elem).val();
					if (un != '')
					{
						$("#username").val(un);
						isChoseRemember(un);
					}
					$('#userpwd').focus();
				});
				form.render('select');
			}
			if (!$('#user-list').data('user-list'))
			{
				$('#user-list').remove();
				$('.layui-form-select').remove();
				$('#username').css('width', '230px');
			}
			if ($('#username').val() == '')
				$('#username').focus();
			else
				$('#userpwd').focus();

			$('#username').on('input propertychange', function ()
			{
				isChoseRemember($(this).val());
			});
			if (!!$('#DomainLogon').data('Account'))
			{
				form.on('checkbox(DomainLogon)', function ()
				{
					if (!!$(this).prop('checked'))
					{
						$('#DomainLogon').data('username', $('#username').val());
						$('#DomainLogon').data('userpwd', $('#userpwd').val());
						$('#user-list').attr('disabled', 'disabled');
						$('#username').attr('disabled', 'disabled');
						$('#userpwd').attr('disabled', 'disabled');
						$('#username').css('color', '#EEEEEE');
						$('#userpwd').css('color', '#EEEEEE');
						$('#username').val($('#DomainLogon').data('Account'));
						$('#userpwd').val($('#DomainLogon').data('Password'));
					}
					else
					{
						$('#user-list').removeAttr('disabled');
						$('#username').removeAttr('disabled');
						$('#userpwd').removeAttr('disabled');
						$('#username').css('color', 'inherit');
						$('#userpwd').css('color', 'inherit');
						$('#username').val($('#DomainLogon').data('username'));
						$('#userpwd').val($('#DomainLogon').data('userpwd'));
					}
					form.render('select');
				});
			}
			//监听提交
			form.on('submit(Btnlogin)', function (data)
			{
				var ajax = {};
				ajax.url = '/User/Login.do';
				ajax.data = data.field;
				ajax.cache = false;
				ajax.dataType = 'text';
				ajax.success = function (result)
				{
					var cu = $('#username').val();
					window.ldb('login', result);
					window.ldb('version', $('.Version').html());
					if (!$('#DomainLogon').prop('checked'))
						savePassword(cu, !$("input[name='remember']").is(":checked") ? '' : $('#userpwd').val());
					/*if (!!$('#devTool').data('debug'))
						alert(result);*/
					top.location.href = top.location.href.replace(/login\.html/img, 'index.html');
				};
				ajax.error = function (r)
				{
					console.log(r);
					layer.alert(lang('登录名或密码错误'), { btn: [lang('确定')], title: lang('提示') });
				};
				$.ajax(ajax);
				return false;
			});
			$('#eye').click(function (obj)
			{
				var password = $('input[name=pwd]').attr('type');
				if (password == "password")
				{
					$('input[name=pwd]').attr('type', 'text');
					$('#eye').attr('src', 'imgs/openEye.png');
				}
				else
				{
					$('input[name=pwd]').attr('type', 'password');
					$('#eye').attr('src', 'imgs/closeEye.png');
				}
			})

			$('.MinMaxClose').click(function ()
			{
				layer.alert(lang('是否确认退出系统？'), {
					title: lang('系统退出')
					, btn: [lang('是'), lang('否')]
					, btnAlign: 'c'
					, btn1: function ()
					{
						top.window.close();
					}
				});
				return false;
			});
		});
		function savePassword(name, password)
		{
			var savedPasswords = window.ldb('user-list');
			if (!savedPasswords)
			{
				savedPasswords = {};
			}
			else if (savedPasswords[0] == '{')
			{
				savedPasswords = JSON.parse(savedPasswords);
			}
			else
			{
				var ts = savedPasswords.split(/\s+/);
				savedPasswords = {};
				for (var i = 0; i < ts.length; i++)
					savedPasswords[ts[i]] = '';
			}
			savedPasswords[name] = password;
			window.ldb('user-list', JSON.stringify(savedPasswords));
		}
		function getPassword(name)
		{
			var savedPasswords = window.ldb('user-list');
			if (!savedPasswords || savedPasswords[0] != '{')
				return '';

			savedPasswords = JSON.parse(savedPasswords);
			return savedPasswords[name] || '';
		}
	</script>
	<script type="text/javascript">
		window['redirect-http'] = function (cs)
		{
			if (!window.clientVer || window.clientVer.value < 1001.002)//001.************
				return;

			if (!location.port || location.port * 1 <= 80)
				return;

			if (arguments.length > 1)
			{
				cs.homeUrl = location.protocol + '//' + location.hostname + ':' + cs.WebPoint;
				window.Data('Home', cs.homeUrl + '/');
				window.top.window.location.replace(cs.homeUrl + location.pathname + location.search + location.hash);
				return;
			}

			if (!cs || !cs.WebPoint || cs.WebPoint == location.port)
				return;

			document.writeln('<script type="text/javascript" src="' + location.protocol + '//' + location.hostname + ':' + cs.WebPoint + cs.ConfigData.WebRoot + 'local.config.js?.do=redirect-http" defer="defer" async="async"><' + '/script>');
		};
		window['redirect-http'].call(window, window['local.config']);
	</script>
</body>

</html >