﻿
; (function (global) {
    "use strict";

    var whLoading = function (opt) {
        this._init(opt);
    }

    whLoading.prototype = {
        constructor: whLoading,

        _init: function (opt) {
            var def = {
                id: "whLoading",
                isShow: true,
            }
            this._opt = Object.assign({}, def, opt);
            this._loadElement = document.getElementById(this._opt.id);
            this.addStyles();
            this.renderLoadingElement();
        },

        renderLoadingElement() {
            var html = '<div class="wh-loader">';
            for (var i = 0; i < 5; i++) {
                html += '<div class="wh-circle"></div>';
            }
            html += '</div>';
            this._loadElement.innerHTML = html;
        },

        addStyles() {
            var styles = [
                '#' + this._opt.id + ' { width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background: rgba(0, 0, 0, 0.2); }',
                '.wh-loader { width: 10em; display: flex; justify-content: space-evenly; }',
                '.wh-circle { width: 1em; height: 1em; border-radius: 50%; position: relative; }',
                '.wh-circle:nth-child(1) { background-color: #90be6d; }',
                '.wh-circle:nth-child(2) { background-color: #f9c74f; }',
                '.wh-circle:nth-child(3) { background-color: #f8961e; }',
                '.wh-circle:nth-child(4) { background-color: #f3722c; }',
                '.wh-circle:nth-child(5) { background-color: #f94346; }',
                '.wh-circle::before { content: ""; width: 100%; height: 100%; position: absolute; border-radius: 50%; opacity: 0.5; animation: animateLoader 2s ease-out infinite; }',
                '.wh-circle:nth-child(1)::before { background-color: #90be6d; }',
                '.wh-circle:nth-child(2)::before { background-color: #f9c74f; animation-delay: 0.2s; }',
                '.wh-circle:nth-child(3)::before { background-color: #f8961e; animation-delay: 0.4s; }',
                '.wh-circle:nth-child(4)::before { background-color: #f3722c; animation-delay: 0.6s; }',
                '.wh-circle:nth-child(5)::before { background-color: #f94346; animation-delay: 0.8s; }',
                '@keyframes animateLoader {',
                '    0% { transform: scale(1); }',
                '    50%, 75% { transform: scale(2.5); }',
                '    80%, 100% { opacity: 0; }',
                '}'
            ].join('\n');
            var styleElement = document.createElement('style');
            if (styleElement.styleSheet) {
                styleElement.styleSheet.cssText = styles;
            } else {
                styleElement.appendChild(document.createTextNode(styles));
            }
            document.getElementsByTagName('head')[0].appendChild(styleElement);

            // document.head.appendChild(document.createElement('style')).textContent = styles;
        },

        show: function () {
            if (this._opt.isShow) return;
            this._opt.isShow = true;
            this._loadElement.style.display = "flex";
        },

        hide: function () {
            this._opt.isShow = false;
            this._loadElement.style.display = "none";
        }
    }

    if (typeof module !== "undefined" && module.exports) {
        module.exports = whLoading;
    } else if (typeof define === "function" && define.amd) {
        define(function () { return whLoading; });
    } else {
        try {
            global['whLoading'] = whLoading;
        } catch (e) {
            console.error("Unable to expose whLoading to global scope", e);
        }
    }
})((typeof self !== 'undefined') ? self : this);