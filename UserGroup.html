﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>用户组管理</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style>
		.layui-transfer-box {
			width: calc(50% - 50px);
			height: 100%;
			min-width: 200px;
			min-height: 360px;
		}

		.laytable-cell-checkbox, .laytable-cell-numbers, .laytable-cell-radio, .laytable-cell-space {
			padding-left: 10px;
		}

		.noSelect {
			background-color: #bbbbbb !important;
		}
		.noSelect:hover {
			background-color: #bbbbbb !important;
		}

		#unBindAdminFooter, #unBindUserFooter {
			text-align: center;
		}

		.layui-transfer-search .layui-input {
			padding-left: 10px;
		}

		.layui-transfer-search .layui-icon-search {
			right: 20px;
			left: auto;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/request.js"></script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>用户组设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:100px;">用户组名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="userGroupname" autocomplete="off" placeholder="请输入用户名称"
							   oninput="cleanSpelChar(this)">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="userGroupSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="userGrouplist" lay-filter="userGrouptable"></table>
		<script type="text/html" id="userGroupbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="operation">
			{{#  if(hasButtonPermission('UserGroupSet')){ }}
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
			<a class="layui-btn layui-btn-primary layui-border-blue layui-btn-xs" name="eventAdmin" lay-event="admin">{{lang('绑定管理员')}}</a>
			<a class="layui-btn layui-btn-primary layui-btn-xs layui-border-green" name="eventUser" lay-event="user">{{lang('绑定成员')}}</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs" name="eventUser" lay-event="equip">{{lang('修改默认权限')}}</a>
			<a class="layui-btn layui-btn-primary layui-btn-xs" name="eventUserEquip" lay-event="userEquip">{{lang('修改用户权限')}}</a>
			{{# } else { }}
			<a class="layui-btn layui-btn-xs" name="eventUser" lay-event="user">{{lang('绑定成员')}}</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs" name="eventUser" lay-event="equip">{{lang('修改默认权限')}}</a>
			<a class="layui-btn layui-btn-primary layui-btn-xs" name="eventUserEquip" lay-event="userEquip">{{lang('修改用户权限')}}</a>
			{{# } }}
		</script>
	</form>

	<form id="addUserGroup" class="layui-form" lay-filter="form-addUserGroup" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">用户组名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入用户组名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>
	</form>

	<div id="admin-wrapper" style="display: none; margin: 20px; height: calc(100% - 40px); min-width: 500px;">
		<div class="layui-transfer-box">
			<div class="layui-transfer-header" style="padding-left: 15px;">
				<div>
					<span>未绑定<span class="name">用户</span></span>
				</div>
			</div>
			<div class="layui-transfer-search">
				<i class="layui-icon layui-icon-search" style="cursor: pointer"></i>
				<input id="unbindAdminSearch" type="text" class="layui-input" placeholder="请输入" onblur="onUserSearch(this, 4)">
			</div>
			<div id="unBindAdmin-container">
				<table style="border: 0px;" id="unBindAdmin" lay-filter="unBindAdminTable"></table>
				<div id="unBindAdminFooter" class="layui-table-page"></div>
			</div>
		</div>

		<div class="layui-transfer-active">
			<button type="button" id="registerAdminbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0" onclick="OnBindAdmin();">
				<i class="layui-icon layui-icon-next"></i>
			</button>
			<button type="button" id="unregisterAdminbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1" onclick="OnUnBindAdmin();">
				<i class="layui-icon layui-icon-prev"></i>
			</button>
		</div>

		<div class="layui-transfer-box">
			<div class="layui-transfer-header" style="padding-left: 15px;">
				<div>
					<span>已绑定<span class="name">用户</span></span>
				</div>
			</div>
			<div class="layui-transfer-search">
				<i class="layui-icon layui-icon-search" style="cursor: pointer;"></i>
				<input id="bindAdminSearch" type="text" class="layui-input" placeholder="请输入" onblur="onUserSearch(this, 5)">
			</div>
			<div id="registerAdmin-container">
				<table style="border: 0px;" id="bindAdmin" lay-filter="bindAdminTable"></table>
			</div>
		</div>
	</div>
	<div id="user-wrapper" style="display: none; margin: 20px; height: calc(100% - 40px); min-width: 500px;">
		<div class="layui-transfer-box">
			<div class="layui-transfer-header" style="padding-left: 15px;">
				<div id="checkAllUnRegister">
					<span>未绑定<span class="name">用户</span></span>
				</div>
			</div>
			<div class="layui-transfer-search">
				<i class="layui-icon layui-icon-search" style="cursor: pointer"></i>
				<input id="unbindSearch" type="text" class="layui-input" placeholder="请输入" onblur="onUserSearch(this, 0)">
			</div>
			<div id="unBindUser-container">
				<table style="border: 0px;" id="unBindUser" lay-filter="unBindUserTable"></table>
				<div id="unBindUserFooter" class="layui-table-page"></div>
			</div>
		</div>

		<div class="layui-transfer-active">
			<button type="button" id="registerbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0" onclick="OnBindTool();">
				<i class="layui-icon layui-icon-next"></i>
			</button>
			<button type="button" id="unregisterbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1" onclick="OnUnBindTool();">
				<i class="layui-icon layui-icon-prev"></i>
			</button>
		</div>

		<div class="layui-transfer-box">
			<div class="layui-transfer-header" style="padding-left: 15px;">
				<div id="checkAllRegister">
					<span>已绑定<span class="name">用户</span></span>
				</div>
			</div>
			<div class="layui-transfer-search">
				<i class="layui-icon layui-icon-search" style="cursor: pointer;"></i>
				<input id="bindSearch" type="text" class="layui-input" placeholder="请输入" onblur="onUserSearch(this, 1)">
			</div>
			<div id="register-container">
				<table style="border: 0px;" id="bindUser" lay-filter="bindUserTable"></table>
			</div>
		</div>
	</div>
	<div id="equip-wrapper" style="display: none; margin: 20px; height: calc(100% - 40px); min-width: 500px;">
		<div class="layui-transfer-box">
			<div class="layui-transfer-header" style="padding-left: 15px;">
				<div>
					<span>未绑定<span class="name">机台</span></span>
				</div>
			</div>
			<div class="layui-transfer-search">
				<i class="layui-icon layui-icon-search" style="cursor: pointer;"></i>
				<input id="unbindEquipSearch" type="text" class="layui-input" placeholder="请输入" onblur="onUserSearch(this, 2)">
			</div>
			<div id="UnEquip-container">
				<table style="border: 0px;" id="unBindEquip" lay-filter="unBindEquipTable"></table>
			</div>
		</div>

		<div class="layui-transfer-active">
			<button type="button" id="registerEquipbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="0" onclick="OnBindEquipTool();">
				<i class="layui-icon layui-icon-next"></i>
			</button>
			<button type="button" id="unregisterEquipbtn" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-disabled" data-index="1" onclick="OnUnBindEquipTool();">
				<i class="layui-icon layui-icon-prev"></i>
			</button>
		</div>

		<div class="layui-transfer-box">
			<div class="layui-transfer-header" style="padding-left: 15px;">
				<div>
					<span>已绑定<span class="name">机台</span></span>
				</div>
			</div>
			<div class="layui-transfer-search">
				<i class="layui-icon layui-icon-search" style="cursor: pointer;"></i>
				<input id="bindEquipSearch" type="text" class="layui-input" placeholder="请输入" onblur="onUserSearch(this, 3)">
			</div>
			<div id="equip-container">
				<table style="border: 0px;" id="bindEquip" lay-filter="bindEquipTable"></table>
			</div>
			<script type="text/html" id="bindEquipOp">
				<a class="layui-btn layui-btn-xs" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			</script>
		</div>
	</div>
	<form id="editEquipForm" class="layui-form" lay-filter="form-edit" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">操作权限</label>
			</div>
			<div class="layui-inline">
				<input type="radio" name="op" value="View" title="查看" checked="checked" />
				<input type="radio" name="op" value="View,Part,Work" title="操作" />
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;"><span class="name">机台</span>权限</label>
			</div>
			<div class="layui-inline">
				<div style="display: inline;"><input type="checkbox" name="equip[Oust]" lay-skin="primary" title="中断操作" /></div>
				<div style="display: inline;"><input type="checkbox" name="equip[SimpleLock]" lay-skin="primary" title="普通锁定" /></div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">RPA脚本</label>
			</div>
			<div class="layui-inline">
				<div style="display: inline;"><input type="checkbox" name="equip[RunScript]" lay-skin="primary" title="运行" /></div>
				<div style="display: inline;"><input type="checkbox" name="equip[StopScript]" lay-skin="primary" title="停止" /></div>
			</div>
		</div>
	</form>
</body>
</html>

<script type="text/javascript">
	var arealist = [];
	var userGrouplist = [];
	var checkedItems = [];
	var col = null;
	var openUser = false; // 打开绑定用户弹窗
	var openEquip = false; // 打开绑定机台弹窗
	var openAdmin = false; // 打开绑定管理员弹窗
	var TableUtils = {
		// 检查ID是否存在于数组中
		isIdExist: function (array, id)
		{
			for (var i = 0; i < array.length; i++)
			{
				if (array[i].ID === id) return true;
			}
			return false;
		},

		addCheckedItem: function (array, item)
		{
			if (!this.isIdExist(array, item.ID))
			{
				array.push(item);
			}
		},

		removeCheckedItem: function (array, dataId)
		{
			var newChecked = [];
			for (var i = 0; i < array.length; i++)
			{
				if (array[i].ID !== dataId)
				{
					newChecked.push(array[i]);
				}
			}
			return newChecked;
		}
	};
	function onResize()
	{
		if (openUser)
		{
			tableHeight = $('#user-wrapper .layui-transfer-box')[0].offsetHeight - 92;
			renderTable('#unBindUser');
			renderTable('#bindUser');
			table.reload('unBindUser', {
				data: unbindUserData,
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
			table.reload('bindUser', {
				data: bindUserData,
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
		}

		if (openAdmin)
		{
			tableHeight = $('#admin-wrapper .layui-transfer-box')[0].offsetHeight - 92;
			renderTable('#unBindAdmin');
			renderTable('#bindAdmin');
			table.reload('unBindAdmin', {
				data: unbindAdminData,
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
			table.reload('bindAdmin', {
				data: bindAdminData,
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
		}

		if (openEquip)
		{
			tableHeight = $('#equip-wrapper .layui-transfer-box')[0].offsetHeight - 92;
			renderTable('#unBindEquip');
			renderTable('#bindEquip');
			table.reload('unBindEquip', {
				data: unbindEquipData,
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
			table.reload('bindEquip', {
				data: bindEquipData,
				page: {
					layout: ['count', 'prev', 'page', 'next']
					, curr: 1
				}
			});
		}
	}

	window.addEventListener('resize', onResize);

	layui.config({ base: 'lib/layui/' });
	layui.extend({ notice: '../layui_exts/notice/notice' });
	layui.use(['element', 'table', 'notice', 'layer', 'jquery', 'form', 'laypage'], function ()
	{
		$ = layui.$
			, form = layui.form
			, table = layui.table
			, layer = layui.layer
			, notice = layui.notice
			, laypage = layui.laypage;


		notice.options =
		{
			closeButton: true,//显示关闭按钮
			debug: false,//启用debug
			positionClass: "toast-bottom-right",//弹出的位置,
			showDuration: "300",//显示的时间
			hideDuration: "500",//消失的时间
			timeOut: "3000",//停留的时间
			extendedTimeOut: "1000",//控制时间
			showEasing: "swing",//显示时的动画缓冲方式
			hideEasing: "linear",//消失时的动画缓冲方式
			iconClass: 'toast-info', // 自定义图标，有内置，如不需要则传空 支持layui内置图标/自定义iconfont类名
			onclick: null, // 点击关闭回调
		};
		lang.call(this, $);
		userGroupData = null;
		table.init('userGrouptable', []);
		limitPageCount = 20;
		currentAdminPage = 1;
		currentUserPage = 1;

		isAdmin = window.CS.isConfigUser();
		//创建Table的列名
		tableIni = table.render({
			elem: '#userGrouplist'
			, even: true
			, sort: true
			, loading: false
			, toolbar: hasButtonPermission('UserGroupSet') ? '#userGroupbar' : []
			, defaultToolbar: ['filter']
			, id: "userGrouplist"
			, title: lang('机台数据表')
			, cols: [columns()]
			, height: 'full-140'
			, page: {
				theme: '#FF5722'
				, layout: ['count', 'prev', 'page', 'next', 'skip']
				, prev: lang('上一页')
				, next: lang('下一页')
				, limit: limitPageCount
			}
			, data: []
		});
		FillDataTable();

		//头工具栏事件
		table.on('toolbar(userGrouptable)', function (obj)
		{
			var checkStatus = table.checkStatus(obj.config.id);
			switch (obj.event)
			{
				case 'getCheckData':
					var data = checkStatus.data;
					Delete(data);
					break;
				case 'isAdd':
					layer.open({
						title: lang('增加用户组')
						, type: 1
						, resize: false
						, id: "isAdd-user"
						, content: $('#addUserGroup')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, btn1: function (index)
						{
							AddUpdateData({} , 'add', index);
						}
						, end: function ()
						{
							$('#addUserGroup')[0].reset();
							$('#addUserGroup').css('display', 'none');
						}
					});
					break;
			};
		});

		//监听用户组table行工具事件
		table.on('tool(userGrouptable)', function (obj)
		{
			var data = obj.data;
			if (obj.event === 'del')
			{
				Delete(data);
			}
			if (obj.event === 'edit')
			{
				layer.open({
					title: lang('修改用户组')
					, type: 1
					, resize: false
					, id: 'edit-user'
					, content: $('#addUserGroup')
					, btn: [lang('确认'), lang('取消')]
					, btnAlign: 'c'
					, success: function ()
					{
						form.val('form-addUserGroup', {
							name: data.Name.substring(data.Name.indexOf('/') + 1, data.Name.length)
						});
					}
					, btn1: function (index)
					{
						AddUpdateData(data, 'edit', index);
					}
					, end: function ()
					{
						$('#addUserGroup')[0].reset();
						$('#addUserGroup').css('display', 'none');
					}
				});
			}
			else if (obj.event === 'admin')
			{
				openBindAdminDialog(data);
			}
			else if (obj.event == 'user')
			{
				openBindUserDialog(data);
			}
			else if (obj.event == 'equip')
			{
				openEquipDialog(data);
			}
			else if (obj.event == 'userEquip')
			{
				var name = data.Name.split('/');
				window.location.href = 'Auth.Client.html?Role=' + data.ID + '&RoleName=' + name[1];
			}
		});

		form.on('submit(userGroupSearch)', function (data)
		{
			FillDataTable();
			return false;
		});
		$('button[type=button]').on("click", function ()
		{
			document.getElementById('form-select').reset();
			FillDataTable();
		})
		// 绑定管理员table
		table.on('checkbox(unBindAdminTable)', function (obj)
		{
			var currentPageData = table.cache.unBindAdmin;
			if (obj.checked)
			{
				if (obj.type == 'all')
				{
					for (var i = 0; i < currentPageData.length; i++)
					{
						TableUtils.addCheckedItem(unbindAdminChecked, currentPageData[i]);
					}
				} else
				{
					TableUtils.addCheckedItem(unbindAdminChecked, obj.data);
				}
			} else
			{
				if (obj.type == 'all')
				{
					unbindAdminChecked = unbindAdminChecked.filter(function (item)
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							if (currentPageData[i].ID === item.ID) return false;
						}
						return true;
					});
				} else
				{
					unbindAdminChecked = TableUtils.removeCheckedItem(unbindAdminChecked, obj.data.ID);
				}
			}
			if (unbindAdminChecked.length > 0)
			{
				$('#registerAdminbtn').removeClass('layui-btn-disabled')
			} else
			{
				$('#registerAdminbtn').addClass('layui-btn-disabled')
			}
		});
		table.on('checkbox(bindAdminTable)', function (obj)
		{
			var currentPageData = table.cache.bindAdmin;
			if (obj.checked)
			{
				if (obj.type == 'all')
				{
					for (var i = 0; i < currentPageData.length; i++)
					{
						TableUtils.addCheckedItem(bindAdminChecked, currentPageData[i]);
					}
				} else
				{
					TableUtils.addCheckedItem(bindAdminChecked, obj.data);
				}
			} else
			{
				if (obj.type == 'all')
				{
					bindAdminChecked = bindAdminChecked.filter(function (item)
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							if (currentPageData[i].ID === item.ID) return false;
						}
						return true;
					});
				} else
				{
					bindAdminChecked = TableUtils.removeCheckedItem(bindAdminChecked, obj.data.ID);
				}
			}

			if (bindAdminChecked.length > 0)
			{
				$('#unregisterAdminbtn').removeClass('layui-btn-disabled')
			} else
			{
				$('#unregisterAdminbtn').addClass('layui-btn-disabled')
			}
		});
		// 绑定成员table
		table.on('checkbox(unBindUserTable)', function (obj)
		{
			var currentPageData = table.cache.unBindUser;
			if (obj.checked)
			{
				if (obj.type == 'all')
				{
					for (var i = 0; i < currentPageData.length; i++)
					{
						TableUtils.addCheckedItem(unbindChecked, currentPageData[i]);
					}
				} else
				{
					TableUtils.addCheckedItem(unbindChecked, obj.data);
				}
			} else
			{
				if (obj.type == 'all')
				{
					unbindChecked = unbindChecked.filter(function (item)
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							if (currentPageData[i].ID === item.ID) return false;
						}
						return true;
					});
				} else
				{
					unbindChecked = TableUtils.removeCheckedItem(unbindChecked, obj.data.ID);
				}
			}
			if (unbindChecked.length > 0)
			{
				$('#registerbtn').removeClass('layui-btn-disabled')
			} else
			{
				$('#registerbtn').addClass('layui-btn-disabled')
			}
		});
		table.on('checkbox(bindUserTable)', function (obj)
		{
			var currentPageData = table.cache.bindUser;
			if (obj.checked)
			{
				if (obj.type == 'all')
				{
					for (var i = 0; i < currentPageData.length; i++)
					{
						TableUtils.addCheckedItem(bindChecked, currentPageData[i]);
					}
				} else
				{
					TableUtils.addCheckedItem(bindChecked, obj.data);
				}
			} else
			{
				if (obj.type == 'all')
				{
					bindChecked = bindChecked.filter(function (item)
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							if (currentPageData[i].ID === item.ID) return false;
						}
						return true;
					});
				} else
				{
					bindChecked = TableUtils.removeCheckedItem(bindChecked, obj.data.ID);
				}
			}
			if (bindChecked.length > 0)
			{
				$('#unregisterbtn').removeClass('layui-btn-disabled')
			} else
			{
				$('#unregisterbtn').addClass('layui-btn-disabled')
			}
		});

		// 绑定机台table
		table.on('tool(bindEquipTable)', function (obj)
		{
			if (obj.event === 'edit')
			{
				openEquipAuthDialog(obj.data);
			}
		})
		table.on('checkbox(unBindEquipTable)', function (obj)
		{
			var currentPageData = table.cache.unBindEquip;
			if (obj.checked)
			{
				if (obj.type == 'all')
				{
					for (var i = 0; i < currentPageData.length; i++)
					{
						TableUtils.addCheckedItem(unbindEquipChecked, currentPageData[i]);
					}
				} else
				{
					TableUtils.addCheckedItem(unbindEquipChecked, obj.data);
				}
			} else
			{
				if (obj.type == 'all')
				{
					unbindEquipChecked = unbindEquipChecked.filter(function (item)
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							if (currentPageData[i].ID === item.ID) return false;
						}
						return true;
					});
				} else
				{
					unbindEquipChecked = TableUtils.removeCheckedItem(unbindEquipChecked, obj.data.ID);
				}
			}
			if (unbindEquipChecked.length > 0)
			{
				$('#registerEquipbtn').removeClass('layui-btn-disabled')
			} else
			{
				$('#registerEquipbtn').addClass('layui-btn-disabled')
			}
		});
		table.on('checkbox(bindEquipTable)', function (obj)
		{
			var currentPageData = table.cache.bindEquip;
			if (obj.checked)
			{
				if (obj.type == 'all')
				{
					for (var i = 0; i < currentPageData.length; i++)
					{
						TableUtils.addCheckedItem(bindEquipChecked, currentPageData[i]);
					}
				} else
				{
					TableUtils.addCheckedItem(bindEquipChecked, obj.data);
				}
			} else
			{
				if (obj.type == 'all')
				{
					bindEquipChecked = bindEquipChecked.filter(function (item)
					{
						for (var i = 0; i < currentPageData.length; i++)
						{
							if (currentPageData[i].ID === item.ID) return false;
						}
						return true;
					});
				} else
				{
					bindEquipChecked = TableUtils.removeCheckedItem(bindEquipChecked, obj.data.ID);
				}
			}
			if (bindEquipChecked.length > 0)
			{
				$('#unregisterEquipbtn').removeClass('layui-btn-disabled')
			} else
			{
				$('#unregisterEquipbtn').addClass('layui-btn-disabled')
			}
		});
	});
	function columns()
	{
		return [
			{ title: lang("序号"), type: "numbers", width: 80 }
			, {
				title: lang("名称"), field: "Name", align: 'center', templet: function (d)
				{
					if (!!d[this.field])
					{
						var role = d[this.field];
						var val = role.split('/');
						if (val.length > 1)
							return val[1];
					}

					return ''; 
				}
			}
			, { title: lang("当前管理员"), field: "adminName", align: 'center' }
			, {
				fixed: 'right', width: hasButtonPermission('UserGroupSet') ? 480 : 300,
				title: lang("操作"), toolbar: "#operation", align: 'center'
			}
		]
	}
	function Delete(data)
	{
		layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
		{
			AjaxApi.request({
				url: "../Role/Delete.do",
				data: { id: data.ID },
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					if (res.code == "Success")
					{
						layer.msg(lang('删除成功'), { icon: 1 });
						FillDataTable();
					}
					else
					{
						layer.alert(res.msg, { icon: 2 });
					}
				},
				error: function (r)
				{
					layer.alert(lang('删除失败'), { icon: 2 });
				}
			});
			layer.close(index);
		});
	}
	// 添加、更新单个用户组
	function AddUpdateData(data, type, dialog)
	{
		var data1 = form.val("form-addUserGroup");
		if (data1.name.trim().length == 0)
		{
			layer.alert(lang('名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
			return false;
		}

		var ajax = { data: {}, dataType: 'text' };
		ajax.data['ID'] = data.ID ? data.ID : window.undefined;
		ajax.data['Name'] = data1.name;
		ajax.data['Data'] = ',IsUserGroup,';
		if (type == 'edit')
			ajax.url = '../Role/Edit.do';
		else
			ajax.url = '../Role/Add.do';
		ajax.success = function (result)
		{
			var res = $.eval(result);
			if (res.code !== "Success")
			{
				layer.msg(res.msg, { icon: 2 });;
				return false
			}
			if (type == 'edit')
			{
				layer.msg(lang('修改成功'), { icon: 1 });
				layer.close(dialog);
				FillDataTable();
			} else
			{
				layer.msg(lang('保存成功'), { icon: 1 });
				layer.close(dialog);
				FillDataTable();
			}
		};
		ajax.error = function (r)
		{
			layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("错误"), });
		};
		AjaxApi.request(ajax);
	}

	// 获取用户组数据
	function FillDataTable(isUpdate, callback)
	{
		var data = form.val('form-select');
		var ajax = {
			url: "../Role/List.do",
			data: { Flags: 0, Take: 99999 },
			dataType: 'text'
		};
		ajax.success = function (result)
		{
			var res = $.eval(result);
			var annexAdmin = res.annex;
			var userGroupData = getRoleUserGroup(res.data, 'userGroup');
			var userAdmin;
			if (hasButtonPermission('UserGroupSet'))
			{
				userAdmin = annexAdmin;
			} else
			{
				userAdmin = annexAdmin.filter(function (item) { return item.User == window.login.ID });
			}
			userGroupData = userGroupData.filter(function (val)
			{
				return userAdmin.filter(function (item)
				{
					if (hasButtonPermission('UserGroupSet'))
					{
						return true;
					} else
					{
						return item.Role == val.ID;
					}
				}).length;
			})
			for (var i = 0; i < userGroupData.length; i++)
			{
				var userAdmin = annexAdmin.filter(function (item) { return item.Role == userGroupData[i].ID }); // 绑定的管理员
				var names = '';
				for (var j = 0; j < userAdmin.length; j++)
				{
					if (j == userAdmin.length - 1)
						names += userAdmin[j].Name;
					else
						names += userAdmin[j].Name + ',';
				}
				userGroupData[i].adminName = names;
				userGroupData[i].adminArr = userAdmin; 
			}
			if (!!isUpdate)
			{
				var arr = userGroupData.filter(function (item) { return item.ID == choseRoleId });
				bindAdminData = arr[0].adminArr;
				choseRoleInfo = arr[0];
				if (callback)
				{
					callback(choseRoleInfo, 1);
				}
				table.reload('bindAdmin', {
					data: bindAdminData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
			}
			
			table.reload('userGrouplist', {
				data: userGroupData,
				cols: [columns()],
				page: {
					theme: '#FF5722'
					, layout: ['count', 'prev', 'page', 'next', 'skip']
					, prev: lang('上一页')
					, next: lang('下一页')
					, limit: limitPageCount
					, curr: 1
				}
			});
		};
		if (!!data.userGroupname)
			ajax.data.Name = data.userGroupname;
		AjaxApi.request(ajax);
	}
	/////////// 公用
	var tableHeight = 0;

	function toolColumnsName(id)
	{
		var columnsArray = [
			{ type: 'checkbox', width: 40, event: 'singleclick', align: 'center' },
			{ field: 'Name', minWidth: 120, title: '名称', align: 'center' }
		];

		if (id === '#bindEquip')
		{
			columnsArray.push({
				field: 'Data', title: '权限', width: 90, templet: function (d)
				{
					if (d.Data.indexOf(CS.VideoMenu.$Work) >= 0)
						return "操作";

					return "查看";
				}
			})
			columnsArray.push({
				width: 80, title: lang("操作"), toolbar: "#bindEquipOp", event: 'singleclick', align: 'center', fixed: 'right'
			})
		}

		return columnsArray;
	}
	function renderTable(id)
	{
		table.render({
			elem: id
			, even: true
			, loading: false
			, defaultToolbar: []
			, page: {
				// limits: [20, 30, 50],
				layout: ['count', 'prev', 'page', 'next'],
				limit: limitPageCount
			}
			, height: tableHeight
			, cols: [toolColumnsName(id)]
			, data: []
		});
	}
	// 添加绑定
	function bindOrUnbind(url, data, item)
	{
		return new Promise(function (resolve, reject)
		{
			AjaxApi.request({
				url: url,
				data: data,
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					if (res.code !== 'Success')
					{
						reject(res.msg);
					}
					resolve(item);
				},
				error: function (error)
				{
					reject(error);
				}
			});
		});
	}
	// input查询
	function onUserSearch(elem, type)
	{
		var search = elem.value;
		var searchResult = [];
		var data = [], name = '';
		switch (type)
		{
			case 0:
				data = unbindUserData;
				name = 'unBindUser';
				break;
			case 1:
				data = bindUserData;
				name = 'bindUser';
				break;
			case 2:
				data = unbindEquipData;
				name = 'unBindEquip';
				break;
			case 3:
				data = bindEquipData;
				name = 'bindEquip';
				break;
			case 4:
				data = unbindAdminData;
				name = 'unBindAdmin';
				break;
			case 5:
				data = bindAdminData;
				name = 'bindAdmin';
				break;
		}
		for (var i = 0; i < data.length; i++)
		{
			if (data[i].Name.toLowerCase().indexOf(search.toLowerCase()) >= 0)
			{
				searchResult.push(data[i]);
			}
		}
		table.reload(name, {
			data: searchResult,
			page: {
				layout: ['count', 'prev', 'page', 'next']
				, curr: 1
			}
		});
	}

	///////////  用户组绑定管理员
	var unbindAdminData = [];
	var bindAdminData = [];
	var unbindAdminChecked = [];
	var bindAdminChecked = [];
	// 绑定管理员弹窗
	function openBindAdminDialog(info)
	{
		var names = info.Name.split('/');
		layer.open({
			title: names[1] + lang(' 绑定管理员')
			, type: 1
			, resize: false
			, id: "bindAdmin-dialog"
			, content: $('#admin-wrapper')
			, area: ['70%', '80%']
			, btnAlign: 'c'
			, success: function ()
			{
				$("#unbindAdminSearch").val("");
				$("#bindAdminSearch").val("");
				$('#registerAdminbtn').addClass('layui-btn-disabled')
				$('#unregisterAdminbtn').addClass('layui-btn-disabled');
				currentAdminPage = 1;
				tableHeight = $('#admin-wrapper .layui-transfer-box')[0].offsetHeight - 92;
				choseRoleId = info.ID;
				choseRoleInfo = info;
				openAdmin = true;
				renderTable('#unBindAdmin');
				renderTable('#bindAdmin');
				table.reload('unBindAdmin', {
					data: unbindAdminData
				});
				bindAdminData = info.adminArr;
				table.reload('bindAdmin', {
					data: bindAdminData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
				setAdminList(info)
			}
			, btn1: function (index)
			{
				layer.close(index);
			}
			, end: function (index)
			{
				FillDataTable();
				layer.close(index);
			}
		})
	}
	function setAdminList(info, page, names)
	{
		currentAdminPage = page || currentAdminPage;
		var gData = {};
		if (names)
		{
			gData = { NameLike: names + '%', Flags: 0, Take: 99999,/*Take: limitPageCount, Skip: (currentAdminPage - 1) * limitPageCount*/ };
		} else
		{
			gData = { Flags: 0, Take: 99999, /*Take: limitPageCount, Skip: (currentAdminPage - 1) * limitPageCount*/ };
		}
		AjaxApi.request({
			url: "../ListUser.do",
			data: gData,
			dataType: 'text',
			success: function (result)
			{
				var res = $.eval(result);
				unbindAdminData = res.data.filter(function (item, index, arr)
				{
					for (var i = 0; i < bindAdminData.length; i++)
					{
						if (item.ID == bindAdminData[i].User)
							return false;
					}

					return true;
				});
				table.reload('unBindAdmin', {
					data: unbindAdminData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
			}
		});
	}
	// 解除绑定管理员
	function OnUnBindAdmin()
	{
		var currentIndex = 0;

		function processNext()
		{

			if (currentIndex >= bindAdminChecked.length)
			{
				return Promise.resolve();
			}

			var item = bindAdminChecked[currentIndex];
			var url = '';
			url = "../Role/AddChief.do";
			var data = { id: item.ID };

			return bindOrUnbind(url, data, item)
				.then(function (result)
				{
					var idx = bindAdminData.findIndex(function (val) { return val.ID === item.ID });
					unbindAdminData.push(result);
					bindAdminData.splice(idx, 1);
					currentIndex++;
					return processNext();
				})
				.catch(function (error)
				{
					notice.error('解除绑定' + item.Name + ' ' + error);
					currentIndex++;
					return processNext();
				});
		}

		processNext().then(function ()
		{
			bindAdminChecked = [];
			unbindAdminChecked = [];
			$("#unbindAdminSearch").val("");
			$("#bindAdminSearch").val("");
			$('#unregisterAdminbtn').addClass('layui-btn-disabled');
			FillDataTable('isUpdate', setAdminList);
		});
	}
	// 绑定管理员
	function OnBindAdmin()
	{
		var currentIndex = 0;

		function processNext()
		{

			if (currentIndex >= unbindAdminChecked.length)
			{
				return Promise.resolve();
			}

			var item = unbindAdminChecked[currentIndex];
			var url = '';
			url = "../Role/AddChief.do";

			var data = {
				Role: choseRoleId,
				User: item.ID,
				Name: item.Name,
				RoleName: choseRoleInfo.Name,
				Append: window.CS.AuthData.TYPE_AUTH,//复制所有权限到管理员
				Log: '将用户（' + item.Name + '）设为用户组（' + choseRoleInfo.Name + '）的管理员'
			};
			return bindOrUnbind(url, data, item)
				.then(function (result)
				{
					var idx = unbindAdminData.findIndex(function (val) { return val.ID === item.ID });
					bindAdminData.push(result);
					unbindAdminData.splice(idx, 1);
					currentIndex++;
					return processNext();
				})
				.catch(function (error)
				{
					notice.error('绑定' + item.Name + ' ' + error);
					currentIndex++;
					return processNext();
				});
		}

		processNext().then(function ()
		{
			bindAdminChecked = [];
			unbindAdminChecked = [];
			$("#unbindAdminSearch").val("");
			$("#bindAdminSearch").val("");
			$('#registerAdminbtn').addClass('layui-btn-disabled');
			FillDataTable('isUpdate', setAdminList);
		});
	}
	///////////  用户组绑定用户
	var unbindUserData = [];
	var bindUserData = [];
	var unbindChecked = [];
	var bindChecked = [];
	var choseRoleId = '';
	// 绑定用户弹窗
	function openBindUserDialog(info)
	{
		var names = info.Name.split('/');
		layer.open({
			title: names[1] + lang(' 绑定用户')
			, type: 1
			, resize: false
			, id: "user-dialog"
			, content: $('#user-wrapper')
			// , btn: [lang('确认'), lang('取消')]
			, area: ['70%', '80%']
			, btnAlign: 'c'
			, success: function ()
			{
				$("#unbindSearch").val("");
				$("#bindSearch").val("");
				$('#registerbtn').addClass('layui-btn-disabled')
				$('#unregisterbtn').addClass('layui-btn-disabled');
				currentUserPage = 1;
				tableHeight = $('#user-wrapper .layui-transfer-box')[0].offsetHeight - 92;
				choseRoleId = info.ID;
				choseRoleInfo = info;
				openUser = true;
				renderTable('#unBindUser');
				renderTable('#bindUser');
				table.reload('unBindUser', {
					data: unbindUserData
				});
				table.reload('bindUser', {
					data: bindUserData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
				setBindUserList(info);
			}
			, btn1: function (index)
			{
				if (!!window.top.FillFavTreeTable)
					window.top.FillFavTreeTable();
				layer.close(index);
				openUser = false;
			}
			, end: function (index)
			{
				//FillDataTable();
				if (!!window.top.FillFavTreeTable)
					window.top.FillFavTreeTable();
				layer.close(index);
				openUser = false;
			}
		})
	}
	
	// 设置未绑定、绑定 用户列表
	var unBindUserAnnex = [];
	function setBindUserList(info, page)
	{
		AjaxApi.request({
			url: "../User/List.do",
			data: { Flags: 0, Take: 99999, Role: info.ID },
			dataType: 'text',
			success: function (result)
			{
				var res = $.eval(result);
				bindUserData = [];
				for (var i = 0; i < res.data.length; i++)
				{
					var u = res.data[i];
					for (var ii = 0; ii < res.annex.length; ii++)
					{
						var a = res.annex[ii];
						if (a.Role != info.ID)
							continue;

						if (a.User == u.ID)
						{
							u.AuthID = a.ID;
							bindUserData.push(u);
							u = null;
							break;
						}
					}
				}
				table.reload('bindUser', {
					data: bindUserData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});

				setUnbindUserList(info, page);
			}
		})
	}
	function setUnbindUserList(info, page, names)
	{
		currentUserPage = page || currentUserPage;
		var gData = {};
		if (names)
		{
			gData = { name: names, Flags: 0, Take: 99999, /*Take: limitPageCount, Skip: (currentUserPage - 1) * limitPageCount*/ };
		} else
		{
			gData = { Flags: 0, Take: 99999, /*Take: limitPageCount, Skip: (currentUserPage - 1) * limitPageCount*/ };
		}
		AjaxApi.request({
			url: "../User/List.do",
			data: gData,//ConditionName: window.CS.AuthData.TYPE_ROLE, ConditionValue: info.ID, 
			dataType: 'text',
			success: function (result)
			{
				var res = $.eval(result);
				unbindUserData = [];
				unbindUserData = res.data.filter(function (item, index, arr)
				{
					for (var i = 0; i < bindUserData.length; i++)
					{
						if (item.ID == bindUserData[i].ID)
							return false;
					}

					return true;
				});
				table.reload('unBindUser', {
					data: unbindUserData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
			}
		});
	}
	// 解除绑定用户
	function OnUnBindTool()
	{
		var currentIndex = 0;

		function processNext()
		{

			if (currentIndex >= bindChecked.length)
			{
				return Promise.resolve();
			}

			var item = bindChecked[currentIndex];
			var url = '';
			url = "../User/JoinRole.do";
			var data = {
				id: item.AuthID
			};
			return bindOrUnbind(url, data, item)
				.then(function (result)
				{
					var idx = bindUserData.findIndex(function (val) { return val.ID === item.ID });
					unbindUserData.push(result);
					bindUserData.splice(idx, 1);
					currentIndex++;
					return processNext();
				})
				.catch(function (error)
				{
					notice.error('解除绑定' + item.Name + ' ' + error);
					currentIndex++;
					return processNext();
				});
		}

		processNext().then(function ()
		{
			bindChecked = [];
			unbindChecked = [];
			$("#unbindSearch").val("");
			$("#bindSearch").val("");
			$('#unregisterbtn').addClass('layui-btn-disabled');
			FillDataTable('isUpdate', setBindUserList);
		});
	}
	// 绑定用户
	function OnBindTool()
	{
		var currentIndex = 0;

		function processNext()
		{

			if (currentIndex >= unbindChecked.length)
			{
				return Promise.resolve();
			}

			var item = unbindChecked[currentIndex];
			var url = '';
			url = "../User/JoinRole.do";
			var data = {
				Role: choseRoleId,
				User: item.ID,
				Name: item.Name,
				RoleName: choseRoleInfo.Name,
				Log: '绑定用户 ' + item.Name
			};
			return bindOrUnbind(url, data, item)
				.then(function (result)
				{
					var idx = unbindUserData.findIndex(function (val) { return val.ID === item.ID });
					bindUserData.push(result);
					unbindUserData.splice(idx, 1);
					currentIndex++;
					return processNext();
				})
				.catch(function (error)
				{
					notice.error('绑定' + item.Name + ' ' + error);
					currentIndex++;
					return processNext();
				});
		}

		processNext().then(function ()
		{
			bindChecked = [];
			unbindChecked = [];
			$("#unbindSearch").val("");
			$("#bindSearch").val("");
			$('#registerbtn').addClass('layui-btn-disabled');
			FillDataTable('isUpdate', setBindUserList);
		});
	}
	
	///////////////  用户组机台设置
	var unbindEquipData = [];
	var bindEquipData = [];
	var unbindEquipChecked = [];
	var bindEquipChecked = [];
	var choseRoleInfo = {};
	function openEquipDialog(info)
	{
		var names = info.Name.split('/');
		layer.open({
			title: names[1] + lang(' 默认权限')
			, type: 1
			, resize: false
			, id: "equip-dialog"
			, content: $('#equip-wrapper')
			// , btn: [lang('确认'), lang('取消')]
			, area: ['70%', '80%']
			, btnAlign: 'c'
			, success: function ()
			{
				$("#unbindEquipSearch").val("");
				$("#bindEquipSearch").val("");
				$('#registerEquipbtn').addClass('layui-btn-disabled')
				$('#unregisterEquipbtn').addClass('layui-btn-disabled');
				tableHeight = $('#equip-wrapper .layui-transfer-box')[0].offsetHeight - 92;
				choseRoleId = info.ID;
				choseRoleInfo = info;
				openEquip = true;
				renderTable('#unBindEquip');
				renderTable('#bindEquip');
				setEquipList(info);
			}
			, btn1: function (index)
			{
				if (!!window.top.FillFavTreeTable)
					window.top.FillFavTreeTable();
				layer.close(index);
				openEquip = false;
			}
			, end: function (index)
			{
				//FillDataTable();
				if (!!window.top.FillFavTreeTable)
					window.top.FillFavTreeTable();
				layer.close(index);
				openEquip = false;
			}
		})
	}
	function setEquipList(info)
	{
		var ajax = {};
		ajax.dataType = 'text';
		ajax.url = '/ListVideo.do?Flags=0&Take=65536&ConditionName=' + window.CS.AuthData.TYPE_ALLOC + '&ConditionValue=' + info.ID;

		ajax.success = function (result)
		{
			var res = $.eval(result);
			if (res.code != "Success")
				return;

			var ajax = {};
			ajax.dataType = 'text';
			ajax.data = { Flags: 0, Take: 65535 };
			ajax.data.Type = window.CS.AuthData.TYPE_AUTH;
			ajax.data.User = '';
			ajax.data.Role = info.ID;
			ajax.url = '/ListAuth.do';
			window.currentAll = res.data;
			ajax.success = function (result)
			{
				var res = $.eval(result);
				bindEquipData = res.data
				for (var i = bindEquipData.length - 1; i >= 0; i--)
				{
					bindEquipData[i].Target = bindEquipData[i]['Video'];
					var info = window.currentAll.filter(function (item)
					{
						return item.ID == bindEquipData[i].Target
					});
					if (info.length > 0)
					{
						bindEquipData[i].Name = info[0].Name;
					}
				}

				unbindEquipData = window.currentAll.filter(function (item, index, arr)
				{
					for (var i = 0; i < bindEquipData.length; i++)
					{
						if (item.ID == bindEquipData[i].Target)
							return false;

						if (item.Parent.length > 0)
							return false;
					}

					return true;
				});

				table.reload('unBindEquip', {
					data: unbindEquipData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
				table.reload('bindEquip', {
					data: bindEquipData,
					page: {
						layout: ['count', 'prev', 'page', 'next']
						, curr: 1
					}
				});
			};
			AjaxApi.request(ajax);
		};
		AjaxApi.request(ajax);
	}
	function getAuthData(info)
	{
		var vs = ',';
		for (var k in CS.VideoMenu)
		{
			var i = CS.VideoMenu[k];
			if (!isNaN(i) && info['equip[' + k + ']'] == 'on')
				vs = vs + k + ',';
		}
		vs[0] = info.op;
		if (info.op.indexOf(CS.VideoMenu.$Work) > 0)
		{
			vs = vs + info.op + ',';
			return vs;
		}
		if (info.op.indexOf(CS.VideoMenu.$Part) > 0)
		{
			vs = vs + info.op + ',';
			return vs;
		}
		vs = vs + CS.VideoMenu.$View + ',';
		return vs;
	}
	// 解除机台权限
	function OnUnBindEquipTool()
	{
		var currentIndex = 0;

		function processNext()
		{

			if (currentIndex >= bindEquipChecked.length)
			{
				return Promise.resolve();
			}

			var item = bindEquipChecked[currentIndex];
			var url = '';
			url = "../Auth/Grant.do";
			var data = {
				Role: choseRoleId,
				Delete: 1,
				Video: item.Video,
				Log: '删除 ' + item.Name
			};
			return bindOrUnbind(url, data, item)
				.then(function (result)
				{
					var idx = bindEquipData.findIndex(function (val) { return val.ID === item.ID });
					unbindEquipChecked.push(result);
					bindEquipData.splice(idx, 1);
					currentIndex++;
					return processNext();
				})
				.catch(function (error)
				{
					notice.error('解除绑定' + item.Name + ' ' + error);
					currentIndex++;
					return processNext();
				});
		}

		processNext().then(function ()
		{
			bindEquipChecked = [];
			unbindEquipChecked = [];
			$('#unregisterEquipbtn').addClass('layui-btn-disabled');
			setEquipList(choseRoleInfo);
		});
	}
	// 绑定机台权限
	function OnBindEquipTool()
	{
		var currentIndex = 0;
		editDialog = layer.open({
			title: lang('权限设置')
			, type: 1
			, resize: false
			, id: 'edit1'
			, content: $('#editEquipForm')
			, btn: [lang('确认'), lang('取消')]
			, btnAlign: 'c'
			, success: function () { }
			, btn1: function (index)
			{
				taskBegin();
				layer.close(index);
			}
		});
		

		function processNext()
		{

			if (currentIndex >= unbindEquipChecked.length)
			{
				return Promise.resolve();
			}

			var item = unbindEquipChecked[currentIndex];
			var authData = '';
			var url = '';
			authData = getAuthData(form.val('form-edit'));
			url = "../Auth/Grant.do";
			var data = {
				Role: choseRoleId,
				Video: item.ID,
				Name: item.Name,
				Data: authData,
				Log: '绑定机台 ' + item.Name
			};
			return bindOrUnbind(url, data, item)
				.then(function (result)
				{
					var idx = unbindEquipData.findIndex(function (val) { return val.ID === item.ID });
					bindEquipData.push(result);
					unbindEquipData.splice(idx, 1);
					currentIndex++;
					return processNext();
				})
				.catch(function (error)
				{
					notice.error('解除绑定' + item.Name + ' ' + error);
					currentIndex++;
					return processNext();
				});
		}

		function taskBegin()
		{
			processNext().then(function ()
			{
				bindEquipChecked = [];
				unbindEquipChecked = [];
				$('#registerEquipbtn').addClass('layui-btn-disabled');
				setEquipList(choseRoleInfo);
			});
		}
	}

	// 机台默认权限编辑
	function openEquipAuthDialog(data)
	{
		editDialog = layer.open({
			title: lang('权限设置')
			, type: 1
			, resize: false
			, id: 'editEquip'
			, content: $('#editEquipForm')
			, btn: [lang('确认'), lang('取消')]
			, btnAlign: 'c'
			, success: function ()
			{
				var vs = {};
				vs.ad = data.Data;
				if (vs.ad.indexOf(CS.VideoMenu.$Work) >= 0)
					vs.op = CS.VideoMenu.$View + ',' + CS.VideoMenu.$Part + ',' + CS.VideoMenu.$Work;
				else if (vs.ad.indexOf(CS.VideoMenu.$Part) >= 0)
					vs.op = CS.VideoMenu.$View + ',' + CS.VideoMenu.$Part;
				else
					vs.op = CS.VideoMenu.$View;
				for (var i in CS.VideoMenu)
				{
					if (!isNaN(CS.VideoMenu[i]))
						vs['equip[' + i + ']'] = vs.ad.indexOf(CS.VideoMenu['$' + i]) >= 0;
				}
				form.val('form-edit', vs);
			}
			, btn1: function (value, index)
			{
				AjaxApi.request({
					url: "../Auth/Grant.do",
					data: {
						Video: data.Video,
						Name: data.Name,
						User: data.User,
						Role: data.Role,
						Data: getAuthData(form.val('form-edit')),
						Log: '将 ' + data.Name + '重新分配权限 '
					},
					dataType: 'text',
					success: function (result)
					{
						layer.close(editDialog);
						setEquipList(choseRoleInfo);
					}
				});
			}
		});
	}
</script>