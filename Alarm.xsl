﻿<?xml version="1.0" encoding="utf-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
				xmlns:msxsl="urn:schemas-microsoft-com:xslt" xmlns:user="urn:my-scripts" xmlns:plugs="urn:Plugs" xmlns:utils="urn:Utils" exclude-result-prefixes="msxsl user plugs utils">
	<msxsl:script language="C#" implements-prefix="user">
		<msxsl:assembly name="System.Web"/>
		<msxsl:assembly name="System.Text.RegularExpressions"/>
		<msxsl:using namespace="System.Text.RegularExpressions"/>
		<![CDATA[
		public string Uuid()
		{
			return Guid.NewGuid().ToString("N");
		}
		public string Left(string a, string b)
		{
			int i = a.IndexOf(b);
			if (i > 0)
				a = a.Remove(i);
			return a;
		}
		public string Right(string a, string b)
		{
			int i = a.LastIndexOf(b);
			if (i > 0)
				a = a.Substring(i + b.Length);
			return a;
		}
		public string Match(string input, string pattern)
		{
			Match m = Regex.Match(input, pattern, RegexOptions.Compiled | RegexOptions.Multiline | RegexOptions.IgnoreCase);
			return m.Success ? m.Groups[m.Groups.Count > 1 ? 1 : 0].Value : string.Empty;
		}
		public string Time(string input, string format)
		{
			double ticks;
			if(!double.TryParse(input, out ticks) || ticks <= 0)
				return string.Empty;
				
			DateTime time = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
			time = time.AddMilliseconds(ticks);
			time = time.ToLocalTime();
			return time.ToString(format);
		}
		]]>
	</msxsl:script>
    <xsl:output method="xml" indent="yes" encoding="utf-8" version="1.0"/>

	<xsl:template match="/">
		<xsl:choose>
			<xsl:when test="/*[@Type='READER_RENEW' or @Type='AUTH_OVER' or @Level='TEST']"><xsl:apply-templates select="/*"/></xsl:when>
			<xsl:otherwise>
				<Alarm>
					<xsl:attribute name="ID"><xsl:value-of select="/*/@ID"/></xsl:attribute>
					<xsl:attribute name="Type"><xsl:value-of select="/*/@Type"/></xsl:attribute>
					<xsl:attribute name="Level"><xsl:value-of select="/*/@Level"/></xsl:attribute>
				</Alarm>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template name="store">
		<xsl:param name="store"/>
		<xsl:choose>
			<xsl:when test="$store='总库'">All Warehouse</xsl:when>
			<xsl:when test="$store='资材库（C）'">Material Warehouse(C)</xsl:when>
			<xsl:when test="$store='冷冻库（B）'">Freezer Warehouse(B)</xsl:when>
			<xsl:when test="$store='解冻库（A）'">Unfreeze Warehouse(A)</xsl:when>
			<xsl:when test="$store='工程库'">Project Warehouse</xsl:when>
			<xsl:when test="$store='实验库'">Test Warehouse</xsl:when>
			<xsl:when test="$store='实验解冻库'">Test Unfreeze Warehouse</xsl:when>
			<xsl:otherwise>
				<xsl:value-of select="$store"/>
			</xsl:otherwise>
		</xsl:choose>
	</xsl:template>
	<xsl:template match="Alarm">
		<Request service="7010" network="**********" daemon="tcp:**********:7500" subject="MES.PRD.MES.NM">
			<Header>
				<MESSAGENAME>MES.PRHOLDALARM</MESSAGENAME>
				<TRANSACTIONID><xsl:value-of select="user:Uuid()"/></TRANSACTIONID>
				<ORGRRN>116184139257933824</ORGRRN>
				<ORGNAME>FAB1</ORGNAME>
				<USERNAME>PRAUTO</USERNAME>
				<LANGUAGE>EN</LANGUAGE>
			</Header>
			<Body>
				<xsl:variable name="station" select="user:Match(/*/Statistic/@Data2, '^[^=]*=(.*)$')"/>
				<EQUIPMENTID><xsl:value-of select="$station"/></EQUIPMENTID>
				<EQUIPMENTTYPE/>
				<SUBEQUIPMENTID><xsl:value-of select="$station"/></SUBEQUIPMENTID>
				<ALARMID><xsl:value-of select="@ID"/></ALARMID >
				<ALARMCODE><xsl:value-of select="user:Left(/*/@T-Name, ':')"/></ALARMCODE>
				<ACTIONTYPE>
					<xsl:choose>
						<xsl:when test="@Level='ITEM_BATCH_1'">INFO</xsl:when>
						<xsl:when test="@Type='READER_RENEW'">HOLD</xsl:when>
						<xsl:when test="@Type='AUTH_OVER'">HOLD</xsl:when>
						<xsl:otherwise>INFO</xsl:otherwise>
					</xsl:choose>
				</ACTIONTYPE>
				<REASON>
					<xsl:choose>
						<xsl:when test="@Level='ITEM_BATCH_1'">PR LOT change</xsl:when>
						<xsl:when test="@Level='ITEM_BATCH'">wrong PR LOT</xsl:when>
						<xsl:when test="@Level='ITEM_STOREHOUSE'">wrong PR store</xsl:when>
						<xsl:when test="@Type='READER_RENEW'">wrong PR name</xsl:when>
						<xsl:when test="@Type='AUTH_OVER'">PR uses timeout</xsl:when>
						<xsl:otherwise><xsl:value-of select="user:Right(/*/@T-Name, ':')"/></xsl:otherwise>
					</xsl:choose>
				</REASON>
				<SETTINGPRNAME><xsl:value-of select="user:Match(/*/Statistic/@BindAuth, '(?:^|&amp;)FullName=([^&amp;]+)')"/></SETTINGPRNAME>
				<ACTUALPRNAME><xsl:value-of select="user:Match(/*/Marker[@T-Api='IAuthData']/@Data2, '^[^=]*=(.*)$')"/></ACTUALPRNAME>
				<STATISTIC>
					<xsl:value-of select="user:Match(/*/Statistic/@Data4, '^[^/]+/(.*)$')"/>
					<xsl:text> </xsl:text>
					<xsl:value-of select="user:Match(/*/Statistic/@Data6, '^[^/]+/(.*)$')"/>
				</STATISTIC>
				<SETTINGLOT><xsl:value-of select="/*/Statistic/@BindBatch"/></SETTINGLOT>
				<ACTUALLOT><xsl:value-of select="/*/Marker[@T-Api='IAuthData']/@Batch"/></ACTUALLOT>
				<SETTINGSTORE>
					<xsl:call-template name="store">
						<xsl:with-param name="store" select="user:Match(/*/Statistic/@BindStorehouse, '^[^/]*/[^/]+/(.*)$')"/>
					</xsl:call-template>
				</SETTINGSTORE>
				<ACTUALSTORE>
					<xsl:call-template name="store">
						<xsl:with-param name="store" select="user:Match(/*/Marker[@T-Api='IAuthData']/@Thawhouse, '^[^/]*/[^/]+/(.*)$')"/>
					</xsl:call-template>
				</ACTUALSTORE>
				<ALARMUSER><xsl:value-of select="user:Match(/*/ExtraData[@Type='AUTH/STATISTIC' and @Status=2]/@Data9, '^[^=]*=(.*)$')"/></ALARMUSER>
				<ALARMTIME><xsl:value-of select="user:Time(@Time, 'yyyyMMddHHmmss')"/></ALARMTIME>
			</Body>
		</Request>
	</xsl:template>
</xsl:stylesheet>
