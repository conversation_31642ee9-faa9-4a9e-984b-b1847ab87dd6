﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<style type="text/css">
		a, a::after, a:hover {
			color: gray;
			text-decoration: none;
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" id="layui.use">
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate'], function ()
		{
			$ = layui.$
			laydate = layui.laydate;
			transfer = layui.transfer;
			form = layui.form;
			table = layui.table;
			layer = layui.layer;
			laypage = layui.laypage;
		});
	</script>
	<script type="text/javascript">
		function StartWS(e)
		{
			if (!window.WebSocket)
				return;

			if (!window.MyRPA)
			{
				if (!this.tagName || !this.value || !this.value.indexOf)
					return;

				window.MyRPA = this.value.indexOf('Server');
				setInterval(StartWS, 1000, 0);
			}
			if (!arguments.length)
			{
				if (window.MyRPA > 0)
				{
					var ws = new window.WebSocket('ws://' + location.host + '/DoWork.do?video=**************&account=test1&password=1111');
					ws.onopen = function ()
					{
						console.log('onopen');
					};
					ws.onerror = function ()
					{
						console.log('onerror');
					};
					ws.onclose = function ()
					{
						console.log('onclose');
						StartWS();
					};
					ws.onmessage = StartWS;
					window.MyWS = ws;
				}
				else
				{
					$.get('openVideo.do', function (result)
					{
						if (!result)//OpenVideo=5,199280,DESKTOP-FN4LG6V&1A4A937D295943E6B4203886CF4E9CFA=1,149388,mc,**************&140180=0&184552=0&118004=0&203592=0&Time=*************
						{
							setTimeout(StartWS, 1000);
							return;
						}

						var i = result.indexOf('\r');
						if (i <= 0)
						{
							i = result.indexOf('\n');
							if (i <= 0)
							{
								setTimeout(StartWS, 1000);
								return;
							}
						}

						var qs = $.parseUrl(result.substring(0, i));
						for (var k in qs)
						{
							if (k == 'OpenVideo' || k[0] == '_')
								continue;

							i = parseInt(k);
							if (i >= 0 && '/' + i == '/' + k)
								continue;

							var t = qs[k];
							if (!t || !t.indexOf)
								continue;

							i = t.indexOf(',');
							if (i <= 0)
								continue;

							t = t.substring(i + 1, t.length);
							i = t.indexOf(',');
							if (i > 0)
							{
								result = t.substring(0, i);
								qs = null;
								break;
							}
						}
						if (!!qs)
						{
							setTimeout(StartWS, 1000);
							return;
						}

						var ws = new window.WebSocket('ws://' + location.hostname + ':7823/.' + result + '/DoWork.do');
						ws.onopen = function ()
						{
							console.log('onopen');
						};
						ws.onerror = function ()
						{
							console.log('onerror');
						};
						ws.onclose = function ()
						{
							console.log('onclose');
							StartWS();
						};
						ws.onmessage = StartWS;
						window.MyWS = ws;
					});
				}
				return;
			}

			if (!e || !e.data)
				return;

			if (typeof (e.data) != typeof (e))
			{
				console.log(e.data);
				$('#txt-msg').val(e.data);
				return;
			}

			var t = $('#img-remote').attr('src');
			if (!!t && /^blob:/img.test(t))
				URL.revokeObjectURL(t);
			t = URL.createObjectURL(e.data);
			$('#img-remote').attr('src', t);
		}
	</script>
	<title>RPA 演示</title>
</head>
<body>
	<div>
		<textarea id="txt-msg" style="width: 99%; height: 50px;">Ready</textarea>
	</div>
	<div>
		<input type="button" onclick="javascript: StartWS.call(this);" value="Start(Client)" />
		<input type="button" onclick="javascript: StartWS.call(this);" value="Start(Server)" />
		X:<input type="text" id="txt-x" value="100" style="width: 64px;" maxlength="5" />,
		Y:<input type="text" id="txt-y" value="100" style="width: 64px;" maxlength="5" />,
		<input type="button" onclick="javascript: window.MyWS.send('input=move&x=' + $('#txt-x').val() + '&y=' + $('#txt-y').val());" value="move" />
		<input type="button" onclick="javascript: window.MyWS.send('input=click');" value="click" />
		<input type="button" onclick="javascript: window.MyWS.send('input=dblclick');" value="dblclick" />
		<input type="button" onclick="javascript: window.MyWS.send('ping');" value="保持图片更新需要定时发送 Ping 保活" />
		<input type="button" onclick="javascript: window.MyWS.send('DoWork');" value="DoWork" />
	</div>
	<div>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=48');">0</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=49');">1</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=50');">2</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=51');">3</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=52');">4</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=53');">5</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=54');">6</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=55');">7</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=56');">8</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=57');">9</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=190');">.</a>
	</div>
	<div>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=65');">a</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=66');">b</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=67');">c</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=68');">d</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=69');">e</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=70');">f</a>
		....
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=85');">u</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=86');">v</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=87');">w</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=88');">x</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=89');">y</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=90');">z</a>
	</div>
	<div>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=65&Modifiers=Shift');">A</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=66&Modifiers=Shift');">B</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=67&Modifiers=Shift');">C</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=68&Modifiers=Shift');">D</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=69&Modifiers=Shift');">E</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=70&Modifiers=Shift');">F</a>
		....
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=85&Modifiers=Shift');">U</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=86&Modifiers=Shift');">V</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=87&Modifiers=Shift');">W</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=88&Modifiers=Shift');">X</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=89&Modifiers=Shift');">Y</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=90&Modifiers=Shift');">Z</a>
	</div>
	<div>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=65&Modifiers=Control');">Ctrl+A</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=67&Modifiers=Control');">Ctrl+C</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=88&Modifiers=Control');">Ctrl+X</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=86&Modifiers=Control');">Ctrl+V</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=85&Modifiers=Control');">Ctrl+U</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=89&Modifiers=Control');">Ctrl+Y</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=90&Modifiers=Control');">Ctrl+Z</a>
	</div>
	<div>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=32');">Space</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=160');">LShift</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=162');">LCtrl</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=164');">LAlt</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=13');">Enter</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=8');">Back</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=9');">Tab</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=45');">Insert</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=46');">Delete</a>
	</div>
	<div>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=27');">Esc</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=20');">CapsLock</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=91');">Windows</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=37');">Left</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=38');">Up</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=39');">Right</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=40');">Down</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=93');">Application</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=112');">F1</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=113');">F2</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=122');">F11</a>
		<a href="javascript://" onclick="javascript: window.MyWS.send('input=key&keyvalue=123');">F12</a>
	</div>
	<div><img id="img-remote" src="favicon.ico" alt="Remote Image" onmouseup="javascript: $('#txt-x').val(event.clientX - this.offsetLeft + $(document).scrollLeft());$('#txt-y').val(event.clientY - this.offsetTop + $(document).scrollTop());" /></div>
</body>
</html>