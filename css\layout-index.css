﻿.btnGroup {
	width: 100%;
	text-align: center;
	margin-bottom: 20px;
}

#statusBtn {
	display: none;
	width: 246px;
	text-align: center;
	position: absolute;
	top: 280px;
}

	#statusBtn > div {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 8px;
	}

	#statusBtn .statusName {
		width: 70px;
	}

	.btn {
		display: inline-block;
		width: 80px;
		height: 30px;
		border-radius: 8px;
	}

.btn:hover {
	opacity: 0.8;
}

.btnNornmal {
	background-color: #28a745;
}

.btnFault {
	background-color: #dc3545;
}

.btnIDLE {
	background-color: #ffc107;
}

.btnOffline {
	background-color: #6c757d;
}

.layout {
	width: 100%;
	display: flex;
	height: calc(100vh - 58px);
	justify-content: space-between;
	position: relative;
}

:root {
	--right-width: 248px;
}

#paletteWrap {
	width: 105px;
	height: 100%;
	margin-right: 2px;
	border: 1px solid #ccc;
	background-color: whitesmoke;
	display: none;
}

.nodeInfo-title,
.palette-title {
	height: 30px;
	line-height: 30px;
	margin-bottom: 10px;
	text-align: center;
	background-color: #f2f2f2;
	border-bottom: 1px solid #ccc;
}

#myPaletteDiv {
	width: 103px;
	height: calc(100% - 40px);
	position: relative;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	cursor: auto;
}

	#myPaletteDiv > canvas {
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 2;
		user-select: none;
		width: 100%;
		height: 100%;
		cursor: auto;
	}

#myDiagramDiv {
	flex-grow: 1;
	height: 100%;
	border: 1px solid #ccc;
	position: relative;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	cursor: auto;
}

	#myDiagramDiv > canvas {
		position: absolute;
		top: 0px;
		left: 0px;
		z-index: 2;
		user-select: none;
		width: 100%;
		height: 100%;
		cursor: auto;
	}

#myOverviewDiv {
	width: 246px;
	height: var(--right-width);
	border: 1px solid #ccc;
	position: absolute;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	cursor: auto;
	background-color: #efefef;
}

	#myOverviewDiv > canvas {
		position: absolute;
		bottom: 0px;
		right: 0px;
		z-index: 3;
		user-select: none;
		width: var(--right-width);
		height: var(--right-width);
		cursor: auto;
		bottom: 0;
		right: 0;
	}

#nodeInfo {
	height: calc(100% - 250px);
	border: 1px solid #ccc;
	/* border-bottom: none; */
}

#nodeInfo-form {
	height: calc(100% - 50px);
	overflow-y: auto;
	display: none;
	padding-top: 10px;
}

.layui-form-item {
	margin-bottom: 10px;
}

.layui-form-label {
	width: 80px;
	padding: 8px 8px;
}

#tooltipInfo {
	width: 350px;
	background-color: #fff;
}

	#tooltipInfo .header,
	#tooltipInfo .footer {
		text-align: center;
		background-color: grey;
		font-weight: bolder;
		padding: 5px;
	}

	#tooltipInfo .li-item span {
		display: inline-block;
		padding: 5px;
		text-align: center;
	}

		#tooltipInfo .li-item span:nth-child(1) {
			width: 80px;
			border-right: 1px solid #fff;
		}

		#tooltipInfo .li-item span:nth-child(2) {
			width: calc(100% - 105px);
		}

.li-item-green {
	background-color: #cad7d3;
}

#contextMenu {
	display: none;
	position: absolute;
	background: white;
	border: 1px solid #ccc;
	border-radius: 5px;
	z-index: 9999;
}

	#contextMenu .menu-item {
		padding: 5px 10px;
		border-bottom: 1px solid #ccc;
	}
	#contextMenu .menu-item:last-child {
		padding: 5px 10px;
	}


		#contextMenu .menu-item:hover {
			background: #cccccc;
		}