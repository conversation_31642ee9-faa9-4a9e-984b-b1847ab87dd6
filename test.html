﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title></title>
	<link rel="stylesheet" href="lib/layui/css/layui.css" />
	<link rel="stylesheet" href="css/common.css" />
	<style type="text/css">
		.key {
			margin: 4px;
			font-size: 14px;
			font-weight: bold;
		}

			.key td {
				height: 32px;
				padding: 2px;
			}

			.key .kl, .key .kl td {
				line-height: 4px;
				height: 4px;
			}

			.key .kp, .key .kp td {
				line-height: 1px;
				height: 1px;
			}

			.key td a {
				width: 100%;
				height: 100%;
				margin: 2px;
				display: inline-block;
				white-space: nowrap;
				overflow: hidden;
				border: solid 1px blue;
				line-height: 32px;
				text-align: center;
				vertical-align: middle;
				font-style: normal;
			}

			.key td .kc {
				border: dashed 1px blue;
			}

			.key td .kd {
				border: dotted 1px blue;
			}

			.key td a:hover {
				background-color: lightskyblue;
				color: red;
			}

			.key td a:active {
				background-color: darkblue;
				color: white;
			}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="js/echarts.js?v=1"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript">
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate', 'echarts'], function ()
		{
			$ = layui.$
				, laydate = layui.laydate
				, transfer = layui.transfer
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, echarts = layui.echarts
				, laypage = layui.laypage;
			var data1 = [
				{ name: "2016-4-28 08:03:17", value: ["2016-4-28 08:03:17", 15] },
				{ name: "2016-4-28 08:03:18", value: ["2016-4-28 08:03:18", 15] },
				{ name: "2016-4-28 08:03:19", value: ["2016-4-28 08:03:19", 15] },
				{ name: "2016-4-28 08:03:20", value: ["2016-4-28 08:03:20", 15] },
				{ name: "2016-4-28 08:03:21", value: ["2016-4-28 08:03:21", 15] },
				{ name: "2016-4-28 08:03:22", value: ["2016-4-28 08:03:22", 15] },
				{ name: "2016-4-28 08:03:23", value: ["2016-4-28 08:03:23", 15] },
				{ name: "2016-4-28 08:03:24", value: ["2016-4-28 08:03:24", 15] },
				{ name: "2016-4-28 08:03:25", value: ["2016-4-28 08:03:25", 15] },
				{ name: "2016-4-28 08:03:26", value: ["2016-4-28 08:03:26", 15] },
				{ name: "2016-4-28 08:03:27", value: ["2016-4-28 08:03:27", 15] },
				{ name: "2016-4-28 08:03:28", value: ["2016-4-28 08:03:28", 15] },
				{ name: "2016-4-28 08:03:29", value: ["2016-4-28 08:03:29", 15] }
			];
			var data = [];
			for (i = 0; i < data1.length; i++)
			{
				//data.push(data1[x].name.substring(10,18));
				data.push(data1[i]);
				data[i].name = data1[i].name.substring(10, 18);
				//data[i].value[0]=data1[i].value[0].substring(10,18); //不能设置此行，如果设置此行，导致时间格式有误，会报错
			}
			console.log(data)
			var option = {
				title: {
					text: 'KVM资源使用状态'
				},
				tooltip: {
					trigger: 'axis'
				},
				legend: {
					data: ['CPU使用率', '运行内存使用率', 'CPU温度', '磁盘使用率']
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				toolbox: {
					feature: {
						saveAsImage: {}
					}
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: ['13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00']
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						name: 'CPU使用率',
						type: 'line',
						stack: 'Total',
						data: [33, 33, 33, 33, 33, 33, 33]
					},
					{
						name: '运行内存使用率',
						type: 'line',
						stack: 'Total',
						data: [7, 7, 7, 7, 7, 7, 7]
					},
					{
						name: 'CPU温度',
						type: 'line',
						stack: 'Total',
						data: [25, 25, 25, 25, 25, 25, 25]
					},
					{
						name: '磁盘使用率',
						type: 'line',
						stack: 'Total',
						data: [35, 36, 35, 34, 35, 35, 36]
					}
				]
			};
			//echarts.init(document.getElementById('testBar')).setOption(option);

			window.keys = { 'LChars': "`1234567890-=qwertyuiop[]\\asdfghjkl;'zxcvbnm,./", 'HChars': '~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:"ZXCVBNM<>?', names: [] }
			for (var i = 0; i < 47; i++)
				$('.kp').append('<td><div style="width: 16px;"><br/></div></td>');
			$('.key td a').each(function ()
			{
				var t = $(this).text();
				var i = window.keys.LChars.indexOf(t);
				if (i >= 0 && !$(this).hasClass('kc') && !$(this).hasClass('kd') && this.name != 'Windows' && this.name != 'Menu')
				{
					this.Low = t;
					this.High = window.keys.HChars.substring(i, i + 1);
					this.Case = this.Low + this.High;
				}
				this.keys = window.keys;
				this.keys[this.name] = this;
				this.keys.names.push(this.name);
				$(this).attr('href', '#' + this.name);
			}).click(function ()
			{
				var t = this.name;
				if (t == 'CapsLock')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'lightgreen');
						$(this).data('background-color', $(this).css('background-color'));
					}
					else
					{
						$(this).css('background-color', 'inherit');
					}
				}
				else if (t == 'NumLock')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'lightgreen');
						$(this).data('background-color', $(this).css('background-color'));
					}
					else
					{
						$(this).css('background-color', 'inherit');
					}
				}
				else if (t == 'ScrollLock')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'lightgreen');
						$(this).data('background-color', $(this).css('background-color'));
					}
					else
					{
						$(this).css('background-color', 'inherit');
					}
				}
				else if (t == 'Fn' || t == 'Windows' || t == 'LShift' || t == 'RShift' || t == 'LCtrl' || t == 'RCtrl' || t == 'LAlt' || t == 'RAlt')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'deepskyblue');
						$(this).data('background-color', $(this).css('background-color'));
						$(this).data('shortcut', 1);
						$(this).data(t, 1);
						this.Shortcut = 1;
						this.keys[t + 'State'] = 1;
					}
					else
					{
						$(this).css('background-color', 'inherit');
						$(this).data('shortcut', 0);
						$(this).data(t, 0);
						this.Shortcut = 0;
						this.keys[t + 'State'] = 0;
					}
					if (!this.keys['LShiftState'] && !this.keys['RShiftState'])
					{
						for (var i = this.keys.names.length - 1; i >= 0; i--)
						{
							t = this.keys.names[i];
							var e = this.keys[t];
							if (!!e.Case)
								$(e).text(e.Low);
						}
						this.keys['ShiftSate'] = 0;
					}
					else
					{
						for (var i = this.keys.names.length - 1; i >= 0; i--)
						{
							t = this.keys.names[i];
							var e = this.keys[t];
							if (!!e.Case)
								$(e).text(e.High);
						}
						this.keys['ShiftSate'] = 1;
					}
				}
				else
				{
					for (var i = this.keys.names.length - 1; i >= 0; i--)
					{
						t = this.keys.names[i];
						var e = this.keys[t];
						if (!!e.Case)
							$(e).text(e.Low);
						if (!!e.Shortcut)
						{
							$(e).css('background-color', 'inherit');
							$(e).data('shortcut', 0);
							$(e).data(t, 0);
							e.Shortcut = 0;
							this.keys[t + 'State'] = 0;
						}
					}
					this.keys['ShiftSate'] = 0;
				}
				return false;
			});
		});
	</script>
	<script type="text/javascript">
		function startSnapshotKVM()
		{
			if (!ip)
			{
				var ip = $('#ip').val();
				if (!ip || ip.length < 3)
					return;
			}
			window.imagePlayer = document.getElementById('img-player')
			if (window.imagePlayer != null)
				return;

			if (ip.indexOf(':') < 0)
				ip += ':8080';
			document.body.innerHTML = '<img id="img-player" src="http://' + ip + '/snapshot/0/0.jpg" style="position:absolute;width: 100%;height: 100%;top: 0px;left:0px;right:0px;bottom:0px;" />';
			setInterval(function () { $('#img-player').attr('src', 'http://' + ip + '/snapshot/0/' + Date.now() + '.jpg'); }, 400);
		}
		function startSnapshotWS(ip)
		{
			if (!ip)
			{
				ip = $('#ip').val();
				if (!ip || ip.length < 3)
					return;
			}
			window.imagePlayer = document.getElementById('img-player')
			if (window.imagePlayer == null)
			{
				document.body.innerHTML = '<img id="img-player" src="http://' + window.location.host + '/frame/' + ip + '/0.jpg" style="position:absolute;width: 100%;height: 100%;top: 0px;left:0px;right:0px;bottom:0px;" />';
				arguments.callee.call(this, ip);
				return;
			}

			if (window.frameWS != null)
				return;

			window.frameWS = new WebSocket('ws://' + window.location.host + '/frame/' + ip);
			window.frameWS.binaryType = 'blob';//'arraybuffer';//
			window.frameWS.onopen = function (e)
			{
				window.frameWS.send('ping');
			};
			window.frameWS.onmessage = function (e)
			{
				if (!!$(window.imagePlayer).data('pause'))
					return;

				if (typeof (e.data) != typeof (''))
				{
					//var src = '';
					//var u8s = new Uint8Array(e.data);
					//for (var i = 0; i < u8s.length / 8192; i++)
					//	src += String.fromCharCode.apply(null, u8s.slice(i * 8192, i * 8192 + 8192));
					//src = window.btoa(src);
					//img.src = 'data:image/jpg;base64,' + src;
					var src = window.imagePlayer.src;
					window.imagePlayer.src = URL.createObjectURL(e.data);
					if (/^blob:/img.test(src))
						URL.revokeObjectURL(src);
				}
				window.frameWS.send('ping');
			};
			window.frameWS.onclose = function (e)
			{
				window.frameWS = null;
				startSnapshotWS();
			};
			window.frameWS.onerror = function (e)
			{

			};
			//window.btoa();
		}
	</script>
	<script type="text/javascript" id="function notebook()">
		function notebook(v)
		{
			var ajax = {};
			if (!window.CS || !window.CS.Settings || !window.CS.Settings.NFS)
				ajax.href = '../file/notebook/test.txt';
			else
				ajax.href = '../file/(NFS)/notebook/test.txt';
			ajax.src = ajax.href;
			ajax.url = ajax.href;
			if (!!v)
			{
				ajax.data = 'Test notebook, date = ' + new Date();
				ajax.type = 'POST';
			}
			ajax.success = function (result)
			{
				$('#p').text(result);
			};
			$.ajax(ajax);
		}
	</script>

</head>
<body>
	<div style="color: lightgreen">
		<input type="text" id="ip" name="ip" value="**************" />
		<a href="javascript: startSnapshotKVM('');">StartSnapshotKVM</a>
		<a href="javascript: startSnapshotWS('');">StartSnapshotWS</a>
		--
		<input type="button" value="notebook Save" onclick="javascript: notebook.call(this, 1);" />
		<input type="button" value="notebook Read" onclick="javascript: notebook.call(this, 0);" />
	</div>
	<table id="keypad" cellpadding="0" cellspacing="0" border="0" class="key" onselectstart="return false;" onmousedown="return false;">
		<tr class="kp"></tr>
		<tr>
			<td colspan="2"><a name="Esc">Esc</a></td>
			<td><br /></td>
			<td colspan="2"><a name="F1">F1</a></td>
			<td colspan="2"><a name="F2">F2</a></td>
			<td colspan="2"><a name="F3">F3</a></td>
			<td colspan="2"><a name="F4">F4</a></td>
			<td colspan="2"><br /></td>
			<td colspan="2"><a name="F5">F5</a></td>
			<td colspan="2"><a name="F6">F6</a></td>
			<td colspan="2"><a name="F7">F7</a></td>
			<td colspan="2"><a name="F8">F8</a></td>
			<td colspan="2"><br /></td>
			<td colspan="2"><a name="F9">F9</a></td>
			<td colspan="2"><a name="F10">F10</a></td>
			<td colspan="2"><a name="F11">F11</a></td>
			<td colspan="2"><a name="F12">F12</a></td>
			<td><br /></td>
			<td colspan="2"><a name="PrintScreen" class="kc" style="font-family: Wingdings; font-size: 20px;" title="Print Screen">v</a></td>
			<td colspan="2"><a name="ScrollLock" class="kc" style="font-family: Wingdings; font-size: 20px;" title="Scroll Lock">5</a></td>
			<td colspan="2"><a name="PauseBreak" class="kc" style="font-family: Wingdings; font-size: 20px;" title="Pause / Break">I</a></td>
			<td colspan="9"><br /></td>
		</tr>
		<tr class="kp"></tr>
		<tr>
			<td colspan="2"><a name="Tilde">`</a></td>
			<td colspan="2"><a name="D1">1</a></td>
			<td colspan="2"><a name="D2">2</a></td>
			<td colspan="2"><a name="D3">3</a></td>
			<td colspan="2"><a name="D4">4</a></td>
			<td colspan="2"><a name="D5">5</a></td>
			<td colspan="2"><a name="D6">6</a></td>
			<td colspan="2"><a name="D7">7</a></td>
			<td colspan="2"><a name="D8">8</a></td>
			<td colspan="2"><a name="D9">9</a></td>
			<td colspan="2"><a name="D0">0</a></td>
			<td colspan="2"><a name="Minus">-</a></td>
			<td colspan="2"><a name="Plus">=</a></td>
			<td colspan="5"><a name="Backspace">Backspace</a></td>
			<td><br /></td>
			<td colspan="2"><a name="Insert" title="Inster" class="kc">Ins</a></td>
			<td colspan="2"><a name="Home" title="Honm" class="kc" style="font-size: 12px;">Home</a></td>
			<td colspan="2"><a name="PageUp" title="Page Up" class="kc" style="font-size: 12px;">PgUp</a></td>
			<td><br /></td>
			<td colspan="2"><a name="NumLock" class="kd" title="Number Lock" style="font-size: 12px;">NmLk</a></td>
			<td colspan="2"><a name="Division" class="kd">/</a></td>
			<td colspan="2"><a name="Asterisk" class="kd">*</a></td>
			<td colspan="2"><a name="Negative" class="kd">-</a></td>
		</tr>
		<tr>
			<td colspan="3"><a name="Tab">Tab</a></td>
			<td colspan="2"><a name="Q">a</a></td>
			<td colspan="2"><a name="W">w</a></td>
			<td colspan="2"><a name="E">e</a></td>
			<td colspan="2"><a name="R">r</a></td>
			<td colspan="2"><a name="T">t</a></td>
			<td colspan="2"><a name="Y">y</a></td>
			<td colspan="2"><a name="U">u</a></td>
			<td colspan="2"><a name="I">i</a></td>
			<td colspan="2"><a name="O">o</a></td>
			<td colspan="2"><a name="P">p</a></td>
			<td colspan="2"><a name="LSB">[</a></td>
			<td colspan="2"><a name="RSB">]</a></td>
			<td colspan="4"><a name="Backslash">\</a></td>
			<td><br /></td>
			<td colspan="2"><a name="Delete" class="kc" title="Delete">Del</a></td>
			<td colspan="2"><a name="End" class="kc" title="End">End</a></td>
			<td colspan="2"><a name="PageDown" class="kc" title="Page Down" style="font-size: 12px;">PgDn</a></td>
			<td><br /></td>
			<td colspan="2"><a name="N7" class="kd">7</a></td>
			<td colspan="2"><a name="N8" class="kd">8</a></td>
			<td colspan="2"><a name="N9" class="kd">9</a></td>
			<td colspan="2" rowspan="2"><a name="Positive" class="kd">+</a></td>
		</tr>
		<tr>
			<td colspan="4"><a name="CapsLock">CapsLock</a></td>
			<td colspan="2"><a name="A">a</a></td>
			<td colspan="2"><a name="S">s</a></td>
			<td colspan="2"><a name="D">d</a></td>
			<td colspan="2"><a name="F">f</a></td>
			<td colspan="2"><a name="G">g</a></td>
			<td colspan="2"><a name="H">h</a></td>
			<td colspan="2"><a name="J">j</a></td>
			<td colspan="2"><a name="K">k</a></td>
			<td colspan="2"><a name="L">l</a></td>
			<td colspan="2"><a name="Colon">;</a></td>
			<td colspan="2"><a name="Quotation">'</a></td>
			<td colspan="5"><a name="Enter">Enter</a></td>
			<td colspan="8"><br /></td>
			<td colspan="2"><a name="N4" class="kd">4</a></td>
			<td colspan="2"><a name="N5" class="kd">5</a></td>
			<td colspan="2"><a name="N6" class="kd">6</a></td>
		</tr>
		<tr>
			<td colspan="5"><a name="LShift">Shift</a></td>
			<td colspan="2"><a name="Z">z</a></td>
			<td colspan="2"><a name="X">x</a></td>
			<td colspan="2"><a name="C">c</a></td>
			<td colspan="2"><a name="V">v</a></td>
			<td colspan="2"><a name="B">b</a></td>
			<td colspan="2"><a name="N">n</a></td>
			<td colspan="2"><a name="M">m</a></td>
			<td colspan="2"><a name="Comma">,</a></td>
			<td colspan="2"><a name="Period">.</a></td>
			<td colspan="2"><a name="Question">/</a></td>
			<td colspan="6"><a name="RShift">Shift</a></td>
			<td colspan="3"><br /></td>
			<td colspan="2"><a name="Up" class="kc" style="font-family: Webdings; font-size: 20px;" title="Up">5</a></td>
			<td colspan="3"><br /></td>
			<td colspan="2"><a name="N1" class="kd">1</a></td>
			<td colspan="2"><a name="N2" class="kd">2</a></td>
			<td colspan="2"><a name="N3" class="kd">3</a></td>
			<td colspan="2" rowspan="2"><a name="NewLine" class="kd">Enter</a></td>
		</tr>
		<tr>
			<td colspan="2"><a name="Fn">Fn</a></td>
			<td colspan="4"><a name="LCtrl">Ctrl</a></td>
			<td colspan="2"><a name="Windows" style="font-family: Wingdings; font-size: 20px;" title="Windows">z</a></td>
			<td colspan="2"><a name="LAlt">Alt</a></td>
			<td colspan="14"><a name="Space">Space</a></td>
			<td colspan="2"><a name="RAlt">Alt</a></td>
			<td colspan="2"><a name="Menu" style="font-family: Wingdings; font-size: 20px;" title="Menu">2</a></td>
			<td colspan="3"><a name="RCtrl">Ctrl</a></td>
			<td><br /></td>
			<td colspan="2"><a name="Left" class="kc" style="font-family: Webdings; font-size: 20px;" title="Left">3</a></td>
			<td colspan="2"><a name="Down" class="kc" style="font-family: Webdings; font-size: 20px;" title="Down">6</a></td>
			<td colspan="2"><a name="Right" class="kc" style="font-family: Webdings; font-size: 20px;" title="Right">4</a></td>
			<td><br /></td>
			<td colspan="4"><a name="N0" class="kd">0</a></td>
			<td colspan="2"><a name="Point" class="kd">.</a></td>
		</tr>
		<tr class="kp"></tr>
	</table>
	<p id="p"><br /></p>
	

	<form class="layui-form" lay-filter="StatisticForm">
		<div class="layui-form-item" style="background-color: gray;">
			<fieldset class="layui-elem-field">
				<legend><span>筛选数据</span></legend>
				<div class="layui-field-box" style="text-align:center;">
					<div id="testBar" style="width:600px;height:600px;">

					</div>
				</div>
			</fieldset>
		</div>
	</form>
</body>
</html>