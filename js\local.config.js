﻿window["local.config"] = {
	"AssemblyName": "rcs", "AssemblyTitle": "北京珂阳智能远程控制管理系统（iRCM）——通用客户端", "AssemblyGuid": "41A635EF-211C-4CA7-BDA9-FC9D9174DFC2", "AssemblyProduct": "RemoteControlSystem", "AssemblyVersion": "124.************", "ProductVersion": "124.************", "ApplicationName": "rcs", "ApplicationData": "D:\\RCM.data", "ExecutablePath": "D:\\Projects\\iRCM\\RCS-Client-D\\rcs.exe", "MachineName": "DESKTOP-FN4LG6V", "StaticId": "676e6f48-6853-6a69-696e-bae9caaebdef", "UniqueId": "b8f6287c-8862-40dd-a01b-3e77c04c6192", "StartTime": new Date(1728872619671.99), "RemoteEndPoint": "127.0.0.1:57179","WebPoint": 7822,"NetPoint": 7820,"Host": "0.0.0.0","Guid": "0805ef5d2efb480c9e9eb5159858028d",
"Headers": {" ":null,"sec-ch-ua": "Chromium;v=128, Not;A=Brand;v=24, Microsoft Edge;v=128","sec-ch-ua-mobile": "?0","sec-ch-ua-platform": "Windows","Upgrade-Insecure-Requests": "1","Sec-Fetch-Site": "none","Sec-Fetch-Mode": "navigate","Sec-Fetch-User": "?1","Sec-Fetch-Dest": "document","Connection": "keep-alive","Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding": "gzip, deflate, br, zstd","Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6","Cookie": "Cowin-SID=1AA8F2DDC78649F6A54FFC27C8ACF7A1; UniqueId=486b700578dc4117993ab3274b9b317c","Host": "127.0.0.1:7822","If-None-Match": "1728718225235","User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}};
window["rcs.config"]=window["local.config"];
window["RCS"]=window["local.config"];
window["CS"]=window["local.config"];
window["NAURA"]=window["local.config"];
window["Plugs"]=window["CS"]["Plugs"]={"$:type":"RCS.Client.Lite.MyPlugs","ProductTitle": "北京珂阳智能远程控制管理系统（iRCM）——通用客户端","User": {"ID":"","Name":"","Alive":"1AA8F2DDC78649F6A54FFC27C8ACF7A1","Title":"","Serial":"","Type":"","Role":"","Dept":"","AuthenticationType":"KY_1AA8F2DDC78649F6A54FFC27C8ACF7A1","Permission":"","Expire":"0","Last":"0","Edit":"0","Theme":"","$:type":"User"},"DBSupported": "Yes","Realm": "SEMI","LicenceKey": "APvkUerhXuFjweGF6UnEaL6kFVUTTTxeBTOon9J2pnlPCLVlpzwkDYjILvKRAZZbjg==","Customer": "NAURA"};
window["local.config"]["NAURA"]=window["local.config"]["Plugs"];
//1728872620683
window["Settings"]=window["CS"]["Settings"]={"$:type":"PickGold.EnvironmentInitializer+SettingCollection","user/cowin": "39BB37CF36D3B29A9280D8A70A0EED42,F6A0B86733A9C2849D67047959516574,EC1A5DC4EEDD187696224B9DB22711CF,AE33F60E64D1E43914D4C32B7777A9A8","SSO": "Y","Logger": "%DataPath%\\Logs\\{0:dd}.log","LoggerDB": "%DataPath%\\Logs\\db{0:dd}.log","LoggerWeb": "%DataPath%\\Logs\\web{0:dd}.log","VideoRecordPath": "%DataPath%\\Snapshot\\{Authority}\\{w}\\{Time,,-1:HHmm}.mp4","Http80": "1","Server": ".","Server/udp": ".","AlarmList": "READER_STATE=304:Modbus Error&Test=code:Note","AlarmTask": "True","TaskVideo": "True","TaskAlarm": "GYF-Desktop","AlarmNotify": "%DataPath%\\NotifyAlarm","Alarm/Test": "http://com.cn/ams TagID=${ITEM_MARKER}","Alarm/*": "http://localhost:7822/CallAlarm.do TagID=${ITEM_MARKER}&Type={ITEM_TYPE}","Schedule/TaskForStorehouse": "second=8&data=http://*/WatchDog?","ClientSettingsProvider.ServiceUri": "","DataDirectory": "D:\\RCM.data","Customer": "NAURA","DESKTOP-FN4LG6V": "Cowin-HSJ-TAP","user/ldap": "ldap:39BB37CF36D3B29A9280D8A70A0EED42,F6A0B86733A9C2849D67047959516574,EC1A5DC4EEDD187696224B9DB22711CF,AE33F60E64D1E43914D4C32B7777A9A8","VideoHub": "**************","VideoPort": "110003","VideoMode": "2","JobHikvision": "True","VncHub": "**************:7069","calc": "calc.exe"};
window["Variables"]=window["CS"]["Variables"]={"$":window["$"],"AssemblyVersion": "124.************","Server": ".","calc": "calc.exe","StaticId": "676e6f4868536a69696ebae9caaebdef","AssemblyName": "rcs","Cookies": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\INetCookies","Path": "C:\\Program Files (x86)\\Intel\\Intel(R) Management Engine Components\\iCLS\\;C:\\Program Files\\Intel\\Intel(R) Management Engine Components\\iCLS\\;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\Intel\\Intel(R) Management Engine Components\\DAL;C:\\Program Files\\Intel\\Intel(R) Management Engine Components\\DAL;C:\\Program Files\\Microsoft SQL Server\\130\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\TortoiseSVN\\bin;C:\\Program Files\\TortoiseGit\\bin;C:\\Program Files\\dotnet\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;","_0": "D:\\Projects\\iRCM\\RCS-Client-D\\rcs.exe","LocalUrl": "http://0.0.0.0:7822/","Favorites": "D:\\Users\\HongShijin\\Favorites","Customer": "NAURA","VSAPPIDNAME": "devenv.exe","Schedule/TaskForStorehouse": "second=8&data=http://*/WatchDog?","DESKTOP-FN4LG6V": "Cowin-HSJ-TAP","AlarmNotify": "%DataPath%\\NotifyAlarm","ThreadedWaitDialogDpiContext": "-4","ProgramData": "C:\\ProgramData","LoggerDB": "%DataPath%\\Logs\\db{0:dd}.log","VSSKUEDITION": "Enterprise","VideoHub": "**************","SystemX86": "C:\\Windows\\SysWOW64","FPS_BROWSER_USER_PROFILE_STRING": "Default","VisualStudioDir": "D:\\Users\\HongShijin\\Documents\\Visual Studio 2019","OneDriveConsumer": "C:\\Users\\<USER>\\OneDrive","CommonVideos": "C:\\Users\\<USER>\\Videos","NanUI": "0","ComSpec": "C:\\Windows\\system32\\cmd.exe","NetPoint": "7820/7822","DataPath": "D:\\RCM.data","AssemblyGuid": "41A635EF-211C-4CA7-BDA9-FC9D9174DFC2","PROCESSOR_REVISION": "9e0a","SendTo": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\SendTo","COMPLUS_NoGuiFromShim": "1","AssemblyDescription": "北京珂阳智能远程控制管理系统（iRCM）——通用客户端\r\n研发项目：北京珂阳智能远程控制管理系统（iRCM）\r\n技术支持：北京珂阳科技有限公司","Personal": "D:\\Users\\HongShijin\\Documents","TaskAlarm": "GYF-Desktop","IIS_USER_HOME": "D:\\Users\\HongShijin\\Documents\\IISExpress","VisualStudioVersion": "16.0","ProgramFiles(x86)": "C:\\Program Files (x86)","Windows": "C:\\Windows","LocalApplicationData": "C:\\Users\\<USER>\\AppData\\Local","Logger": "%DataPath%\\Logs\\{0:dd}.log","SSO": "Y","ProgramW6432": "C:\\Program Files","CommonStartMenu": "C:\\ProgramData\\Microsoft\\Windows\\Start Menu","PROCESSOR_LEVEL": "6","VideoPort": "110003","USERNAME": "HongShijin","AssemblyCompany": "北京珂阳科技有限公司","NUMBER_OF_PROCESSORS": "12","SESSIONNAME": "RDP-Tcp#193","VSAPPIDDIR": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Enterprise\\Common7\\IDE\\","TaskVideo": "True","JobHikvision": "True","Templates": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Templates","CommonProgramFilesX86": "C:\\Program Files (x86)\\Common Files","USERPROFILE": "C:\\Users\\<USER>\\Program Files (x86)\\IIS Express","USERDOMAIN_ROAMINGPROFILE": "DESKTOP-FN4LG6V","Fonts": "C:\\Windows\\Fonts","VSLANG": "2052","CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files","PkgDefApplicationConfigFile": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\VisualStudio\\16.0_121f1f5e\\devenv.exe.config","IIS_DRIVE": "C:","CommonTemplates": "C:\\ProgramData\\Microsoft\\Windows\\Templates","BasePath": "D:\\Projects\\iRCM\\RCS-Client-D\\","CefSharp": "0","ENABLE_XAML_DIAGNOSTICS_SOURCE_INFO": "1","COMPUTERNAME": "DESKTOP-FN4LG6V","ProgramFilesX86": "C:\\Program Files (x86)","FugueRepository": "C:\\PROGRAM FILES (X86)\\MICROSOFT VISUAL STUDIO\\2019\\ENTERPRISE\\TEAM TOOLS\\STATIC ANALYSIS TOOLS\\FxCop\\Repository","StartPath": "D:\\Projects\\iRCM\\RCS-Client-D","LocalizedResources": "C:\\Windows\\resources\\0804","LOGONSERVER": "\\\\DESKTOP-FN4LG6V","HOMEDRIVE": "C:","OneDrive": "C:\\Users\\<USER>\\OneDrive","CommonDesktopDirectory": "C:\\Users\\<USER>\\Desktop","SlaveUrl": "http://127.0.0.1:7823/.292312/","MSBuildLoadMicrosoftTargetsReadOnly": "true","DevPath": "D:\\Projects\\iRCM\\RCS.Client","DesktopDirectory": "D:\\Users\\HongShijin\\Desktop","Recent": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Recent","Server/udp": ".","CommandLine": "D:\\Projects\\iRCM\\RCS-Client-D\\rcs.exe www.baidu.com.0","PUBLIC": "C:\\Users\\<USER>\\Users\\HongShijin\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs","_1": "www.baidu.com.0","OS": "Windows_NT","ApplicationName": "rcs","windir": "C:\\Windows","VideoRecordPath": "%DataPath%\\Snapshot\\{Authority}\\{w}\\{Time,,-1:HHmm}.mp4","Desktop": "D:\\Users\\HongShijin\\Desktop","ForceIdentityAuthenticationType": "Embedded","PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 158 Stepping 10, GenuineIntel","CommonStartup": "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\Startup","APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming","HOMEPATH": "\\Users\\HongShijin","CommonOemLinks": "C:\\ProgramData\\OEM Links","Http80": "1","TMP": "C:\\Windows\\Temp","SystemDrive": "C:","AssemblyTitle": "北京珂阳智能远程控制管理系统（iRCM）——通用客户端","SignInWithHomeTenantOnly": "False","PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","PrinterShortcuts": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Printer Shortcuts","LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local","ServiceHubLogSessionKey": "3331A4EE","StartTime": "2024-10-14 10:23:39.671","user/cowin": "39BB37CF36D3B29A9280D8A70A0EED42,F6A0B86733A9C2849D67047959516574,EC1A5DC4EEDD187696224B9DB22711CF,AE33F60E64D1E43914D4C32B7777A9A8","AssemblyProduct": "RemoteControlSystem","InternetCache": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\INetCache","ZES_ENABLE_SYSMAN": "1","CLIENTNAME": "DESKTOP-L99QQD8","CommonPictures": "C:\\Users\\<USER>\\Pictures","History": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\History","CommonMusic": "C:\\Users\\<USER>\\Music","LoggerWeb": "%DataPath%\\Logs\\web{0:dd}.log","AdminTools": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Administrative Tools","ProductVersion": "124.************","DataDirectory": "D:\\RCM.data","AlarmList": "READER_STATE=304:Modbus Error&Test=code:Note","CommonApplicationData": "C:\\ProgramData","Resources": "C:\\Windows\\resources","_PTVS_PID": "89796","Alarm/Test": "http://com.cn/ams TagID=${ITEM_MARKER}","MyPictures": "D:\\Users\\HongShijin\\Pictures","CDBurning": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\Burn\\Burn","UniqueId": "b8f6287c886240dda01b3e77c04c6192","MyMusic": "D:\\Users\\HongShijin\\Music","CommonPrograms": "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs","ProgramFiles": "C:\\Program Files","DriverData": "C:\\Windows\\System32\\Drivers\\DriverData","VncHub": "**************:7069","FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer","IIS_SITES_HOME": "D:\\Users\\HongShijin\\Documents\\My Web Sites","AlarmTask": "True","ApplicationData": "C:\\Users\\<USER>\\AppData\\Roaming","PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules","USERDOMAIN": "DESKTOP-FN4LG6V","CommonProgramW6432": "C:\\Program Files\\Common Files","Startup": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup","Process": "292312","TEMP": "C:\\Windows\\Temp","NetworkShortcuts": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Network Shortcuts","ALLUSERSPROFILE": "C:\\ProgramData","VideoMode": "2","CommonAdminTools": "C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\Administrative Tools","MyVideos": "D:\\Users\\HongShijin\\Videos","System": "C:\\Windows\\system32","CommonProgramFiles": "C:\\Program Files\\Common Files","PROCESSOR_ARCHITECTURE": "AMD64","StartMenu": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu","AssemblyCopyright": "Copyright © Cowin 2035","CommonDocuments": "C:\\Users\\<USER>\\Documents","HomeUrl": "http://127.0.0.1:7822/","SystemRoot": "C:\\Windows","CurrentDirectory": "D:\\Projects\\iRCM\\RCS-Client-D","_NO_DEBUG_HEAP": "1","VisualStudioEdition": "Microsoft Visual Studio Enterprise 2019","VSLOGGER_UNIQUEID": "d5bd8785d4a04e1280c1c84b503ec5e6","user/ldap": "ldap:39BB37CF36D3B29A9280D8A70A0EED42,F6A0B86733A9C2849D67047959516574,EC1A5DC4EEDD187696224B9DB22711CF,AE33F60E64D1E43914D4C32B7777A9A8","MyDocuments": "D:\\Users\\HongShijin\\Documents"};
window["BaseDB"]=window["RCS.Api.BaseDB"]=window["CS"]["BaseDB"]={"FLAG_NORMAL": 0,"FLAG_READONLY": 1,"FLAG_HIDDEN": 2,"FLAG_SYSTEM": 4,"FLAG_VOLUME": 8,"FLAG_DIRECTORY": 16,"FLAG_ARCHIVE": 32,"FLAG_ALIAS": 64,"FLAG_COMPRESSED": 128,"FLAG_INHERIT": 256,"FLAG_MASTER": 16777216,"FLAG_SLAVE": 33554432,"FLAG_OFFLINE": 536870912,"FLAG_INVALID": 1073741824,"FLAG_DELETED": -1,"STATUS_NORMAL": 0,"STATUS_SUCCESS": 1,"STATUS_WORKING": 2,"STATUS_ONLINE": 1024,"STATUS_TIMEOUT": 2048,"STATUS_FINISH": 4096,"STATUS_CANCEL": 8192,"STATUS_RECYCLE": 16384,"STATUS_INVALID": 32768,"STATUS_ERROR": 65536,"STATUS_DELETED": -1,"ID_SIZE": 32,"MENU_SIZE": 32,"DATA_SIZE": 4000,"ZERO_ID": "00000000000000000000000000000000","FULL_ID": "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF","SAVE": "SAVE","ALARM": "ALARM","AUTH": "AUTH","CONFIG": "CONFIG","EXTRA": "EXTRA","VIDEO": "VIDEO","LOG": "LOG","STATISTIC": "STATISTIC","DEPT": "DEPT","USER": "USER","MACHINE": "MACHINE","EQP": "EQP","AREA": "AREA","ITEM_ID": "ITEM_ID","ITEM_NAME": "ITEM_NAME","ITEM_PARENT": "ITEM_PARENT","ITEM_TYPE": "ITEM_TYPE","ITEM_TITLE": "ITEM_TITLE","ITEM_SERIAL": "ITEM_SERIAL","ITEM_THEME": "ITEM_THEME","ITEM_LEVEL": "ITEM_LEVEL","ITEM_DEPT": "ITEM_DEPT","ITEM_USER": "ITEM_USER","ITEM_ROLE": "ITEM_ROLE","ITEM_VIDEO": "ITEM_VIDEO","ITEM_ALARM": "ITEM_ALARM","ITEM_STATISTIC": "ITEM_STATISTIC","ITEM_AUTH": "ITEM_AUTH","ITEM_LAYOUT": "ITEM_LAYOUT","ITEM_MACHINE": "ITEM_MACHINE","ITEM_EQP": "ITEM_EQP","ITEM_AREA": "ITEM_AREA","ITEM_UNIT": "ITEM_UNIT","ITEM_LANDMARK": "ITEM_LANDMARK","ITEM_MARKER": "ITEM_MARKER","ITEM_LOCALE": "ITEM_LOCALE","ITEM_PATH": "ITEM_PATH","ITEM_HREF": "ITEM_HREF","ITEM_PROXY": "ITEM_PROXY","ITEM_REVIEW": "ITEM_REVIEW","ITEM_URL": "ITEM_URL","ITEM_JOB": "ITEM_JOB","ITEM_ACT": "ITEM_ACT","ITEM_ALIVE": "ITEM_ALIVE","ITEM_IDLE": "ITEM_IDLE","ITEM_BUSY": "ITEM_BUSY","ITEM_REPAIR": "ITEM_REPAIR","ITEM_TROUBLE": "ITEM_TROUBLE","ITEM_LOST": "ITEM_LOST","ITEM_OTHER": "ITEM_OTHER","ITEM_CIPHER": "ITEM_CIPHER","ITEM_ACCOUNT": "ITEM_ACCOUNT","ITEM_PROTOCOL": "ITEM_PROTOCOL","ITEM_HARDWARE": "ITEM_HARDWARE","ITEM_FEATURE": "ITEM_FEATURE","ITEM_MULTI": "ITEM_MULTI","ITEM_SERVER": "ITEM_SERVER","ITEM_PING": "ITEM_PING","ITEM_PONG": "ITEM_PONG","ITEM_SWEEPER": "ITEM_SWEEPER","ITEM_SWEEP": "ITEM_SWEEP","ITEM_SWEEPED": "ITEM_SWEEPED","ITEM_WATCHER": "ITEM_WATCHER","ITEM_WATCH": "ITEM_WATCH","ITEM_WATCHED": "ITEM_WATCHED","ITEM_START": "ITEM_START","ITEM_VALID": "ITEM_VALID","ITEM_LAST": "ITEM_LAST","ITEM_EXPIRE": "ITEM_EXPIRE","ITEM_RESULT": "ITEM_RESULT","ITEM_STATUS": "ITEM_STATUS","ITEM_DATA": "ITEM_DATA","ITEM_DATA1": "ITEM_DATA1","ITEM_DATA2": "ITEM_DATA2","ITEM_DATA3": "ITEM_DATA3","ITEM_DATA4": "ITEM_DATA4","ITEM_DATA5": "ITEM_DATA5","ITEM_DATA6": "ITEM_DATA6","ITEM_DATA7": "ITEM_DATA7","ITEM_DATA8": "ITEM_DATA8","ITEM_DATA9": "ITEM_DATA9","ITEM_VALUE": "ITEM_VALUE","ITEM_VALUE1": "ITEM_VALUE1","ITEM_VALUE2": "ITEM_VALUE2","ITEM_VALUE3": "ITEM_VALUE3","ITEM_VALUE4": "ITEM_VALUE4","ITEM_VALUE5": "ITEM_VALUE5","ITEM_VALUE6": "ITEM_VALUE6","ITEM_VALUE7": "ITEM_VALUE7","ITEM_VALUE8": "ITEM_VALUE8","ITEM_VALUE9": "ITEM_VALUE9","ITEM_FLAGS": "ITEM_FLAGS","ITEM_EDIT": "ITEM_EDIT","ITEM_TIME": "ITEM_TIME","ITEM_MEMO": "ITEM_MEMO","ROW_KEY": "ROW_KEY","ROW_ID": "ROW_ID","METHOD_URL": ":Url/","METHOD_LOG": ":Log/","METHOD_GET": ":Get/","METHOD_SET": ":Set/","METHOD_REGEX": ":Regex","METHOD_INDEX": ":Index/","METHOD_PREFIX": ":Prefix","PREFIX_COLUMN": "Column-","PREFIX_SELECT": "Select-","PREFIX_INSERT": "Insert-","PREFIX_UPDATE": "Update-","PREFIX_DELETE": "Delete-","PREFIX_WHERE": "Where-","PREFIX_NOT": "Not-","PREFIX_CUT": "-","$:type":"RCS.Api.BaseDB"};
window["UriScheme"]=window["RCS.Api.UriScheme"]=window["CS"]["UriScheme"]={"MC": "MC","HK": "HK","PW": "PW","SNV": "SNV","PSC": "PSC","VNC": "VNC","VNCL": "VNCL","VNCR": "VNCR","ATEN": "ATEN","NQVM": "NQVM","ONVIF": "ONVIF","RTSP": "RTSP","Oust": "Oust","Lock": "Lock","Pilot": "Pilot","Alarm": "Alarm","State": "State","Login": "Login","Sweep": "Sweep","Mutil": "Mutil","Robot": "Robot","Delimiter": "://","$:type":"RCS.Api.UriScheme"};
window["States"]=window["RCS.Api.States"]=window["CS"]["States"]={"$No": "No","No": 0,"$Yes": "Yes","Yes": 1,"$Unknow": "Unknow","Unknow": 2,"$:type":"RCS.Api.States"};
window["ItemFlags"]=window["RCS.Api.ItemFlags"]=window["CS"]["ItemFlags"]={"$Normal": "Normal","Normal": 0,"$Readonly": "Readonly","Readonly": 1,"$Hidden": "Hidden","Hidden": 2,"$System": "System","System": 4,"$Volume": "Volume","Volume": 8,"$Directory": "Directory","Directory": 16,"$Archive": "Archive","Archive": 32,"$Alias": "Alias","Alias": 64,"$Compressed": "Compressed","Compressed": 128,"$Inherit": "Inherit","Inherit": 256,"$Offline": "Offline","Offline": 536870912,"$Invalid": "Invalid","Invalid": 1073741824,"$:type":"RCS.Api.ItemFlags"};
window["ExtraData"]=window["RCS.Api.ExtraData"]=window["CS"]["ExtraData"]={"SESSION": "Cowin-SID","TYPE_EXTRA": "TYPE_EXTRA","TYPE_VIDEO_DEVICE": "VIDEO_DEVICE","TYPE_VIDEO_SWEEPER": "VIDEO_SWEEPER","TYPE_VIDEO_EXTRADATA": "VIDEO_EXTRADATA","TYPE_VIDEO_LOCK": "VIDEO_LOCK","TYPE_VIDEO_RECORD": "VIDEO_RECORD","TYPE_VIDEO_SNAPSHOT": "VIDEO_SNAPSHOT","TYPE_DEVICE_ORDER": "DeviceOrder","STATUS_NORMAL": 0,"STATUS_WAITING": 1,"STATUS_WORKING": 2,"STATUS_CANCEL": 8192,"STATUS_INVALID": 32768,"SIMPLE_LOCK": 1,"STRICT_LOCK": 2,"$:type":"RCS.Api.ExtraData"};
window["ConfigData"]=window["RCS.Api.ConfigData"]=window["CS"]["ConfigData"]={"ExportLimit": 2000,"NetSuccessTimes": 60000,"SSO": true,"WatchSDK": 0,"VideoRecordTool": "D:\\Projects\\iRCM\\RCS-Client-D\\rcsvt.exe","VideoRecordArgs": "-f image2pipe -use_wallclock_as_timestamps 1 -i - -c:v libx264 -pix_fmt yuv420p -fps_mode passthrough -y \"{0}\"","VideoRecordPath": "D:\\RCM.data\\Snapshot\\{Authority}\\{w}\\{Time,,-1:HHmm}.mp4","VideoRecordWay": "N","VideoRecordFPS": 10,"MCAccount": "F2AF9F701A39E509BF7BBDF53BB443A5F9F167D2:A2F9C12D410FE509BF7BBDF53BB443A5F9F167D2","HKAccount": "admin:cowin123","AtendAccount": "administrator:123456","LoginTimeout": 600000,"IdleTimeout": 600000,"JobDelay": 256,"JobWorker": 5,"ClientWorker": 0,"OpenVideo": 4,"VideoHub": "**************","VideoMode": 2,"VideoPort": 110003,"ImagePort": 8080,"CtrlPort": 8001,"MatrixDelay": 384,"ClientPlatform": "","WorkUltra": false,"WorkMinimalism": false,"WebEngine": 0,"Robot": "","AlarmTarget": "","Customer": "NAURA","Charset": "System.Text.DBCSCodePageEncoding","HttpPool": 0,"WebRoot": "/","USER_COWIN": "user/cowin","USER": "user/","ROLE": "role/","TYPE_ALARM": "Alarm","TYPE_AREA": "Area","TYPE_LAYOUT": "Layout","TYPE_UPGRADE": "Upgrade","TYPE_FAVORITES": "favorties","TYPE_TOOLGROUP": "toolgroup","TYPE_ROLE": "Role","TYPE_MAIL": "Mail","TYPE_ROLE_AUDIO": "roleaduio","TYPE_NOTICE": "notice","TYPE_UNIT": "unit","FACTORY": "FACTORY/","UNIT": "UNIT/","STATISTIC": "STATISTIC/","LDAP": "LDAP","DataSmtpHost": "SmtpHost","DataSmtpPort": "SmtpPort","DataSmtpUser": "SmtpUser","DataSmtpPassword": "SmtpPassword","DataMailFrom": "MailFrom","DataMailSubject": "MailSubject","DataMailBody": "MailBody","DataMailRecipients": "MailRecipients","DataLayoutStyle": "Style","DataLayoutTop": "Top","DataLayoutLeft": "Left","DataLayoutSize": "Size","PIPELINE": "PIPELINE/","PACKAGES_TYLE": "PACKAGES_TYLE/","MILL": "MILL/","RECYCLE": "RECYCLE/","STATION_TYPE": "STATION_TYPE/","SAFETY_COUNT": "SAFETY_COUNT","ACTUAL_COUNT": "ACTUAL_COUNT","SAFETYCOUNT": "SafetyCount","ACTUALYCOUNT": "ActualCount","STATION_NAME": "STATION_NAME","STATION": "STATION","VALVE_TYPE": "VALVE_TYPE","VALE": "VALE","VALE_STATUS": "VALE_STATUS","VALE_CONN": "VALE_CONN","AUTH_TYPE": "AUTH_TYPE","AUTH_TYPE_SET": "AUTH_TYPE_SET","RECIPE_SETTING": "RECIPE_SETTING","VIDEO_SETTING": "VIDEO_SETTING","VIDEO_REPLACE": "VIDEO_REPLACE","AUTH_CATEGORY": "AUTH_CATEGORY","SHELFLIFE_OVERDUE": "SHELFLIFE_OVERDUE","FAB_OVERDUE": "FAB_OVERDUE","STOREHOUSE": "STOREHOUSE","STOREHOUSE_COOL": "STOREHOUSE/COOL","STOREHOUSE_THAW": "STOREHOUSE/THAW","C_ALARMENABLE": "Enable","C_ALARMLEVEL": "Level","C_MULTI": "Multi","C_MATRIX": "Grid","C_MAINMATRIX": "MGrid","C_SLAVEMATRIX": "SGrid","C_TYPE_NAME": "TypeName","C_NOTE": "Note","C_ROOTPATH": "ROOTPATH","C_TREELEVEL": "TreeLevel","C_TREECOUNT": "TreeCount","C_FULL_NAME": "FullName","C_TRACK_RECIPE": "TrackRecipe","C_EDIT_USER": "EditUser","C_PART_NO": "PartNo","C_SHOW": "Show","C_VENDOR": "Vendor","C_VIDEO_TYPE": "VideoType","C_VIDEO_SIZE": "VideoSize","C_REMARK": "Remark","C_FAVORITES": "Fav","C_INDEX": "index","LAYER": "LAYER/","TYPE_LAYER": "LAYER","TYPE_SCHEDULE": "SCHEDULE","LastAccount": "LastAccount","LastLanguage": "LastLanguage","SystemSetting": "SystemSetting","$:type":"RCS.Api.ConfigData"};
window["Menu"]=window["RCS.Api.Menu"]=window["CS"]["Menu"]={"$None": "None","None": 0,"$ToolManagement": "ToolManagement","ToolManagement": 1,"$AreaSetup": "AreaSetup","AreaSetup": 2,"$AreaOptionSet": "AreaOptionSet","AreaOptionSet": 3,"$ToolGroupSetup": "ToolGroupSetup","ToolGroupSetup": 4,"$ToolGroupOptionSet": "ToolGroupOptionSet","ToolGroupOptionSet": 5,"$ToolSetup": "ToolSetup","ToolSetup": 6,"$ToolOptionSet": "ToolOptionSet","ToolOptionSet": 7,"$ToolSimpleLock": "ToolSimpleLock","ToolSimpleLock": 28,"$ToolStrictLock": "ToolStrictLock","ToolStrictLock": 29,"$AlarmSetup": "AlarmSetup","AlarmSetup": 8,"$AlarmOptionSet": "AlarmOptionSet","AlarmOptionSet": 9,"$AlarmDownload": "AlarmDownload","AlarmDownload": 10,"$AlarmImport": "AlarmImport","AlarmImport": 11,"$AlarmExport": "AlarmExport","AlarmExport": 12,"$SummarySearch": "SummarySearch","SummarySearch": 13,"$GridSetup": "GridSetup","GridSetup": 14,"$UserSetup": "UserSetup","UserSetup": 15,"$UserOptionSet": "UserOptionSet","UserOptionSet": 16,"$UserDownload": "UserDownload","UserDownload": 17,"$UserImport": "UserImport","UserImport": 18,"$UserExport": "UserExport","UserExport": 19,"$UserManagement": "UserManagement","UserManagement": 20,"$RoleSetup": "RoleSetup","RoleSetup": 21,"$RoleOptionSetup": "RoleOptionSetup","RoleOptionSetup": 22,"$AuthAssign": "AuthAssign","AuthAssign": 23,"$AuthSetup": "AuthSetup","AuthSetup": 24,"$AuthOptionSet": "AuthOptionSet","AuthOptionSet": 25,"$AudioSetup": "AudioSetup","AudioSetup": 26,"$NoticeSetup": "NoticeSetup","NoticeSetup": 27,"$Reserve0": "Reserve0","Reserve0": 30,"$Reserve1": "Reserve1","Reserve1": 31,"$Invalid": "Invalid","Invalid": 32,"$MenuCount": "MenuCount","MenuCount": 32,"$:type":"RCS.Api.Menu"};
window["DeclareAttribute"]=window["RCS.Api.DeclareAttribute"]=window["CS"]["DeclareAttribute"]={"PK": "PK","FK": "FK","URL": "URL","SET": "SET","TREE": "TREE","CONTENT": "CONTENT","$:type":"RCS.Api.DeclareAttribute"};
window["AlarmData"]=window["RCS.Api.AlarmData"]=window["CS"]["AlarmData"]={"ALARM_TYPE": "ALARM_TYPE","ALARM_NAME": "ALARM_NAME","ALARM_MARKER": "ALARM_MARKER","TYPE_VIDEO_LOST": "VIDEO_LOST","STATUS_NORMAL": 0,"STATUS_FINISH": 4096,"STATUS_CLOSED": 8192,"$:type":"RCS.Api.AlarmData"};
window["AlarmStatus"]=window["RCS.Api.AlarmStatus"]=window["CS"]["AlarmStatus"]={"$Normal": "Normal","Normal": 0,"$Finish": "Finish","Finish": 4096,"$Closed": "Closed","Closed": 8192,"$:type":"RCS.Api.AlarmStatus"};
window["PlayInputs"]=window["RCS.Api.PlayInputs"]=window["CS"]["PlayInputs"]={"$None": "None","None": 0,"$Key": "Key","Key": 1,"$KeyUp": "KeyUp","KeyUp": 2,"$KeyDown": "KeyDown","KeyDown": 3,"$Press": "Press","Press": 4,"$KeyPress": "KeyPress","KeyPress": 5,"$Mouse": "Mouse","Mouse": 6,"$Move": "Move","Move": 7,"$MouseMove": "MouseMove","MouseMove": 8,"$Click": "Click","Click": 9,"$DbClick": "DbClick","DbClick": 10,"$DblClick": "DblClick","DblClick": 11,"$MouseClick": "MouseClick","MouseClick": 12,"$DoubleClick": "DoubleClick","DoubleClick": 13,"$Up": "Up","Up": 14,"$MouseUp": "MouseUp","MouseUp": 15,"$Down": "Down","Down": 16,"$MouseDown": "MouseDown","MouseDown": 17,"$MouseDoubleClick": "MouseDoubleClick","MouseDoubleClick": 18,"$Wheel": "Wheel","Wheel": 19,"$MouseWheel": "MouseWheel","MouseWheel": 20,"$:type":"RCS.Api.PlayInputs"};
window["PlayFlags"]=window["RCS.Api.PlayFlags"]=window["CS"]["PlayFlags"]={"$NONE": "NONE","NONE": 0,"$WS": "WS","WS": 1,"$VNC": "VNC","VNC": 2,"$RTSP": "RTSP","RTSP": 3,"$SDK": "SDK","SDK": 4,"$:type":"RCS.Api.PlayFlags"};
window["PlayCursors"]=window["RCS.Api.PlayCursors"]=window["CS"]["PlayCursors"]={"$None": "None","None": 0,"$No": "No","No": 1,"$Arrow": "Arrow","Arrow": 2,"$AppStarting": "AppStarting","AppStarting": 3,"$Cross": "Cross","Cross": 4,"$Help": "Help","Help": 5,"$IBeam": "IBeam","IBeam": 6,"$SizeAll": "SizeAll","SizeAll": 7,"$SizeNESW": "SizeNESW","SizeNESW": 8,"$SizeNS": "SizeNS","SizeNS": 9,"$SizeNWSE": "SizeNWSE","SizeNWSE": 10,"$SizeWE": "SizeWE","SizeWE": 11,"$UpArrow": "UpArrow","UpArrow": 12,"$Wait": "Wait","Wait": 13,"$Hand": "Hand","Hand": 14,"$Pen": "Pen","Pen": 15,"$ScrollNS": "ScrollNS","ScrollNS": 16,"$PanNS": "PanNS","PanNS": 16,"$ScrollWE": "ScrollWE","ScrollWE": 17,"$PanWE": "PanWE","PanWE": 17,"$ScrollAll": "ScrollAll","ScrollAll": 18,"$PanAll": "PanAll","PanAll": 18,"$ScrollN": "ScrollN","ScrollN": 19,"$PanNorth": "PanNorth","PanNorth": 19,"$ScrollS": "ScrollS","ScrollS": 20,"$PanSouth": "PanSouth","PanSouth": 20,"$ScrollW": "ScrollW","ScrollW": 21,"$PanWest": "PanWest","PanWest": 21,"$ScrollE": "ScrollE","ScrollE": 22,"$PanEast": "PanEast","PanEast": 22,"$ScrollNW": "ScrollNW","ScrollNW": 23,"$PanNW": "PanNW","PanNW": 23,"$ScrollNE": "ScrollNE","ScrollNE": 24,"$PanNE": "PanNE","PanNE": 24,"$ScrollSW": "ScrollSW","ScrollSW": 25,"$PanSW": "PanSW","PanSW": 25,"$ScrollSE": "ScrollSE","ScrollSE": 26,"$PanSE": "PanSE","PanSE": 26,"$ArrowCD": "ArrowCD","ArrowCD": 27,"$Default": "Default","Default": -1,"$:type":"RCS.Api.PlayCursors"};
window["StatisticData"]=window["RCS.Api.StatisticData"]=window["CS"]["StatisticData"]={"TYPE_TIBCO_ALARM": "TibcoAlarm","TYPE_FDC_ALARM": "FDCAlarm","TYPE_OCR_ALARM": "OCRAlarm","TYPE_TIBCO_STATUS": "TibcoStatus","STATUS_NORMAL": 0,"STATUS_PAUSE": 1,"STATUS_STOPPED": 2,"STATUS_DISABLED": 3,"STATUS_BACK": 4,"STATUS_CHANGE": 5,"STATUS_BACK_HALF": 6,"STATUS_NORMAL_RECOVERY": 7,"$:type":"RCS.Api.StatisticData"};
window["StatisticStatus"]=window["RCS.Api.StatisticStatus"]=window["CS"]["StatisticStatus"]={"$Normal": "Normal","Normal": 0,"$Pause": "Pause","Pause": 1,"$Stopped": "Stopped","Stopped": 2,"$Disabled": "Disabled","Disabled": 3,"$Back": "Back","Back": 4,"$Change": "Change","Change": 5,"$:type":"RCS.Api.StatisticStatus"};
window["LogData"]=window["RCS.Api.LogData"]=window["CS"]["LogData"]={"TYPE_ASYNC": "ASYNC","TYPE_ADD": "ADD","TYPE_EDIT": "EDIT","TYPE_SAVE": "SAVE","TYPE_DELETE": "DELETE","TYPE_USER_ADD": "TYPE_USER_ADD","TYPE_USER_EDIT": "TYPE_USER_EDIT","TYPE_USER_DELETE": "TYPE_USER_DELETE","TYPE_ROLE_ADD": "TYPE_ROLE_ADD","TYPE_ROLE_EDIT": "TYPE_ROLE_EDIT","TYPE_ROLE_DELETE": "TYPE_ROLE_DELETE","TYPE_STATION_ADD": "STATION_ADD","TYPE_STATION_EDIT": "STATION_EDIT","TYPE_STATION_DELETE": "STATION_DELETE","TYPE_STATISTIC_": "STATISTIC_","TYPE_STATISTIC_ADD": "STATISTIC_ADD","TYPE_STATISTIC_EDIT": "STATISTIC_EDIT","TYPE_STATISTIC_EDIT_IP": "STATISTIC_EDIT_IP","TYPE_STATISTIC_STATUS_NORMAL": "STATISTIC_STATUS_NORMAL","TYPE_STATISTIC_STATUS_RECOVERY": "STATISTIC_STATUS_RECOVERY","TYPE_STATISTIC_STATUS_PAUSE": "STATISTIC_STATUS_PAUSE","TYPE_STATISTIC_STATUS_DISABLED": "STATISTIC_STATUS_DISABLED","TYPE_STATISTIC_STATUS_BACK": "STATISTIC_STATUS_BACK","TYPE_STATISTIC_STATUS_BACK_HALF": "STATISTIC_STATUS_BACK_HALF","TYPE_STATISTIC_STATUS_CHANGE": "STATISTIC_STATUS_CHANGE","TYPE_STATISTIC_DELETE": "STATISTIC_DELETE","TYPE_AREA_ADDSUB": "TYPE_AREA_ADDSUB","TYPE_AREA_ADD": "TYPE_AREA_ADD","TYPE_AREA_EDIT": "TYPE_AREA_EDIT","TYPE_AREA_DELETE": "TYPE_AREA_DELETE","TYPE_AREA_BATCHDELETE": "TYPE_AREA_BATCHDELETE","TYPE_TOOLGROUP_ADDSUB": "TYPE_TOOLGROUP_ADDSUB","TYPE_TOOLGROUP_ADD": "TYPE_TOOLGROUP_ADD","TYPE_TOOLGROUP_EDIT": "TYPE_TOOLGROUP_EDIT","TYPE_TOOLGROUP_DELETE": "TYPE_TOOLGROUP_DELETE","TYPE_TOOLGROUP_BATCHDELETE": "TYPE_TOOLGROUP_BATCHDELETE","TYPE_UNIT_ADD": "TYPE_UNIT_ADD","TYPE_UNIT_EDIT": "TYPE_UNIT_EDIT","TYPE_UNIT_DELETE": "TYPE_UNIT_DELETE","TYPE_UNIT_BATCHDELETE": "TYPE_UNIT_BATCHDELETE","TYPE_ALARM_ADD": "TYPE_ALARM_ADD","TYPE_ALARM_EDIT": "TYPE_ALARM_EDIT","TYPE_ALARM_DELETE": "TYPE_ALARM_DELETE","TYPE_ALARM_BATCHDELETE": "TYPE_ALARM_BATCHDELETE","TYPE_AUTH_REGISTERTOOL": "TYPE_AUTH_REGISTERTOOL","TYPE_AUTH_UNREGISTERTOOL": "TYPE_AUTH_UNREGISTERTOOL","TYPE_EQP_BINDING": "TYPE_EQP_BINDING","TYPE_EQP_UNBINDING": "TYPE_EQP_UNBINDING","TYPE_LAYOUT_ADD": "LAYOUT_ADD","TYPE_LAYOUT_EDIT": "LAYOUT_EDIT","TYPE_LAYOUT_DELETE": "LAYOUT_DELETE","TYPE_VIDEO_SET": "VIDEO_SET","TYPE_VIDEO_RECORD": "VIDEO_RECORD","TYPE_VIDEO_SNAPSHOT": "VIDEO_SNAPSHOT","TYPE_VIDEO_EDIT": "TYPE_VIDEO_EDIT","TYPE_VIDEO_ADD": "TYPE_VIDEO_ADD","TYPE_VIDEO_DELETE": "TYPE_VIDEO_DELETE","TYPE_VIDEO_SWEEPER": "VIDEO_SWEEPER","TYPE_VIDEO_TRACK": "VIDEO_TRACK","Login": "Login","Logout": "Logout","MonitorLogin": "MonitorLogin","MonitorLogout": "MonitorLogout","OperateLogin": "OperateLogin","OperateLogout": "OperateLogout","TYPE_NOTICE_SETTING": "TYPE_NOTICE_SETTING","$:type":"RCS.Api.LogData"};
window["AuthData"]=window["RCS.Api.AuthData"]=window["CS"]["AuthData"]={"TYPE_FAVORITES": "favorties","TYPE_REGISTER": "register","C_INDEX": "index","TYPE_OPERATION": "operation","TYPE_STORE": "TYPE_STORE","STATUS_NORMAL": 0,"STATUS_PART": 1,"STATUS_WORK": 2,"STATUS_BACK": 8192,"STATUS_UNBOUNDED": 16384,"$:type":"RCS.Api.AuthData"};
window["AuthStatus"]=window["RCS.Api.AuthStatus"]=window["CS"]["AuthStatus"]={"$Normal": "Normal","Normal": 0,"$Part": "Part","Part": 1,"$Work": "Work","Work": 2,"$Back": "Back","Back": 8192,"$Unbounded": "Unbounded","Unbounded": 16384,"$:type":"RCS.Api.AuthStatus"};
window["VideoData"]=window["RCS.Api.VideoData"]=window["CS"]["VideoData"]={"DataRecording": "Recording","DataRelative": "Relative","DataNoKB": "NoKB","DataExtra": "Extra","DataExtraMore": "ExtraMore","DataSweep": "Sweep","DataPTZ": "PTZ","AreaDenial": "AreaDenial","Alias": "Alias","Primary": "Primary","Slave": "Slave","HardwareMideaComm": "MideaComm","HUB": "HUB","TypeKVM": "KVM","TypeCCTV": "CCTV","TypeTouch": "Touch","TypeNQVM": "NQVM","UriSchemeMC": "MC","UriSchemeHK": "HK","UriSchemePW": "PW","UriSchemeSNV": "SNV","UriSchemePSC": "PSC","UriSchemeVNC": "VNC","UriSchemeVNCL": "VNCL","UriSchemeVNCR": "VNCR","UriSchemeATEN": "ATEN","UriSchemeNQVM": "NQVM","UriSchemeONVIF": "ONVIF","UriSchemeRTSP": "RTSP","STATUS_NORMAL": 0,"STATUS_OUTED": 1,"STATUS_MAPPED": 2,"STATUS_USING": 3,"STATUS_USED": 4,"$:type":"RCS.Api.VideoData"};
window["VideoItem"]=window["RCS.Api.VideoItem"]=window["CS"]["VideoItem"]={"MULTI_DATA_DESCRIPTION": "Mutil://slave.ID/?ID=primary.ID&Url=primary.Url&Multi=slave.Multi&Index=Value1&Columns=Value2&Rows=Value3&Left=Value4&Top=Value5&Right=Value6&Bottom=Value7&Width=Value8&Height=Value9","SWEEP_USER_DESCRIPTION": "Sweep://ID/Name?{eid}=last/2/S{sid}/2/U{uid}/2/T{uname}&{eid}=last/1/S{sid}/1/U{uid}/1/T{uname}&{eid}=last/1/S{sid}/1/U{uid}/1/T{uname}&{eid}=last/1/S{sid}/1/U{uid}/1/T{uname}","$:type":"RCS.Api.VideoItem"};
window["VideoMenu"]=window["RCS.Api.VideoMenu"]=window["CS"]["VideoMenu"]={"$None": "None","None": 0,"$View": "View","View": 1,"$Part": "Part","Part": 2,"$Work": "Work","Work": 3,"$Oust": "Oust","Oust": 4,"$Edit": "Edit","Edit": 5,"$Snapshot": "Snapshot","Snapshot": 6,"$Record": "Record","Record": 7,"$Layout": "Layout","Layout": 8,"$Chief": "Chief","Chief": 9,"$RunScript": "RunScript","RunScript": 10,"$PauseScript": "PauseScript","PauseScript": 11,"$StopScript": "StopScript","StopScript": 12,"$SetScript": "SetScript","SetScript": 13,"$SimpleLock": "SimpleLock","SimpleLock": 14,"$StrictLock": "StrictLock","StrictLock": 15,"$:type":"RCS.Api.VideoMenu"};
window["VideoStatus"]=window["RCS.Api.VideoStatus"]=window["CS"]["VideoStatus"]={"$Normal": "Normal","Normal": 0,"$Outed": "Outed","Outed": 1,"$Mapped": "Mapped","Mapped": 2,"$Using": "Using","Using": 3,"$Used": "Used","Used": 4,"$:type":"RCS.Api.VideoStatus"};
window["UserItem"]=window["RCS.Api.UserItem"]=window["CS"]["UserItem"]={"Administrators": "Administrators","Guests": "Guests","Users": "Users","AUTH_CONFIG": "config","AUTH_LDAP": "LDAP","AUTH_DB": "db","$:type":"RCS.Api.UserItem"};
window["Utils"]=window["RCS.Api.Utils"]=window["CS"]["Utils"]={"JS_MINUTE": 60000,"JS_HOUR": 3600000,"JS_DAY": 86400000,"JS_BASE": 621355968000000000,"WEB": "Web","WEB_A": "/Web","WEB_B": "Web/","WEB_AB": "/Web/","$:type":"RCS.Api.Utils"};
window["ResultCodes"]=window["RCS.Api.ResultCodes"]=window["CS"]["ResultCodes"]={"$Success": "Success","Success": 0,"$Failed": "Failed","Failed": 1,"$Exist": "Exist","Exist": 2,"$Denied": "Denied","Denied": 3,"$BadData": "BadData","BadData": 4,"$Conflict": "Conflict","Conflict": 5,"$NotFound": "NotFound","NotFound": 6,"$NotSupport": "NotSupport","NotSupport": 7,"$:type":"RCS.Api.ResultCodes"};
window["Realms"]=window["RCS.Api.Realms"]=window["CS"]["Realms"]={"$Unknow": "Unknow","Unknow": 0,"$SEMI": "SEMI","SEMI": 1,"$LCD": "LCD","LCD": 2,"$:type":"RCS.Api.Realms"};
window["LoginResults"]=window["RCS.Api.LoginResults"]=window["CS"]["LoginResults"]={"$Nonsupport": "Nonsupport","Nonsupport": 0,"$OK": "OK","OK": 1,"$Cancel": "Cancel","Cancel": 2,"$Error": "Error","Error": 3,"$NotFound": "NotFound","NotFound": 4,"$Forbidden": "Forbidden","Forbidden": 5,"$PasswordError": "PasswordError","PasswordError": 6,"$PasswordWeak": "PasswordWeak","PasswordWeak": 7,"$:type":"RCS.Api.LoginResults"};
window["VideoTypes"] = window["RCS.Api.VideoTypes"] = window["CS"]["VideoTypes"] = { "$Unknow": "Unknow", "Unknow": 0, "$KVM": "KVM", "KVM": 1, "$KVM2": "KVM2", "KVM2": 2, "$Touch": "Touch", "Touch": 3, "$CCTV": "CCTV", "CCTV": 4, "$NVR": "NVR", "NVR": 5, "$DVR": "DVR", "DVR": 6, "$VNC": "VNC", "VNC": 7, "$RDP": "RDP", "RDP": 8, "$User": "User", "User": 9, "$:type": "RCS.Api.VideoTypes" };
