﻿/* for ESWIN */
/*<!--.css 以下是有效的 CSS 代码，切勿移除 **************************
.god, .god-a-t, .god-a-v, .god-a-s, .god-v-r, .god-v-sm, .god-v-sp, .god-v-a, .god-v-p, .god-a-m, .eswin {
	display: none !important;
}
/*--><!--.js 以下是 JS 代码 ***************************************** */
if (!!window.godCSS)
	window.CS.css = window.godCSS('.god, .god-a-t, .god-a-v, .god-a-s, .god-v-r, .god-v-sm, .god-v-sp, .god-v-a, .god-v-p, .god-a-m, .eswin');
window.CS.title = '远程控制系统(RCS)';
window.CS.company = '西安奕斯伟(ESWIN)';
window.CS.getExtraData = function (url)
{
	if (!url || !url.indexOf)
	{
		if (!arguments.length)
			return [{ 'text': '抛光 机械按钮', 'value': 'Burnishing' }, { 'text': '拉晶 机械按钮', 'value': 'Growing' }, { 'text': '拉晶 WinCC', 'value': 'WinCC' },];

		return;
	}

	var u = url.toLowerCase();
	var i = u.indexOf('extra=wincc'); // WinCC //
	if (i > 0)
	{
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'WinCC';
			this.html = '<a class="tb" onclick="javascript: doWinCC.call(this);" id="a-winCC" title="WinCC"><img id="img-winCC" src="imgs/toolbar/chart.png" alt="WinCC"/><br />WinCC</a>';
			this.src = null;
			this['e'] = '#p-end-0';
		}
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				f.prototype.ExtraMore = o[i];
				break;
			}
		}
		f.prototype.doWinCC = function (e)
		{
			if (!!e && e.target == window.winCC.s)
			{
				//
				return;
			}

			if (!window.winCC.s || window.winCC.s.readyState != window.WebSocket.OPEN)
				return;

			window.winCC.s.send('winCC.do');
		};
		if (!!window.WebSocket)
		{
			f.prototype.o = function (e)
			{
				if (!!e.target && e.target != window.winCC.s)
				{
					if (!!window.winCC.s && window.winCC.s.readyState == window.WebSocket.OPEN)
						window.winCC.s.close();
					window.winCC.s = e.target;
				}
				window.winCC.s.send('ping');
			};
			f.prototype.e = function (e)
			{
				console.log('WS error' + e.data);
			};
			f.prototype.c = function (e)
			{
				if (!!e && !!e.target && e.target != window.winCC)
					return;

				if (!window.winCC.ExtraMore)
					return;

				var ws = new WebSocket('ws://' + window.winCC.ExtraMore);
				ws.onopen = window.winCC.o;
				ws.onerror = window.winCC.e;
				ws.onclose = window.winCC.c;
				ws.onmessage = window.doWinCC;
				window.winCC.s = ws;
			};
		}
		window.winCC = new f();
		window.doWinCC = window.winCC.doWinCC;
		if (!!window.winCC.c)
			window.winCC.c.call(window.winCC);
		return window.winCC;
	}

	i = u.indexOf('extra=growing'); // 拉晶 //
	if (i > 0)
	{
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'Growing';
			this.html = null;
			this.src = 'part/growing.html';
			this.collect = true;
			this['e'] = '#p-end-0';
			this['0'] = '55AA770100640102030405060708000000/55AA660203E80000000000000000AA55';// 点动时间 / 开关量主动上传 //
			this['1'] = { o: 1, data: 'CCDD330100010001366C', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['2'] = { o: 2, data: 'CCDD3301000200023870', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['3'] = { o: 3, data: 'CCDD3301000400043C78', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['4'] = { o: 5, data: 'CCDD33010010001054A8', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['5'] = { o: 4, data: 'CCDD3301000800084488', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['6'] = { o: 6, data: 'CCDD33010020002074E8', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['7'] = { o: 7, data: 'CCDD330100400040B468', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['8'] = { o: 8, data: 'CCDD3301008000803468', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['Q0'] = '55AA770100640102030405060708000000';//点动时间 100 + (1,2,3,4,5,6,7,8) ms //
			this['A0'] = '4F4B21';
			this['Q1'] = '55AA660203E80000000000000000AA55';//开关量主动上传 0x03E8 = 1000ms // 55AA660200640000000000000000AA55(0x0064 = 100ms) //
			this['A1'] = '4F4B21';
			this['OQ'] = { data: 'CCDDB00100000DBE7C', setInterval: 200 };//读取输出状态//2+8 = 0x82 = AABBB00100820D40//
			this['OS'] = [0, 0, 0, 0, 0, 0, 0, 0];
			this['IQ'] = { data: 'CCDDC00100000DCE9C', setInterval: 200 };//读取输入状态//1+2+3+6 = 0x27 = EEFFC00100270DF5EA//
			this['IS'] = [0, 0, 0, 0, 0, 0, 0, 0];
			this.hw = 'CX-5X0XE';
		};
		var cb = function (data, extra)
		{
			if (!data)
				return;

			if (data == '0')
			{
				return
				[
					'55AA770100640102030405060708000000',//点动时间 100 + (1,2,3,4,5,6,7,8) ms //
					'55AA660203E80000000000000000AA55',//开关量主动上传 0x03E8 = 1000ms // 55AA660200640000000000000000AA55(0x0064 = 100ms) //
					null
				];
			}

			var i = data.lastIndexOf('=');
			if (i > 0)
			{
				data = data.substring(i + 1, data.length);
			}
			else
			{
				i = data.lastIndexOf('?');
				if (i > 0)
				{
					data = data.substring(i + 1, data.length);
				}
				else
				{
					i = data.lastIndexOf('/');
					if (i > 0)
						data = data.substring(i + 1, data.length);
				}
			}
			data = data.toUpperCase();
			if (data == '4F4B21')//操作成功
			{
				if (!!window.Data)
					window.Data('Status', lang('操作成功'));
				return data;
			}

			if (data.substring(0, 8) == 'AABBB001')//读取输出状态//2+8 = 0x82 = AA-BB-B0-01-00-82-0D-40//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				if (!!extra && !!extra['OS'])
					extra['OS'] = v;
				return v;
			}

			if (data.substring(0, 8) == 'EEFFC001')//读取输入状态//1+2+3+6 = 0x27 = EE-FF-C0-01-00-27-0D-F5-EA//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				for (i = 1; i <= 8; i++)
				{
					var t = v[i - 1] > 0 ? '1' : '0';
					if ($('#img-extra' + i).data('src') != t)
					{
						$('#img-extra' + i).data('src', t);
						$('#img-extra' + i).attr('src', 'imgs/toolbar/' + this[i + '']['img' + t]);
					}
				}
				if (!!extra && !!extra['IS'])
					extra['IS'] = v;
				return v;
			}
		};
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				if (o[i].indexOf(':') > 0)
					f.prototype.ExtraMore = o[i];
				else
					f.prototype.ExtraMore = o[i] + ':50000';
				break;
			}
		}
		f.prototype.cb = cb;
		f.prototype.onmessage = cb;
		f.prototype.onMessage = cb;
		return new f();
	}

	i = u.indexOf('extra=burnishing'); // 抛光 //
	if (i > 0)
	{
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'Burnishing';
			this.html = null;
			this.src = 'part/burnishing.html';
			this['e'] = '#p-end-1';
			this['0'] = {};
			this['1'] = { type: 'reset', data: '00FA000F01FAFE1000030002040002000AA16A', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['2'] = { type: 'reset', data: '00FA000F01FAFE1000080002040002000AE0D9', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['3'] = { type: 'reset', data: '00FA000F01FAFE10000D0002040002000A20E6', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['4'] = { type: 'reset', data: '00FA000F01FAFE1000120002040002000A61AA', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['5'] = { type: 'reset', data: '00FA000F01FAFE1000170002040002000AA195', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['6'] = { type: 'reset', data: '00FA000F01FAFE10001C0002040002000AE026', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['7'] = { type: 'reset', data: '00FA000F01FAFE1000210002040002000A22AB', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['8'] = { type: 'reset', data: '00FA000F01FAFE1000260002040002000A634D', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
		}
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				f.prototype.ExtraMore = o[i];
				break;
			}
		}
		return new f();
	}
};
/*-->*/
