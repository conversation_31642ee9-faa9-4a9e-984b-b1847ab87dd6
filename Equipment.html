﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>iRCMDevice页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="../user/loginMenu.docss" />
	<style type="text/css">
		#addkvm .layui-inline {
			margin-right: 0px;
		}

		.edit {
			width: 200px;
		}

		.borderStyle {
			border: 1px solid red;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}

		.layui-form-switch {
			margin-top: -3px;
		}

		.txMark {
		}

		.txMarkHide {
			display: none;
		}

		#img-container {
			position: relative;
			width: 100%;
			height: 100%;
			background-size: contain;
			background-repeat: no-repeat;
		}

		#resizable-div {
			position: relative;
			width: 100%; /* Start width */
			height: 100%; /* Start height */
			top: 0%; /* Start top position */
			left: 0%; /* Start left position */
			border: 1px solid red;
		}

		.handle {
			position: absolute;
			width: 8px;
			height: 8px;
			background-color: rgba(255, 0, 0, 0.5);
		}

		#top-left {
			top: 0px;
			left: 0px;
			cursor: nwse-resize;
		}

		#top-right {
			top: 0px;
			right: 0px;
			cursor: nesw-resize;
		}

		#bottom-left {
			bottom: 0px;
			left: 0px;
			cursor: nesw-resize;
		}

		#bottom-right {
			bottom: 0px;
			right: 0px;
			cursor: nwse-resize;
		}

		#top-center {
			top: 0px;
			left: 50%;
			cursor: ns-resize;
		}

		#bottom-center {
			bottom: 0px;
			left: 50%;
			cursor: ns-resize;
		}

		#middle-left {
			top: 50%;
			left: 0px;
			cursor: ew-resize;
		}

		#middle-right {
			top: 50%;
			right: 0px;
			cursor: ew-resize;
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/excel.js"></script>
	<script type="text/javascript" src="js/request.js"></script>
	<script type="text/javascript">
		$(document).ready(function ()
		{
			if (!!window.CS.getExtraData)
			{
				var vs = window.CS.getExtraData();
				if (!vs)
				{
					var es = document.getElementsByName('div-extra');
					for (var i = 0; i < es.length; i++)
						$(es[i]).remove();
				}
				else
				{
					var es = document.getElementsByName('query-extra');
					for (var i = 0; i < es.length; i++)
					{
						for (var ii = 0; ii < vs.length; ii++)
							$(es[i]).append('<option value="' + vs[ii].value + '">' + vs[ii].text + '</option>');
					}
				}
			}
		});
	</script>
	<script type="text/javascript">
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel', 'upload'], function ()
		{
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, upload = layui.upload
				, excel = layui.excel;

			lang.call(this, $);
			toolData = null;
			table.init('tooltable', []);
			limitPageCount = 17;
			currentFirstIndex = 1;
			col = toolColumnsName();
			//创建Table的列名
			tableIni = table.render({
				elem: '#toollist'
				, even: true
				, sort: true
				, loading: false
				, toolbar: hasButtonPermission('EquipmentSet') ? '#tooltoolbar' : []
				, defaultToolbar: ['filter']
				, id: "toollist"
				, title: lang('Unit数据表')
				, limit: limitPageCount
				, cols: [col]
				, height: 'full-185'
				, data: []
			});
			pageCount = 1;
			FillDataTable();
			//头工具栏事件
			table.on('toolbar(tooltable)', function (obj)
			{
				var checkStatus = table.checkStatus(obj.config.id);
				switch (obj.event)
				{
					case 'getCheckData':
						var data = checkStatus.data;
						Delete(data);
						break;
					case 'isAdd':
						refreshAreaAndGroup('', '', '_pc');
						index = layer.open({
							title: lang('增加 PC')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addkvm')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function ()
							{
							}
							, btn1: function (index)
							{
								var data = form.val("form-addkvm");
								if (data.name.trim().length == 0)
								{
									layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.ip.length == 0)
								{
									layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.toolgroupname == '不选' && data.areaname == '不选')
								{
									layer.alert(lang('请选择一个区域或一个机台组！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.eqp.trim().length == 0)
								{
									layer.alert(lang('Device EQP ID不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.toolgroupname == '不选')
									data.toolgroupname = "";

								if (data.areaname == "不选")
									data.areaname = "";

								if (data.toolgroupname.length == 0)
								{
									layer.alert(lang('组不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}
								if (data.areaname.length == 0)
								{
									layer.alert(lang('区域不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}
								data['toolgroupname'] = data.toolgroupname;
								data['areaname'] = data.areaname;

								data.type = CS.VideoData.TypeKVM;

								addEquipment({
									data: data,
									name: "PC",
									id: "addkvm",
									content: index,
									type: '_pc'
								})
							}
							, end: function ()
							{
								$('#addkvm')[0].reset();
								$('#addkvm').css('display', 'none');
							}
						});
						break;
					case 'ExportData':
						{
							exportExcelData();
							break;
						}
					case 'isAddCctv':
						refreshAreaAndGroup('', '', '_cctv');
						index = layer.open({
							title: lang('增加 CCTV')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addcctv')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function ()
							{
							}
							, btn1: function (index)
							{
                                var data = form.val("form-addcctv");
								if (data.name.trim().length == 0)
								{
									layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.ip.length == 0)
								{
									layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.toolgroupname == '不选' && data.areaname == '不选')
								{
									layer.alert(lang('请选择一个区域或一个机台组！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.eqp.trim().length == 0)
								{
									layer.alert(lang('Device EQP ID不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.toolgroupname == '不选')
									data.toolgroupname = "";

								if (data.areaname == "不选")
									data.areaname = "";

								data['toolgroupname'] = data.toolgroupname;
								data['areaname'] = data.areaname;

								data.type = CS.VideoData.TypeCCTV;

								addEquipment({
									data: data,
									name: "CCTV",
									id: "addcctv",
									content: index,
									type: '_cctv'
								})
							}
							, end: function ()
							{
								$('#addcctv')[0].reset();
								$('#addcctv').css('display', 'none');
							}
						});
						break;
					case 'isAddTouch':
						refreshAreaAndGroup('', '', '_touch');
						index = layer.open({
							title: lang('增加 Touch Panel')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addtouch')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function ()
							{
							}
							, btn1: function (index)
							{
                                var data = form.val("form-addtouch");
								if (data.name.trim().length == 0)
								{
									layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.ip.length == 0)
								{
									layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.toolgroupname == '不选' && data.areaname == '不选')
								{
									layer.alert(lang('请选择一个区域或一个机台组！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.eqp.trim().length == 0)
								{
									layer.alert(lang('Device EQP ID不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.toolgroupname == '不选')
									data.toolgroupname = "";

								if (data.areaname == "不选")
									data.areaname = "";

								data['toolgroupname'] = data.toolgroupname;
								data['areaname'] = data.areaname;

								data.type = CS.VideoData.TypeTouch;
								data.scheme = 'VNCR';

								addEquipment({
									data: data,
									name: "Touch Panel",
									id: "addtouch",
									content: index,
									type: '_touch'
								})
							}
							, end: function ()
							{
								$('#addtouch')[0].reset();
								$('#addtouch').css('display', 'none');
							}
						});
						break;
					case 'BatchDelete':
						{
							BatchDelete();
							break;
						}
				};
			});
			// 添加设备pc cctv touch
			function addEquipment(obj)
			{
				AjaxApi.request({
					url: "/Equipment/AddEquipment.do",
					data: obj.data,
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.code == "Success")
						{
							FillDataTable();
							layer.alert(lang('保存成功,是否继续?'), {
								title: lang('增加 ' + obj.name)
								, btn: [lang('继续'), lang('取消')]
								, btnAlign: 'c'
								, btn1: function (subIndex)
								{
									$('#' + obj.id)[0].reset();
									setAreaAndGroup('', '', obj.type);
									layer.close(subIndex);
								}
								, btn2: function (subIndex)
								{
									$('#' + obj.id)[0].reset();
									layer.close(subIndex);
									layer.close(obj.content);
									//FillDataTable();
								}
							});
						}
						else
						{
							layer.alert(res.msg, { btn: [lang('确定')], title: lang("增加 " + obj.name), });
						}
					},
					error: function (r)
					{
						layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加 " + obj.name), });
					}
				})
			}
			// 批量删除
			function BatchDelete()
			{
				if (checkedItems.length == 0)
				{
					layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
					return;
				}

				layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
				{
					AjaxApi.request({
						type: "post",
						url: "/Equipment/DatchDeleteEquipment.do",
						data: JSON.stringify(checkedItems),
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);
							if (res.code == "Success")
							{
								checkedItems = [];
								//col[col.length - 1].hide = true;
								table.reload('toollist', {
									cols: [col],
								})

								$('#normaldiv').show();
								$('#batchdiv').hide();

								layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
							}
							else
							{
								layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除Unit"), });
							}
							FillDataTable();
						}
					})
				});
			}

			/**
			 * 导出 Excel 数据
			 */
			function exportExcelData()
			{
				AjaxApi.request({
					url: "/tool/GetToolListAllExport.do",
					data: gData,
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);

						//col[col.length - 1].hide = true;
						//加载table数据
						table.reload('toollist', {
							data: refillData(res.data),
							limit: res.data.length,
							cols: [col],
							done: function (res, curr, count)
							{
								exportDIYStyleExcel('toollist', excel, lang('Unit设置.xlsx'));
							}
						});
					}
				})
			}

			//监听行工具事件
			table.on('tool(tooltable)', function (obj)
			{
				var data = obj.data;

				if (obj.event === 'del')
				{
					Delete(data);
					return;
				}

				if (obj.event === 'edit')
				{
					var elem = 'add' + data.Type.toLowerCase();

					var index1 = layer.open({
						title: lang('修改 ') + data.Type
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#' + elem)
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function ()
						{
							var d = { name: data.Name, mark: data.Landmark, ip: data.Url.replace(/^.+:\/\/([^\/]+)(\/.*)?/g, '$1'), port: '', user: '', psd: '' };
							var i = data.Url.indexOf('?');
							if (!!data.Data)
							{
								var ds = $.parseUrl(data.Data, {});
								for (var k in ds)
									d['data-' + k] = ds[k];
							}
							if (i > 0)
							{
								var ds = $.parseUrl(data.Url.substring(i + 1, data.Url.length), {});
								for (var k in ds)
									d['query-' + k] = ds[k];
							}
							i = d.ip.indexOf('@');
							if (i > 0)
							{
								d.psd = d.ip.substring(0, i);
								d.ip = d.ip.substring(i + 1, d.ip.length);
								i = d.ip.indexOf(':');
								if (i > 0)
								{
									d.port = d.ip.substring(i + 1, d.ip.length);
									d.ip = d.ip.substring(0, i);
								}
								i = d.psd.indexOf(':');
								if (i >= 0)
								{
									d.user = d.psd.substring(0, i);
									d.psd = d.psd.substring(i + 1, d.psd.length);
									d.user = decodeURIComponent(d.user);
								}
								d.psd = decodeURIComponent(d.psd);
							}
							d.alias = data.Alias;
							d.recording = data.Recording;
							d.eqp = data.Eqp;
							switch (data.Type)
							{
								case CS.VideoData.TypeKVM:
									refreshAreaAndGroup(data.areaname, data.unitname, '_pc');
									form.val('form-addkvm', d);
									break;
								case CS.VideoData.TypeCCTV:
									refreshAreaAndGroup(data.areaname, data.unitname, '_cctv');
									form.val('form-addcctv', d);
									break;
								case CS.VideoData.TypeTouch:
									refreshAreaAndGroup(data.areaname, data.unitname, '_touch');
									form.val('form-addtouch', d);
									break;
							}
						}
						, btn1: function (value, index)
						{
							var data1 = form.val("form-" + elem);
                            if (elem.indexOf('kvm') > 0) addSelectIcon('_pc');
                            if (elem.indexOf('cctv') > 0) addSelectIcon('_cctv');
                            if (elem.indexOf('touch') > 0) addSelectIcon('_touch');

							data1["id"] = data.ID;
							data1['type'] = data.Type;

							if (data1.toolgroupname == '不选')
								data1.toolgroupname = "";

							if (data1.areaname == "不选")
								data1.areaname = "";

							if (data1.toolgroupname.length == 0)
							{
								layer.alert(lang('组不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}
							if (data1.areaname.length == 0)
							{
								layer.alert(lang('区域不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}

							if (data1.eqp.trim().length == 0)
							{
								layer.alert(lang('Device EQP ID不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}
							data1['toolgroupname'] = data1.toolgroupname;
							data1['areaname'] = data1.areaname;
							if (data.Type == CS.VideoData.TypeTouch) data1.scheme = 'VNCR';

							AjaxApi.request({
								url: "/Equipment/EditEquipment.do",
								data: data1,
								dataType: 'text',
								success: function (result)
								{
									var res = $.eval(result);
									if (res.code == "Success")
									{
										layer.msg(lang('修改成功'), { icon: 1 });
										layer.close(index1);
										FillDataTable();
									}
									else
									{
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
							})
						}
						, end: function ()
						{
							$('#' + elem)[0].reset();
							$('#' + elem).css('display', 'none');
						}
					});
					return;
				}

				if (obj.event === 'change')
				{
					//	img.src = 'http://ip:8001/mt/0/' + Date.now() + '.jpg';//1,2

					var index1 = layer.open({
						title: lang('修改 Recipe')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#changeRecipe')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function ()
						{

						}
						, btn1: function (value, index)
						{
							var recipe = form.val('form-changeRecipe');

							if (data.Type == 'CCTV')
								return;

							var ip = data.Url.toLowerCase().replace(/^.+:\/\/([^\/]+).*$/img, '$1');

							if (recipe.enable == undefined)
							{
								$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/0/' + Date.now() + '.jpg');
							}
							else
							{
								$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/' + recipe.recipe + '/' + Date.now() + '.jpg');
								//$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/rpa/' + recipe.recipe + '/' + Date.now() + '.jpg');
							}

							layer.msg('已切换Recipe');

							layer.close(index1);
						}
						, end: function ()
						{
						}
					});
					return;
				}

				if (obj.event == 'singleclick')
				{
					var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
					if (checkCell.length > 0)
						checkCell.click();
					return;
				}

				if (obj.event === 'area')
					chooseArea(data)
			});

			var checkedItems = [];
			table.on('checkbox(tooltable)', function (obj)
			{
				if (obj.checked)
				{
					if (obj.type == 'all')
					{
						checkedItems = table.cache.toollist;
					} else
					{
						checkedItems.push(obj.data);
					}
				} else
				{
					if (obj.type == 'all')
					{
						checkedItems = [];
					} else
					{
						var i = 0;
						$.each(checkedItems, function (key, value)
						{
							if (value.id == obj.data.id)
							{
								checkedItems.splice(i, 1);
								return false;
							}
							i++;
						})
					}
				}
			});

			form.on('submit(toolSearch)', function (data)
			{
				FillDataTable();
				return false;
			});
			form.on('select(selRoleVideo)', function (data)
			{
				FillDataTable();
			})
			$('button[type=button]').click(function ()
			{
				document.getElementById('form-select').reset();
				FillDataTable();
			})
			function toolColumnsName()
			{
				var columnsArray = [];
				var columnsNames = [
					{
						title: lang("序号"), field: "xuhao", width: 80, templet: function (e)
						{
							return limitPageCount * (pageCount - 1) + e.LAY_INDEX
						}
					}
					, { title: lang("Device名称"), field: "Name" }
					, { title: lang("Device类型"), field: "Type" }
					, { title: lang("所属区域"), field: "areaname" }
					, { title: lang("所属机台组"), field: "unitname" }
					, { title: lang("Url"), field: "Url" }
					, { title: lang("地标"), field: "Landmark" }
					, { title: lang("最后心跳"), field: "Pong", templet: templet }
					, { title: lang("最后操作者"), field: "WatcherName" }
					, { title: lang('更新者'), field: 'EditUser' }
				];

				for (var i = 0; i < columnsNames.length; i++)
				{
					var o = {
						title: columnsNames[i].title,
						field: columnsNames[i].field,
						align: 'center',
						event: 'singleclick',
						templet: columnsNames[i].templet,
						hide: columnsNames[i].hide
					};
					if (columnsNames[i].width) o.width = columnsNames[i].width;
					columnsArray.push(o);
				}

				var columnsLast = {
					minWidth: 180,
					title: lang("操作"),
					toolbar: "#operation",
					event: 'singleclick',
					align: 'center',
					fixed: 'right',
					hide: !hasButtonPermission('EquipmentSet')
				}
				columnsArray.push(columnsLast);

				var columnsLastAgain = {
					title: lang("选中"),
					type: 'checkbox',
					event: 'singleclick',
					align: 'center',
					hide: false,
				}
				columnsArray.unshift(columnsLastAgain);

				return columnsArray;
			}

			function Delete(data)
			{
				layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
				{
					AjaxApi.request({
						url: "/Equipment/DeleteEquipment.do",
						data: { id: data.ID, type: data.Type, name: data.Name },
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);
							if (res.code == "Success")
							{
								layer.msg(lang('删除成功'), { icon: 1 });
								FillDataTable();
							}
							else
							{
								layer.msg(lang('删除失败'), { icon: 2 });
							}
						},
						error: function (r)
						{
							layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
						}
					})
					layer.close(index);
				});
			}

			form.on('select()', function (data)
			{
				$(".txMark").addClass("txMarkHide");

				if (data.value == 'NQVM')
				{
					$('#pc_record').html('\
<span class="god-v-rw"><input type="radio" name="recording" value="W" title="观看时录屏"></span>\
<span class="god-v-rc"><input type="radio" name="recording" value="C" title="操作时录屏"></span>\
<span class="god-v-ra"><input type="radio" name="recording" value="A" title="始终录屏"></span>');
				}
				else if (data.value == 'KVM')
				{
					$('#pc_record').html('\
<span class="god-v-rn"><input type="radio" name="recording" value="N" title="手动录屏" checked=""></span>\
<span class="god-v-rw"><input type="radio" name="recording" value="W" title="观看时录屏"></span>\
<span class="god-v-rc"><input type="radio" name="recording" value="C" title="操作时录屏"></span>\
<span class="god-v-ra"><input type="radio" name="recording" value="A" title="始终录屏"></span>');
				}
				else if (data.value == "NQVM_")
				{
					$('#pc_record').html('\
<span class="god-v-rw"><input type="radio" name="recording" value="W" title="观看时录屏"></span>\
<span class="god-v-rc"><input type="radio" name="recording" value="C" title="操作时录屏"></span>\
<span class="god-v-ra"><input type="radio" name="recording" value="A" title="始终录屏"></span>');
					$(".txMark").removeClass("txMarkHide");
				}

				form.render();
			});

            form.on("select(areaname_pc)", function (data) {
                form.render('select');
                addSelectIcon('_pc');
            });
            form.on("select(toolgroupname_pc)", function (data) {
                form.render('select');
                addSelectIcon('_pc');
			});
            form.on("select(areaname_cctv)", function (data) {
                form.render('select');
                addSelectIcon('_cctv');
            });
            form.on("select(toolgroupname_cctv)", function (data) {
                form.render('select');
                addSelectIcon('_cctv');
			});
            form.on("select(areaname_touch)", function (data) {
                form.render('select');
                addSelectIcon('_touch');
            });
            form.on("select(toolgroupname_touch)", function (data) {
                form.render('select');
                addSelectIcon('_touch');
            });
		});
		// 框选范围
		function chooseArea(data)
		{
			var AreaDialog = layer.open({
				title: lang('选择范围 ')
				, type: 1
				, resize: false
				, id: 'areaChoose'
				, area: ['70%', '70%']
				, content: $('#chooseArea')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
				, success: function ()
				{
					var ip = data.Url.split("://");
					$("#img-container").css('background-image', "url(" + '/frame/' + ip[1] + ")");
					if (!!data.AreaDenial)
					{
						var pos = data.AreaDenial.split(',');
						$('#resizable-div').css('top', parseFloat(pos[1]) * 100 + '%')
						$('#resizable-div').css('left', parseFloat(pos[0]) * 100 + '%')
						$('#resizable-div').css('width', parseFloat(pos[2]) * 100 + '%')
						$('#resizable-div').css('height', parseFloat(pos[3]) * 100 + '%')
					}
					else
					{
						$('#resizable-div').css('top', '0%')
						$('#resizable-div').css('left', '0%')
						$('#resizable-div').css('width', '100%')
						$('#resizable-div').css('height', '100%')
					}
					var resDiv = document.getElementById('resizable-div');
					var container = document.getElementById('img-container');
					var containerWidth = container.offsetWidth;
					var containerHeight = container.offsetHeight;
					var startX, startY, startWidth, startHeight, startTop, startLeft;
					var draggable = false;
					var currentHandle = null;
					var minWidth = 100, minHeight = 50;

					var handles = Array.prototype.slice.call(document.querySelectorAll('.handle'));
					handles.forEach(function (handle)
					{
						handle.addEventListener('mousedown', function (e)
						{
							e.stopPropagation();
							draggable = true;
							currentHandle = e.target.id;
							startX = e.clientX;
							startY = e.clientY;
							startWidth = parseFloat(resDiv.offsetWidth);
							startHeight = parseFloat(resDiv.offsetHeight);
							startTop = parseFloat(window.getComputedStyle(resDiv, null).getPropertyValue('top'));
							startLeft = parseFloat(window.getComputedStyle(resDiv, null).getPropertyValue('left'));
							containerWidth = container.clientWidth;
							containerHeight = container.clientHeight;
							e.preventDefault();
						}, false);
					});

					document.addEventListener('mousemove', function (e)
					{
						if (!draggable) return;
						var dx = e.clientX - startX;
						var dy = e.clientY - startY;
						var newLeft = startLeft;
						var newTop = startTop;
						var newWidth = startWidth - 2;
						var newHeight = startHeight - 2;
						switch (currentHandle)
						{
							case 'top-left':
								newTop = Math.max(0, Math.min(containerHeight - resDiv.offsetHeight, startTop + dy));
								newLeft = Math.max(0, Math.min(containerWidth - resDiv.offsetWidth, startLeft + dx));
								newWidth = Math.min(containerWidth - newLeft, Math.max(minWidth, startWidth - dx));
								newHeight = Math.min(containerHeight - newTop, Math.max(minHeight, startHeight - dy));
								break;
							case 'top-center':
								newTop = Math.max(0, Math.min(containerHeight - resDiv.offsetHeight, startTop + dy));
								newHeight = Math.min(containerHeight - newTop, Math.max(minHeight, startHeight - dy));
								break;
							case 'top-right':
								newTop = Math.max(0, Math.min(containerHeight - resDiv.offsetHeight, startTop + dy));
								newWidth = Math.min(containerWidth - newLeft, Math.max(minWidth, startWidth + dx));
								newHeight = Math.min(containerHeight - newTop, Math.max(minHeight, startHeight - dy));
								break;
							case 'middle-left':
								newLeft = Math.max(0, Math.min(containerWidth - resDiv.offsetWidth, startLeft + dx));
								newWidth = Math.min(containerWidth - newLeft, Math.max(minWidth, startWidth - dx));
								break;
							case 'middle-right':
								newWidth = Math.min(containerWidth - newLeft, Math.max(minWidth, startWidth + dx));
								break;
							case 'bottom-left':
								newLeft = Math.max(0, Math.min(containerWidth - resDiv.offsetWidth, startLeft + dx));
								newWidth = Math.min(containerWidth - newLeft, Math.max(minWidth, startWidth - dx));
								newHeight = Math.min(containerHeight - newTop, Math.max(minHeight, startHeight + dy));
								break;
							case 'bottom-center':
								newHeight = Math.min(containerHeight - newTop, Math.max(minHeight, startHeight + dy));
								break;
							case 'bottom-right':
								newWidth = Math.min(containerWidth - newLeft, Math.max(minWidth, startWidth + dx));
								newHeight = Math.min(containerHeight - newTop, Math.max(minHeight, startHeight + dy));
								break;
						}

						resDiv.style.top = newTop + 'px';
						resDiv.style.left = newLeft + 'px';
						resDiv.style.width = newWidth + 'px';
						resDiv.style.height = newHeight + 'px';
						e.preventDefault();
					}, false);

					document.addEventListener('mouseup', function ()
					{
						draggable = false;
						currentHandle = null;
					}, false);
				}
				, btn1: function ()
				{
					var resDiv = document.getElementById('resizable-div');
					var containerWidth = document.getElementById('img-container').offsetWidth;
					var containerHeight = document.getElementById('img-container').offsetHeight;
					var x = (resDiv.offsetLeft / containerWidth).toFixed(2);
					var y = (resDiv.offsetTop / containerHeight).toFixed(2);
					var width = (resDiv.offsetWidth / containerWidth).toFixed(2);
					var height = (resDiv.offsetHeight / containerHeight).toFixed(2);
					// console.log(data, 'dimensions: ', x, y, width, height);
					var videoData = '';
					var query = data.Data.split('&');
					query.forEach(value =>
					{
						if (value == '' || value.indexOf('AreaDenial') > -1)
							return;
						videoData += '&' + value;
					})
					videoData += `&AreaDenial=${x},${y},${width},${height}`;
					AjaxApi.request({
						url: '/Equipment/SetAreaDenial.do',
						data: { "id": data.ID, data: videoData },
						success: function ()
						{
							layer.msg("设置成功");
							FillDataTable();
							layer.close(AreaDialog);
						}
					})
				}
			})
		}

		var col = null;

		//选择导入文件
		function initupload()
		{
			upload.render({
				elem: '#LAY-excel-upload' //绑定元素
				, auto: false //选择文件后不自动上传
				, accept: 'file'
				, choose: function (obj)
				{// 选择文件回调
					var files = obj.pushFile()
					// var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
					var fileArr = Object.keys(files).map(function (key)
					{
						return files[key];
					});
					// 用完就清理掉，避免多次选中相同文件时出现问题
					for (var index in files)
					{
						if (files.hasOwnProperty(index))
						{
							delete files[index]
						}
					}
					$('#LAY-excel-upload').next().val('');
					init(fileArr);
				}
			});
		}
		//导入数据
		function init(files)
		{
			excel.importExcel(files, {}, function (data)
			{
				data = excel.filterImportData(data, {
					'Name': 'A'
					, 'Area': 'B'
					, 'Unit': 'C'
					, 'LandMark': 'D'
					, 'Url': 'E'
					, 'Data': 'F'
				});

				if (data[0].Sheet1.length > 0)
				{
					data[0].Sheet1.splice(0, 1);
					if (data[0].Sheet1.length > 200)
					{
						layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					for (var i = 0; i < data[0].Sheet1.length; i++)
					{
						if ($.trim(data[0].Sheet1[i].Name + '') == '')
						{
							layer.alert(lang('Unit名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}

						switch (data[0].Sheet1[i].Data)
						{
							case '始终录屏':
								data[0].Sheet1[i].Data = '&Recording=A';
								break;
							case '操作时录屏':
								data[0].Sheet1[i].Data = '&Recording=C';
								break;
							case '观看时录屏':
								data[0].Sheet1[i].Data = '&Recording=W';
								break;
							case '手动录屏':
								data[0].Sheet1[i].Data = '&Recording=N';
								break;
						}

					}
				}
				AjaxApi.request({
					type: "post",
					url: "../Tool/ImportData.do",
					data: JSON.stringify(data[0].Sheet1),
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						var names = '';
						if (!!res.msg)
						{
							var msgArr = res.msg.split('|');
							if (msgArr.length > 1)
							{
								res.msg = msgArr[0];
								names = msgArr[1];
							}
						}
						if (res.code == "Success")
						{
							layer.alert(lang('导入成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else if (res.msg == "Exist")
						{
							layer.alert(names + lang(' Unit已存在！'), { btn: [lang('确定')], title: lang("增加Unit"), });
						}
						else
						{
							layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加Unit"), });
						}
						FillDataTable();
					}
				})
			});
		}
		var username = '';
		var gData = '';
		function initRoleSelect()
		{
			if (configData == null)
				return;
			for (var con = 0; con < configData.length; con++)
			{
				var key = configData[con].Name;
				var value = key.split('/');
				if (value.length > 1)
				{
					$("#selRole").append("<option value='" + key + "'>" + value[1] + "</option>");
				}
			}
			form.render('select');
		}
		function FillDataTable()
		{
			if (window.top)
			{
				window.top.FillFavTreeTable();
			}
			var data = form.val('form-select');
			if (data.type == '-')
				delete data.type;

			refreshAreaAndGroup('', '', '');
			var gData = { pageCount: 0, limitCount: limitPageCount, namelike: data.name, type: data.type };
			AjaxApi.request({
				url: "/Equipment/GetEquipment.do",
				data: gData,
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					var res_data = refillData(res.data);
					var res_count = res.count;
					var gdata = { ids: '' };
					for (var i = 0; i < res_data.length; i++)
					{
						if (!res_data[i].Watcher)
							continue;
						else if (gdata.ids.indexOf(res_data[i].Watcher) >= 0)
							continue;

						gdata.ids += ',' + res_data[i].Watcher;
					}

					AjaxApi.request({
						url: "/User/GetUserListByID.do",
						data: gdata,
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);

							res_data.forEach(function (item)
							{
								var temp = arealistData.filter(function (i) { return i.ID == item.Area });
								if (temp.length > 0)
									item.areaname = temp[0].Name;
								//item.areaname = arealistData.filter(function (i, index, arr) { i.ID == item.Area.substr(32, 64) })[0].Name;
								var temp1 = toolgrouplistData.filter(function (i, index, arr) { return i.ID == item.Unit })
								if (temp1.length > 0)
									item.unitname = temp1[0].Name;

								if (!!res.data)
									res.data.filter(function (sub)
									{
										if (!!sub && item.Sweeper == sub.ID)
											item.WatcherName = sub.Name;
									});
							});

							username = top.login.Name;
							laypage.render({
								elem: 'footer'
								, count: res_count
								, theme: '#FF5722'
								, limit: limitPageCount
								, prev: lang('上一页')
								, next: lang('下一页')
								, layout: ['count', 'prev', 'page', 'next', 'skip']
								, jump: function (obj, first)
								{
									pageCount = obj.curr;
									if (first)
									{
										//col[col.length - 1].hide = true;
										//加载table数据
										table.reload('toollist', {
											data: res_data,
											page: false,
											cols: [col],
										});
									}
									else
									{
										setTable({ pageCount: pageCount - 1, limitCount: limitPageCount, namelike: data.name, type: data.type });
									}
									laypageEN();//翻页翻译成英文方法
								}
							});
						}
					})
				}
			})
		}

		function setTable(data)
		{
			AjaxApi.request({
				url: "/Equipment/GetEquipment.do",
				data: data,
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					var res_data = refillData(res.data);
					var res_count = res.count;
					var gdata = { ids: '' };
					for (var i = 0; i < res_data.length; i++)
					{
						if (!res_data[i].Watcher)
							continue;
						else if (gdata.ids.indexOf(res_data[i].Watcher) >= 0)
							continue;

						gdata.ids += ',' + res_data[i].Watcher;
					}

					AjaxApi.request({
						url: "/User/GetUserListByID.do",
						data: gdata,
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);

							if (!!res.data && !!res.data.length)
								res_data.forEach(function (item)
								{
									var temp = arealistData.filter(function (i) { return i.ID == item.Area });
									if (temp.length > 0)
										item.areaname = temp[0].Name;

									var temp1 = toolgrouplistData.filter(function (i, index, arr) { return i.ID == item.Unit })
									if (temp1.length > 0)
										item.unitname = temp1[0].Name;
									res.data.filter(function (sub)
									{
										if (!!sub && item.Sweeper == sub.ID)
											item.WatcherName = sub.Name;
									});
								});

							//col[col.length - 1].hide = true;
							//加载table数据
							table.reload('toollist', {
								data: res_data,
								page: false,
								cols: [col],
							});
						}
					})
				}
			})
		}
		function show()
		{
			if ($('#pwd').val().length == 0)
				$($('p')[0]).css("visibility", "visible");
			else
				$($('p')[0]).css("visibility", "hidden");
		}

		var arealistData = [];
		var toolgrouplistData = [];

		function refreshAreaAndGroup(area, unit, id)
		{
			AjaxApi.request({
				url: "/Tool/GetAreaAndGroupListAll.do",
				data: {},
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);

					if (res.arealist.length == 0 && res.toolgrouplist.length == 0)
					{
						//layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加Unit"), });
					}
					else
					{
						arealistData = buildTree(res.arealist);
                        toolgrouplistData = buildTree(res.toolgrouplist);
                        for (var i = 0; i < arealistData.length; i++) {
                            if (arealistData[i].Parent) {
                                arealistData[i].icon =
                                    '<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
                            } else {
                                arealistData[i].icon =
                                    '<i class="layui-icon layui-icon-layer"></i>';
                            }
                        }
                        for (var i = 0; i < toolgrouplistData.length; i++) {
                            if (toolgrouplistData[i].Parent) {
                                toolgrouplistData[i].icon =
                                    '<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
                            } else {
                                toolgrouplistData[i].icon =
                                    '<i class="layui-icon layui-icon-layer"></i>';
                            }
                        }

						findRoot();

						if (id !== '')
							setAreaAndGroup(area, unit, id);
					}
				}
			})
		}

		function findRoot()
		{
			for (var i = 0; i < arealistData.length; i++)
			{
				var item = arealistData[i];
				if (arealistData.filter(function (value) { value.Parent == item.ID }).length > 0)
				{
					arealistData.splice(i, 1);
					i--;
				}
			}

			for (var i = 0; i < toolgrouplistData.length; i++)
			{
				var item = toolgrouplistData[i];
				if (toolgrouplistData.filter(function (value) { value.Parent == item.ID }).length > 0)
				{
					toolgrouplistData.splice(i, 1);
					i--;
				}
			}
		}

		function setAreaAndGroup(area, unit, id)
		{
			var arealist = arealistData;
			var toolgrouplist = toolgrouplistData;

			$("#areaname" + id).empty();
			$("#toolgroupname" + id).empty();

			$("#areaname" + id).append("<option>不选</option>");

			//var rootarealist = arealist.filter((item, index, arr) => item.Parent === '');
			//setArea(area, id, rootarealist);
			for (var i = 0; i < arealist.length; i++)
			{
				var value = arealist[i].ID;
				var name = arealist[i].Name;

				var option = '';
				if (!!area && !!area.length && area == name)
					option = "<option selected value='" + value + "'>" + name + "</option>";
				else
					option = "<option value='" + value + "'>" + name + "</option>";

				$("#areaname" + id).append(option);
			}

			$("#toolgroupname" + id).append("<option>不选</option>");
			for (var i = 0; i < toolgrouplist.length; i++)
			{
				var value = toolgrouplist[i].ID;
				var name = toolgrouplist[i].Name;

				var option = '';
				if (!!unit && !!unit.length && unit == name)
					option = "<option selected value='" + value + "'>" + name + "</option>";
				else
					option = "<option value='" + value + "'>" + name + "</option>";

				$("#toolgroupname" + id).append(option);
			}

			form.render('select');
			setTimeout(function ()
			{
				addSelectIcon(id);
			}, 100);
		}
        function addSelectIcon(id) {
            var areaSelect = $("#areaname" + id);
            var groupSelect = $("#toolgroupname" + id);
            // 添加自定义图标
            var areaDl = areaSelect.next("div.layui-form-select").find("dl");
			areaDl.find("dd:not(.layui-select-tips)").each(function (index) {
                if ($(this).text() != '不选' && arealistData[index-1]) {
                    var icon = arealistData[index-1].icon;
                    $(this).html(
                        '<span class="option-icon">' +
                        icon +
                        '</span><span class="option-text" style="padding-left: 10px;">' +
                        $(this).text() +
                        "</span>"
                    );
                }
            });
            var groupDl = groupSelect.next("div.layui-form-select").find("dl");
            groupDl.find("dd:not(.layui-select-tips)").each(function (index) {
                if ($(this).text() != '不选' && toolgrouplistData[index - 1]) {
                    var icon = toolgrouplistData[index - 1].icon;
                    $(this).html(
                        '<span class="option-icon">' +
                        icon +
                        '</span><span class="option-text" style="padding-left: 10px;">' +
                        $(this).text() +
                        "</span>"
                    );
                }
            });
        }

		function setArea(area, id, arr)
		{
			for (var rI = 0; rI < arr.length; rI++)
			{
				var root = arr[rI];
				var sublist = arealistData.filter(function (item) { item.Parent == root.ID });

				if (sublist.length != 0)
				{
					$("#areaname" + id).append('<optgroup label="' + root.Name + '">');
					setArea(area, id, sublist);
				}
				else
				{
					var value = '';
					var name = root.Name;
					if (root.Parent.length != 0)
						value = root.Parent + root.ID;
					else
						value = root.ID + root.ID;

					var option = '';
					if (!!area && !!area.length && area == name)
						option = "<option selected value='" + value + "'>" + name + "</option>";
					else
						option = "<option value='" + value + "'>" + name + "</option>";

					$("#areaname" + id).append(option);
				}
			}
		}

		function refillData(data)
		{
			for (var i = 0; i < data.length; i++)
			{
				$.parseUrl(data[i].Data, data[i]);
				$.parseUrl(data[i].Data9, data[i]);

				data[i].xuhao = i + 1;

				switch (data[i].Recording)
				{
					case 'A':
						data[i].RecordingName = '始终录屏';
						break;
					case 'C':
						data[i].RecordingName = '操作时录屏';
						break;
					case 'W':
						data[i].RecordingName = '观看时录屏';
						break;
					case 'N':
						data[i].RecordingName = '手动录屏';
						break;
				}
			}

			return data;
		}

        function buildTree(items) {
            var result = [];
            var map = {};
            var i, item;

            for (i = 0; i < items.length; i++) {
                item = items[i];
                map[item.ID] = item;
            }

            for (i = 0; i < items.length; i++) {
                item = items[i];
                if (item.Parent) {
                    var parent = map[item.Parent];
                    if (parent) {
                        if (!parent.children) {
                            parent.children = [];
                        }
                        parent.children.push(item);
                    }
                } else {
                    result.push(item);
                }
            }

            function flattenTree(arr) {
                var flatArray = [];

                function flatten(nodes) {
                    var i;
                    for (i = 0; i < nodes.length; i++) {
                        flatArray.push(nodes[i]);
                        if (nodes[i].children && nodes[i].children.length) {
                            flatten(nodes[i].children);
                            delete nodes[i].children;
                        }
                    }
                }

                flatten(arr);
                return flatArray;
            }

            return flattenTree(result);
        }

	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>Device 设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<img id="recipe_img" style="width:1px; height:1px;" />
						<lable class="layui-form-label" style="width:85px;">Device名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
							   oninput="cleanSpelChar(this)">
					</div>
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">Device类型：</lable>
					</div>
					<div class="layui-inline">
						<select name="type" id="type" lay-search="">
							<option value="-" selected="selected">-</option>
							<option value="KVM">PC</option>
							<option value="CCTV">CCTV</option>
							<option value="Touch">TouchPanel</option>
						</select>
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="toolSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="toollist" lay-filter="tooltable"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="userListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="tooltoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加 PC')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAddCctv">{{lang('增加 CCTV')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAddTouch">{{lang('增加 Touch Panel')}}</button>
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
					</div>
				</div>
				<div class="layui-inline" id="batchdiv" style="display:none" name="batchdiv">
					<div class="layui-inline">
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="ConfirmBatchDelete" id="batchdeleteConfirmBtn">{{lang('批量删除确定')}}</button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm" lay-event="CancelBatchDelete" id="batchdeleteCancelBtn">{{lang('取消')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="operation">
			<!--<a class="layui-btn layui-btn-xs m_UserBC" name="eventChange" lay-event="change">{{lang('切换Recipe')}}</a>-->
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventArea" lay-event="area">{{lang('区域')}}</a>
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
		</script>
		<script type="text/html" id="checkedOne">
			<input type="checkbox" name="like1[write]" lay-skin="primary">
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="addkvm" class="layui-form" lay-filter="form-addkvm" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device类型</label>
			</div>
			<div class="layui-inline edit">
				<select name="scheme" id="scheme" lay-search="">
					<option value="MC" selected="selected">MC</option>
					<option value="NQVM">RCS</option>
					<option value="NQVM_">OF</option>
					<option value="NQVM_">Aten</option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaname" id="areaname_pc" lay-filter="areaname_pc" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupname" id="toolgroupname_pc" lay-filter="toolgroupname_pc" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device EQP ID</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="eqp" autocomplete="off" placeholder="请输入eqp名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device地标位置</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">DeviceIP地址</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item txMark txMarkHide">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device 标号</label>
			</div>

			<div class="layui-inline">
				<input type="text" class="layui-input" name="signal" autocomplete="off" placeholder="请输入Device标号"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device 副屏IP</label>
			</div>

			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="slaveip" autocomplete="off" placeholder="请输入Device副屏IP"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item god-v-p">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">端口映射</label>
			</div>

			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="query-hub" autocomplete="off" placeholder="请输入代理 IP" oninput="cleanNoIPADRESS(this)"/>
				<div>&nbsp;</div>
				<input type="text" class="layui-input" name="query-vp" autocomplete="off" placeholder="请输入视频端口"/>
				<div>&nbsp;</div>
				<input type="text" class="layui-input" name="query-cp" autocomplete="off" placeholder="请输入控制端口"/>
			</div>
		</div>

		<div class="layui-form-item god-v-r">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device录屏模式</label>
			</div>
			<div id="pc_record" class="layui-inline edit" lay-filter="pcrecord">
				<span class="god-v-rn"><input type="radio" name="recording" value="N" title="手动录屏" checked=""></span>
				<span class="god-v-rw"><input type="radio" name="recording" value="W" title="观看时录屏"></span>
				<span class="god-v-rc"><input type="radio" name="recording" value="C" title="操作时录屏"></span>
				<span class="god-v-rs"><input type="radio" name="recording" value="" title="跟随系统"></span>
			</div>
		</div>

		<div class="layui-form-item god god-v-sm">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">相对鼠标</label>
			</div>
			<div id="relative_mouse" class="layui-inline edit" lay-filter="query-relative">
				<select name="query-relative" id="query-relative" lay-search="">
					<option value="" selected="selected">否</option>
					<option value="1">KVM</option>
					<option value="2">Remote</option>
				</select>
			</div>
		</div>

		<div class="layui-form-item god-v-sp">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">同步互斥</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="query-pilot" autocomplete="off" placeholder="请输入互斥地址" oninput="cleanNoIPADRESS(this)"/>
			</div>
		</div>

		<div class="layui-form-item god-v-e" name="div-extra">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">扩展功能</label>
			</div>
			<div id="io_tools" class="layui-inline edit" lay-filter="query-extra">
				<select name="query-extra" id="query-extra" lay-search="">
					<option value="" selected="selected">无</option>
				</select>
				<div>&nbsp;</div>
				<input type="text" class="layui-input" name="query-extraMore" autocomplete="off" placeholder="请输入目标地址" oninput="cleanNoIPADRESS(this)"/>
			</div>
		</div>

		<div class="layui-form-item god-v-a">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">忽略权限</label>
			</div>
			<div id="ignore_sweep" class="layui-inline edit" lay-filter="query-sweep">
				<select name="query-sweep" id="query-sweep" lay-search="">
					<option value="" selected="selected">否</option>
					<option value="1">是</option>
				</select>
			</div>
		</div>
	</form>

	<form id="addcctv" class="layui-form" lay-filter="form-addcctv" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device类型</label>
			</div>
			<div class="layui-inline edit">
				<select name="scheme" id="scheme" lay-search="">
					<option value="TPLink" selected="selected">TPLink</option>
					<option value="HK">HK</option>
					<option value="DH">DH</option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaname" id="areaname_cctv" lay-filter="areaname_cctv" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupname" id="toolgroupname_cctv" lay-filter="toolgroupname_cctv" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device EQP ID</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="eqp" autocomplete="off" placeholder="请输入eqp名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device地标位置</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">DeviceIP</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">用户名/密码</label>
			</div>
			<div class="layui-inline">
				<input type="text" class="layui-input" name="user" autocomplete="off" placeholder="请输入用户名"
					   oninput="cleanNoIPADRESS(this)">
				<input type="text" style="margin-top: 10px;" class="layui-input" name="psd" autocomplete="off" placeholder="请输入密码"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item god-v-e" name="div-extra">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">扩展功能</label>
			</div>
			<div id="io_tools" class="layui-inline edit" lay-filter="query-extra">
				<select name="query-extra" id="query-extra" lay-search="">
					<option value="" selected="selected">无</option>
				</select>
				<div>&nbsp;</div>
				<input type="text" class="layui-input" name="query-extraMore" autocomplete="off" placeholder="请输入按钮地址" oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">同步互斥</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="query-pilot" autocomplete="off" placeholder="请输入互斥地址" oninput="cleanNoIPADRESS(this)" />
			</div>
		</div>
	</form>

	<form id="addtouch" class="layui-form" lay-filter="form-addtouch" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaname" id="areaname_touch" lay-filter="areaname_touch" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupname" id="toolgroupname_touch" lay-filter="toolgroupname_touch" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device EQP ID</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="eqp" autocomplete="off" placeholder="请输入eqp名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device地标位置</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device IP 地址</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device 端口</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="port" autocomplete="off" placeholder="请输入端口"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device 密码</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="psd" autocomplete="off" placeholder="请输入密码"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item god-v-e" name="div-extra">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">扩展功能</label>
			</div>
			<div id="io_tools" class="layui-inline edit" lay-filter="query-extra">
				<select name="query-extra" id="query-extra" lay-search="">
					<option value="" selected="selected">无</option>
				</select>
				<div>&nbsp;</div>
				<input type="text" class="layui-input" name="query-extraMore" autocomplete="off" placeholder="请输入按钮地址" oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">同步互斥</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="query-pilot" autocomplete="off" placeholder="请输入互斥地址" oninput="cleanNoIPADRESS(this)" />
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device录屏模式</label>
			</div>

			<div class="layui-inline edit">
				<span class="god-v-rn"><input type="radio" name="recording" value="N" title="手动录屏" checked=""></span>
				<span class="god-v-rw"><input type="radio" name="recording" value="W" title="观看时录屏"></span>
				<span class="god-v-rc"><input type="radio" name="recording" value="C" title="操作时录屏"></span>
				<span class="god-v-rs"><input type="radio" name="recording" value="" title="跟随系统"></span>
			</div>
		</div>
	</form>

	<form id="changeRecipe" class="layui-form" lay-filter="form-changeRecipe" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">是否启用RPA</label>
			</div>
			<div class="layui-inline edit">
				<input type="checkbox" name="enable" lay-skin="switch" lay-text="启用|关闭" title="">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Recipe Type</label>
			</div>
			<div class="layui-inline edit">
				<select name="recipe" id="scheme" lay-search="">
					<option value="1" selected="selected">Recipe1</option>
					<option value="2">Recipe2</option>
					<option value="3">Recipe3</option>
					<option value="4">Recipe4</option>
					<option value="5">Recipe5</option>
				</select>
			</div>
		</div>
	</form>

	<form id="chooseArea" style="display: none; height: calc(100% - 2px); width: calc(100% - 2px);">
		<div id="img-container">
			<div id="resizable-div">
				<div class="handle" id="top-left"></div>
				<div class="handle" id="top-right"></div>
				<div class="handle" id="bottom-left"></div>
				<div class="handle" id="bottom-right"></div>
				<div class="handle" id="top-center"></div>
				<div class="handle" id="bottom-center"></div>
				<div class="handle" id="middle-left"></div>
				<div class="handle" id="middle-right"></div>
			</div>
		</div>
	</form>
</body>
</html>