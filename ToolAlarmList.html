<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Layui</title>
    <!-- ע�⣺�����ֱ�Ӹ������д��뵽���أ�����css·����Ҫ�ĳ��㱾�ص� -->
    <link rel="stylesheet" href="lib/layui/css/layui.css">
    <link href="css/common.css" rel="stylesheet" />
    <link rel="stylesheet" href="/user/loginMenu.docss" />
    <link rel="stylesheet" href="lib/layui/treetable/treetable.css" />
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script src="lib/layui/layui.js"></script>
    <script type="text/javascript" src="lib/pickgold.js"></script>
    <script src="lib/layui/excel.js"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script src="js/common.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
</head>
<body>

    <table class="layui-table" id="test"></table>


    <script>
        layui.use('table', function(){
          var table = layui.table;

          table.render({
            elem: '#test'
            ,cols: [[
              {field:'id', width:80, title: 'ID', sort: true}
              ,{field:'username', width:80, title: '�û���'}
              ,{field:'sex', width:80, title: '�Ա�', sort: true}
              ,{field:'city', width:80, title: '����'}
              ,{field:'sign', title: 'ǩ��', width: '30%', minWidth: 100} //minWidth���ֲ����嵱ǰ��Ԫ�����С��ȣ�layui 2.2.1 ����
              ,{field:'experience', title: '����', sort: true}
              ,{field:'score', title: '����', sort: true}
              ,{field:'classify', title: 'ְҵ'}
              ,{field:'wealth', width:137, title: '�Ƹ�', sort: true}
            ]]
          });
        });
    </script>

</body>
</html>