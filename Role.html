<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>角色管理</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style>
		.borderStyle {
			border: 1px solid red;
		}

		.field-float {
			float: left;
			min-height: 55px;
			padding: 7px;
			margin: 0px 5px 10px 5px;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/request.js"></script>
	<script type="text/javascript" src="js/menu/menu.js"></script>
	<script type="text/javascript" id="function generateMenus(html)">
		function generateMenus(html)
		{
			if (!arguments.length)
			{
				if (!window.CS.menus)
					return;

				html = arguments.callee.call(window.CS.menus, '');
				$('#auth-values').html(html);
				form.render();
				return html;
			}
			if (!this.length)
				return html + '<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"/></div>';
			for (var i = 0; i < this.length; i++)
			{
				var v = this[i];
				if (!v.child)
					html = html + '<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_' + v.key + '" name="' + v.key + '" value="' + v.key + '" title="' + v.text + '" lay-skin="primary" lay-filter="checkboxItem" /></div>\r\n';
				else
					html = arguments.callee.call(v.child, html + '<fieldset class="layui-elem-field field-float" id="' + v.key + '">\r\n\
<legend>\r\n\
<input type="checkbox" class="layui-form-checkbox" id="m_' + v.key + '" name="' + v.key + '" value="' + v.key + '" title="' + v.text + '" lay-skin="primary" lay-filter="checkboxItem" />\r\n\
</legend>\r\n') + '</fieldset>\r\n';
			}
			return html;
		}
	</script>

	<script type="text/javascript" id="generateRoleMenus">
		function generatePermissionFieldsets(menuData)
		{
			var fieldsetsHTML = '';

			for (var i = 0; i < menuData.length; i++)
			{
				var menu = menuData[i];

				if (menu.type === 'group' && menu.children)
				{
					fieldsetsHTML += generateGroupFieldset(menu);
				} else if (menu.type === 'item' || menu.special)
				{
					fieldsetsHTML += generateItemFieldset(menu);
				}
			}

			return fieldsetsHTML;
		}

		function generateGroupFieldset(menu)
		{
			var html = '<fieldset class="layui-elem-field field-float" id="' + menu.id + '">\n';
			html += '    <legend>\n';
			html += '        <input type="checkbox" class="layui-form-checkbox" id="m_' + menu.id + '" name="' + menu.id + '" value="' + menu.id + '" title="' + menu.name + '" lay-skin="primary" lay-filter="checkboxItem">\n';
			html += '    </legend>\n';

			// 生成子菜单
			if (menu.children && menu.children.length > 0)
			{
				for (var i = 0; i < menu.children.length; i++)
				{
					var child = menu.children[i];
					html += generateChildFieldset(child);
				}
			}

			html += '</fieldset>\n';
			return html;
		}

		function generateChildFieldset(child)
		{
			var html = '    <fieldset class="layui-elem-field field-float" id="' + child.id + '">\n';
			html += '        <legend>\n';
			html += '            <input type="checkbox" class="layui-form-checkbox" id="m_' + child.id + '" name="' + child.id + '" value="' + child.id + '" title="' + child.name + '" lay-skin="primary" lay-filter="checkboxItem">\n';
			html += '        </legend>\n';

			if (child.operations && child.operations.length > 0)
			{
				html += '        <div class="layui-field-box">';
				for (var j = 0; j < child.operations.length; j++)
				{
					var operation = child.operations[j];
					html += '<input type="checkbox" class="layui-form-checkbox" id="m_' + operation + '" name="' + operation + '" value="' + operation + '" title="增删改" lay-skin="primary" lay-filter="checkboxItem">';
					html += '<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div>';
				}
				html += '</div>\n';
			} else
			{
				html += '        <div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>\n';
			}

			html += '    </fieldset>\n';
			return html;
		}

		function generateItemFieldset(menu)
		{
			var parentGroup = getParentGroup(menu);

			var html = '<fieldset class="layui-elem-field field-float" id="' + parentGroup.id + '">\n';
			html += '    <legend>\n';
			html += '        <input type="checkbox" class="layui-form-checkbox" id="m_' + parentGroup.id + '" name="' + parentGroup.id + '" title="' + parentGroup.name + '" lay-skin="primary" lay-filter="checkboxItem">\n';
			html += '    </legend>\n';

			html += '    <fieldset class="layui-elem-field field-float" id="' + menu.id + '">\n';
			html += '        <legend>\n';
			html += '            <input type="checkbox" class="layui-form-checkbox" id="m_' + menu.id + '" name="' + menu.id + '" value="' + menu.id + '" title="' + menu.name + '" lay-skin="primary" lay-filter="checkboxItem">\n';
			html += '        </legend>\n';

			if (menu.operations && menu.operations.length > 0)
			{
				html += '        <div class="layui-field-box">';
				for (var j = 0; j < menu.operations.length; j++)
				{
					var operation = menu.operations[j];
					html += '<input type="checkbox" class="layui-form-checkbox" id="m_' + operation + '" name="' + operation + '" value="' + operation + '" title="增删改" lay-skin="primary" lay-filter="checkboxItem">';
					html += '<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div>';
				}
				html += '</div>\n';
			} else
			{
				html += '        <div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>\n';
			}

			html += '    </fieldset>\n';
			html += '</fieldset>\n';
			return html;
		}

		function getParentGroup(menu)
		{
			if (menu.id === 'AlarmSetup')
			{
				return { id: 'othermanagement', name: '其他' };
			} else if (menu.id === 'SummarySearch')
			{
				return { id: 'othermanagement', name: '其他' };
			} else if (menu.id === 'NoticeSetup')
			{
				return { id: 'othermanagement', name: '其他' };
			}

			return { id: 'othermanagement', name: '其他' };
		}

		function convertMenuToPermissionForm(menuData)
		{
			var html = '';
			var otherMenus = [];

			for (var i = 0; i < menuData.length; i++)
			{
				var menu = menuData[i];

				if (menu.type === 'group' && menu.children)
				{
					html += generateGroupFieldset(menu);
				} else
				{
					otherMenus.push(menu);
				}
			}

			if (otherMenus.length > 0)
			{
				html += '<fieldset class="layui-elem-field field-float" id="othermanagement">\n';
				html += '    <legend>\n';
				html += '        <input type="checkbox" class="layui-form-checkbox" id="m_othermanagement" name="othermanagement" title="其他" lay-skin="primary" lay-filter="checkboxItem">\n';
				html += '    </legend>\n';

				for (var j = 0; j < otherMenus.length; j++)
				{
					var menu = otherMenus[j];
					html += '    <fieldset class="layui-elem-field field-float" id="' + menu.id + '">\n';
					html += '        <legend>\n';
					html += '            <input type="checkbox" class="layui-form-checkbox" id="m_' + menu.id + '" name="' + menu.id + '" value="' + menu.id + '" title="' + menu.name + '" lay-skin="primary" lay-filter="checkboxItem">\n';
					html += '        </legend>\n';

					if (menu.operations && menu.operations.length > 0)
					{
						html += '        <div class="layui-field-box">';
						for (var k = 0; k < menu.operations.length; k++)
						{
							var operation = menu.operations[k];
							html += '<input type="checkbox" class="layui-form-checkbox" id="m_' + operation + '" name="' + operation + '" value="' + operation + '" title="增删改" lay-skin="primary" lay-filter="checkboxItem">';
							html += '<div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div>';
						}
						html += '</div>\n';
					} else
					{
						html += '        <div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>\n';
					}

					html += '    </fieldset>\n';
				}

				html += '</fieldset>\n';
			}

			return html;
		}
	</script>
	<script type="text/javascript">
		function editItem(data)
		{
			var op = {
				data: data
				, type: 1
				, resize: false
				, id: "isAdd"
				, area: ['70%', '80%']
				, content: $('#editMenuAuth')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
			};
			if (!op.data)
			{
				op.title = lang('添加角色');
			}
			else
			{
				op.roleName = op.data.Name.substring(op.data.Name.indexOf('/') + 1, op.data.Name.length);
				op.title = lang('编辑角色') + ' ( ' + op.roleName + ' )';
			}
			op.success = function ()
			{
				if (!op.data)
					return;

				$('#rolename').val(op.roleName);
				if (!op.data.Data)
					return;

				/*for (let item in CS.Menu)
				{
					if (!isNaN(CS.Menu[item]) && op.data.Data.indexOf(CS.Menu['$' + item]) >= 0)
		$('#m_' + item).prop('checked', true);
				}*/
				var checks = op.data.Data.split(',');
				for (let item of checks)
				{
					$('#m_' + item).prop('checked', true);
				}
				form.render();
			};
			op.ajaxCB = function (result)
			{
				var res = $.eval(result);
				if (res.code == 'Success')
				{
					layer.msg(lang('修改成功'));
					if (!window.CS.ignoreAuthEdit && !!op.data && op.data.Name == window.login.Role)
					{
						layer.alert(lang('当前用户角色已修改，请重新登录系统！'), { icon: 1, closeBtn: 0, btn: [lang('确定')], title: lang('提示') }, function (index)
						{
							if (!!window.matrix)
								window.matrix.window.Data('close', '');

							top.window.location.href = top.window.location.href.replace(/index\.html/img, 'Login.html');
						});
					}
					FillDataTable();
				}
				else
				{
					layer.msg(res.msg);
				}
				layer.close(op.index);
				$('#editMenuAuth input[type="checkbox"]').removeAttr('checked');
			};
			op.btn1 = function (value, index)
			{
				var vs = { menu: ',' };
				vs.name = $('#rolename').val();
				if (!!op.data)
				{
					if (!vs.name)
						vs.name = op.roleName;
					vs.id = op.data.ID;
				}
				$("#editMenuAuth input[type='checkbox']").each(function (index, item)
				{
					if (item.checked == true && $(this).val().length > 0)
						vs.menu = vs.menu + $(this).val() + ',';//向数组中添加元素
				});
				AjaxApi.request({
					url: "../Role/AddRoleMenu.do",
					data: vs,
					dataType: 'text',
					success: op.ajaxCB
				})
			};
			op.end = function ()
			{
				$('#editMenuAuth')[0].reset();
				$('#editMenuAuth').css('display', 'none');
			};
			op.index = layer.open(op);
		}
	</script>
	<script type="text/javascript">
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel', 'upload'], function ()
		{
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, upload = layui.upload
				, excel = layui.excel;
			lang.call(this, $);
			$('#auth-values').html(convertMenuToPermissionForm(RCS_Menu));
			// generateMenus.call(window);
			table.init('userVideo', []);
			limitPageCount = 20;
			currentPage = 1;
			var col = columnsName();
			//创建Table的列名
			tableIni = table.render({
				elem: '#roleList'
				, even: true
				, sort: true
				, loading: false
				, toolbar: hasButtonPermission('RoleOptionSetup') ? '#usertoolbar' : []
				, defaultToolbar: []
				, id: "roleList"
				, title: lang('用户数据表')
				, cols: [col]
				, data: []
				, height: 'full-140'
				, page: {
					theme: '#FF5722'
					, layout: ['count', 'prev', 'page', 'next', 'skip']
					, prev: lang('上一页')
					, next: lang('下一页')
					, limit: limitPageCount
				}
			});
			pageCount = 1;
			FillDataTable();

			form.on('checkbox(checkboxall)', function (data)
			{
				$('#editMenuAuth input[type="checkbox"]').each(function (index, item)
				{
					item.checked = data.elem.checked;
				})

				form.render('checkbox');
			});

			form.on('checkbox(checkboxItem)', function (data)
			{
				$("#" + data.elem.name + " input[type='checkbox']").each(function (index, item)
				{
					item.checked = data.elem.checked;
				});

				form.render('checkbox');
			});

			//头工具栏事件
			table.on('toolbar(userVideo)', function (obj)
			{
				var checkStatus = table.checkStatus(obj.config.id);
				switch (obj.event)
				{
					case 'getCheckData':
						var data = checkStatus.data;
						Delete(data);
						break;
					case 'isAdd':
						var isLdap = true;
						AjaxApi.request({
							url: "/User/LoginType.do",
							data: {},
							dataType: 'text',
							success: function (result)
							{
								if (result == "OK")
								{
									$('#div_pwd').hide();
									$('#div_confirmPwd').hide();
								}
								else
								{
									isLdap = false;
									$('#div_pwd').show();
									$('#div_confirmPwd').show();
								}
							},
							error: function (r)
							{
								layer.alert(lang(r.textContent), { btn: [lang('确定')], title: lang('提示') });
							}
						})

						editItem();
						break;
					//自定义头工具栏右侧图标 - 提示
					case 'LAYTABLE_TIPS':
						layer.alert(lang('这是工具栏右侧自定义的一个图标按钮'), { btn: [lang('确定')], title: lang('提示') });
						break;
					case 'BatchDelete':
						col[col.length - 1].hide = false;
						table.reload('roleList', {
							cols: [col],
						})

						$('#normaldiv').hide();
						$('#batchdiv').show();

						break;
					case 'CancelBatchDelete':
						{
							col[col.length - 1].hide = true;
							table.reload('roleList', {
								cols: [col],
							})

							$('#normaldiv').show();
							$('#batchdiv').hide();
							checkedItems = [];
							break;
						}
					case 'ConfirmBatchDelete':
						{
							BatchDelete();
							break;
						}
				};
			});

			function BatchDelete()
			{
				if (checkedItems.length == 0)
					return;

				/*var data = { };
		checkedItems.forEach(function (item, index, arr) {
			data.ids = item.ID + ',';
		data.names = item.Name + ',';
				});*/

				layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
				{
					AjaxApi.request({
						type: "post",
						url: "/Role/BatchDeleteRole.do",
						data: JSON.stringify(checkedItems),
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);
							if (res.code == "Success")
							{
								checkedItems = [];
								col[col.length - 1].hide = true;
								table.reload('arealist', {
									cols: [col],
								})

								$('#normaldiv').show();
								$('#batchdiv').hide();

								layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
							}
							else
							{
								layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除角色"), });
							}
							checkedItems = [];
							FillDataTable();
						}
					})
					layer.close(index);
				});
			}

			//监听行工具事件
			table.on('tool(userVideo)', function (obj)
			{
				if (obj.event === 'del')
				{
					Delete(obj.data);
					return;
				}

				if (obj.event === 'edit')
				{
					editItem(obj.data);
					return;
				}

				if (obj.event == 'singleclick')
				{
					var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
					if (checkCell.length > 0)
						checkCell.click();
					return;
				}
			});

			var checkedItems = [];
			table.on('checkbox(userVideo)', function (obj)
			{
				if (obj.checked)
				{
					if (obj.type == 'all')
					{
						checkedItems = table.cache.roleList;
					} else
					{
						checkedItems.push(obj.data);
					}
				} else
				{
					if (obj.type == 'all')
					{
						checkedItems = null;
					} else
					{
						var i = 0;
						$.each(checkedItems, function (key, value)
						{
							if (value.id == obj.data.id)
							{
								checkedItems.splice(i, 1);
								return false;
							}
							i++;
						})
					}
				}
			});

			form.on('submit(roleSearch)', function (data)
			{
				FillDataTable();
				return false;
			});
			form.on('select(selRoleVideo)', function (data)
			{
				FillDataTable();
			})
			$('button[type=button]').on("click", function ()
			{
				document.getElementById('form-select').reset();
				FillDataTable();
			})
			function columnsName()
			{
				var columnsArray = [];
				var columnsNames = [
					{
						title: lang("序号"), field: "xuhao", templet: function (e)
						{
							return limitPageCount * (currentPage - 1) + e.LAY_INDEX
						}
					}
					, {
						title: lang("角色名称"), field: "Name", templet: function (d)
						{
							if (!!d[this.field])
							{
								var role = d[this.field];
								var val = role.split('/');
								if (val.length > 1)
									return val[1];
							}

							return '';
						}
					}
					, { title: lang('更新者'), field: 'EditUser' }
					, { title: lang("更新时间"), field: "Edit", templet: templet }
				];
				for (var i = 0; i < columnsNames.length; i++)
				{
					var o = {
						title: columnsNames[i].title,
						field: columnsNames[i].field,
						align: 'center',
						event: 'singleclick',
						templet: columnsNames[i].templet,
						hide: columnsNames[i].hide
					};
					columnsArray.push(o);
				}
				var columnsLast = {
					fixed: 'right',
					minWidth: 220,
					title: lang("操作"),
					toolbar: "#barDemo",
					event: 'singleclick',
					align: 'center',
					hide: !hasButtonPermission('RoleOptionSetup')
				}
				columnsArray.push(columnsLast);

				var columnsLastAgain = {
					title: lang("选中"),
					type: 'checkbox',
					event: 'singleclick',
					align: 'center',
					hide: true,
				}
				columnsArray.push(columnsLastAgain);

				return columnsArray;
			}
			function Delete(data)
			{
				layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
				{
					AjaxApi.request({
						url: "/Role/DeleteRole.do",
						data: { id: data.ID, name: data.Name },
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);
							if (res.code == "Success")
							{
								layer.msg(lang('删除成功'), { icon: 1 });
								FillDataTable();
							}
							else
							{
								layer.msg(lang('删除失败'), { icon: 2 });
								layer.close(index);
							}
						},
						error: function (r)
						{
							layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
						}
					})
					layer.close(index);
				});
			}
		});

		//选择导入文件
		function initupload()
		{
			upload.render({
				elem: '#LAY-excel-upload' //绑定元素
				, auto: false //选择文件后不自动上传
				, accept: 'file'
				, choose: function (obj)
				{// 选择文件回调
					var files = obj.pushFile()
					// var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
					var fileArr = Object.keys(files).map(function (key)
					{
						return files[key];
					});
					// 用完就清理掉，避免多次选中相同文件时出现问题
					for (var index in files)
					{
						if (files.hasOwnProperty(index))
						{
							delete files[index]
						}
					}
					$('#LAY-excel-upload').next().val('');
					init(fileArr);
				}
			});
		}
		//导入数据
		function init(files)
		{
			excel.importExcel(files, {}, function (data)
			{
				data = excel.filterImportData(data, {
					'Name': 'A'
					, 'Dept': 'B'
					, 'Role': 'C'
				});

				if (data[0].Sheet1.length > 0)
				{
					data[0].Sheet1.splice(0, 1);
					if (data[0].Sheet1.length > 200)
					{
						layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					for (var i = 0; i < data[0].Sheet1.length; i++)
					{
						if ($.trim(data[0].Sheet1[i].Name + '') == '')
						{
							layer.alert(lang('用户名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}
						if ($.trim(data[0].Sheet1[i].Role + '') == '')
						{
							layer.alert(lang('用户角色不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}
						if (IsCheckSpecialCharUser(data[0].Sheet1[i].Name) > 0
							|| IsCheckSpecialCharUser(data[0].Sheet1[i].Dept > 0)
							|| IsCheckSpecialCharUser(data[0].Sheet1[i].Role > 0))
						{
							layer.alert(lang('存在特殊字符，禁止导入'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}
					}
				}
				AjaxApi.request({
					type: "post",
					url: "../User/ImportData.do",
					data: JSON.stringify(data[0].Sheet1),
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						var names = '';
						if (!!res.msg)
						{
							var msgArr = res.msg.split('|');
							if (msgArr.length > 1)
							{
								res.msg = msgArr[0];
								names = msgArr[1];
							}
						}
						if (res.code == "Success")
						{
							layer.alert(lang('导入成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else if (res.msg == "Exist")
						{
							layer.alert(names + lang(' 用户已存在！'), { btn: [lang('确定')], title: lang("增加用户"), });
						}
						else if (res.msg == "LDAP Not Found")
						{
							layer.alert(lang(' LDAP不存在此用户：') + names, { btn: [lang('确定')], title: lang("增加用户"), });
						}
						else
						{
							layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加用户"), });
						}
						FillDataTable();
					}
				})
			});
		}
		var username = '';
		var gData = '';

		function FillDataTable()
		{
			var data = form.val('form-select');
			gData = { Name: data.rolename };
			AjaxApi.request({
				url: "../Role/List.do",
				data: gData,
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					username = top.login.Name;
					var roleData = res.data.filter(function (item)
					{
						if (item.Data.indexOf('IsUserGroup') > 0)
						{
							return false;
						}
						return true;
					})
					for (var i = 0; i < roleData.length; i++)
					{
						$.parseUrl(roleData[i].Data9, roleData[i]);
						$.parseUrl(roleData[i].Data, roleData[i]);
					}
					//加载table数据
					table.reload('roleList', {
						data: roleData,
						page: {
							theme: '#FF5722'
							, prev: lang('上一页')
							, next: lang('下一页')
							, layout: ['count', 'prev', 'page', 'next']
							, curr: 1
						}
					});
				}
			})
		}

		function show()
		{
			if ($('#pwd').val().length == 0)
				$($('p')[0]).css("visibility", "visible");
			else
				$($('p')[0]).css("visibility", "hidden");
		}
	</script>
</head>
<body style="overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; min-height: 600px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>角色设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">角色名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="rolename" autocomplete="off" placeholder="请输入角色名称">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="roleSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="roleList" lay-filter="userVideo"></table>
		<script type="text/html" id="usertoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
						<!--<button type="button" class="layui-btn-danger layui-btn layui-btn-sm m_UserBC" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>-->
					</div>
				</div>
				<div class="layui-inline" id="batchdiv" style="display:none" name="batchdiv">
					<div class="layui-inline">
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm m_UserBC" lay-event="ConfirmBatchDelete" id="batchdeleteConfirmBtn">{{lang('批量删除确定')}}</button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="CancelBatchDelete" id="batchdeleteCancelBtn">{{lang('取消')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="barDemo">
			<a class="layui-btn layui-btn-xs m_RoleBC" name="eventEdit" lay-event="edit">{{lang('编辑')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_RoleBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="editMenuAuth" class="layui-form" lay-filter="form-editMenuAuth" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-field-box">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">角色名称</label>
				</div>
				<div class="layui-inline edit">
					<input type="text" class="layui-input" id="rolename" name="rolename" autocomplete="off" placeholder="请输入角色名称" oninput="cleanSpelChar(this)">
				</div>
			</div>

			<div class="layui-form-item" id="auth-values">
				<!--<fieldset class="layui-elem-field field-float" id="ToolManagement">
					<legend>
						<input type="checkbox" class="layui-form-checkbox" id="m_ToolManagement" name="ToolManagement" value="ToolManagement" title="机台管理" lay-skin="primary" lay-filter="checkboxItem">
					</legend>
					<fieldset class="layui-elem-field field-float" id="AreaSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_AreaSetup" name="AreaSetup" value="AreaSetup" title="区域设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_AreaOptionSet" name="AreaOptionSet" value="AreaOptionSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="ToolGroupSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_ToolGroupSetup" name="ToolGroupSetup" value="ToolGroupSetup" title="机台组设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_ToolGroupOptionSet" name="ToolGroupOptionSet" value="ToolGroupOptionSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="ToolSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_ToolSetup" name="ToolSetup" value="ToolSetup" title="机台设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_ToolOptionSet" name="ToolOptionSet" value="ToolOptionSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="EquipmentSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_EquipmentSetup" name="EquipmentSetup" value="EquipmentSetup" title="Device设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_EquipmentSet" name="EquipmentSet" value="EquipmentSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="ToolMachineSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_ToolMachineSetup" name="ToolMachineSetup" value="ToolMachineSetup" title="Device设置-1" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_ToolMachineSet" name="ToolMachineSet" value="ToolMachineSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="LayoutSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_LayoutSetup" name="LayoutSetup" value="LayoutSetup" title="Layout设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_LayoutSet" name="LayoutSet" value="LayoutSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
				</fieldset>
				<fieldset class="layui-elem-field field-float" id="LogManagement">
					<legend>
						<input type="checkbox" class="layui-form-checkbox" id="m_LogManagement" name="LogManagement" value="LogManagement" title="日志管理" lay-skin="primary" lay-filter="checkboxItem">
					</legend>
					<fieldset class="layui-elem-field field-float" id="ToolLog">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_ToolLog" name="ToolLog" value="ToolLog" title="机台日志" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="SystemLog">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_SystemLog" name="SystemLog" value="SystemLog" title="系统日志" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
				</fieldset>
				<fieldset class="layui-elem-field field-float" id="othermanagement">
					<legend>
						<input type="checkbox" class="layui-form-checkbox" id="m_othermanagement" name="othermanagement" title="其他" lay-skin="primary" lay-filter="checkboxItem" />
					</legend>
					<fieldset class="layui-elem-field field-float" id="NoticeSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_NoticeSetup" name="NoticeSetup" value="NoticeSetup" title="公告设置" lay-skin="primary" lay-filter="checkboxItem" />
						</legend>
						<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="AlarmSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_AlarmSetup" name="AlarmSetup" value="AlarmSetup" title="警报设置" lay-skin="primary" lay-filter="checkboxItem" />
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_AlarmOptionSet" name="AlarmOptionSet" value="AlarmOptionSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="SummarySearch">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_SummarySearch" name="SummarySearch" value="SummarySearch" title="汇总查询" lay-skin="primary" lay-filter="checkboxItem" />
						</legend>
						<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
				</fieldset>
				<fieldset class="layui-elem-field field-float" id="SystemSetup">
					<legend>
						<input type="checkbox" class="layui-form-checkbox" id="m_SystemSetup" name="SystemSetup" value="SystemSetup" title="系统管理" lay-skin="primary" lay-filter="checkboxItem">
					</legend>
					<fieldset class="layui-elem-field field-float" id="UserSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_UserSetup" name="UserSetup" value="UserSetup" title="用户设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_UserOptionSet" name="UserOptionSet" value="UserOptionSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="RoleSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_RoleSetup" name="RoleSetup" value="RoleSetup" title="角色设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_RoleOptionSetup" name="RoleOptionSetup" value="RoleOptionSetup" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="UserGroupSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_UserGroupSetup" name="UserGroupSetup" value="UserGroupSetup" title="用户组设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box"><input type="checkbox" class="layui-form-checkbox" id="m_UserGroupSet" name="UserGroupSet" value="UserGroupSet" title="增删改" lay-skin="primary" lay-filter="checkboxItem"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>增删改</span><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="AuthSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_AuthSetup" name="AuthSetup" value="AuthSetup" title="权限设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
					<fieldset class="layui-elem-field field-float" id="ParamSetup">
						<legend>
							<input type="checkbox" class="layui-form-checkbox" id="m_ParamSetup" name="ParamSetup" value="ParamSetup" title="参数设置" lay-skin="primary" lay-filter="checkboxItem">
						</legend>
						<div class="layui-field-box" style="visibility: hidden;"><input type="checkbox" class="layui-form-checkbox" lay-skin="primary"><div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i></div></div>
					</fieldset>
				</fieldset>-->
			</div>
		</div>
	</form>
</body>
</html>
