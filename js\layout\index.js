function layout() {
    myDiagram.layoutDiagram(true);
}

function save() {
    document.getElementById("mySavedModel").value = myDiagram.model.toJson();
    console.log(myDiagram.model.toJson());
    myDiagram.isModified = false;
}
function load()
{
    var nodes = localStorage.getItem('layoutNode');
    if (nodes)
    {
        myDiagram.model = go.Model.fromJson(nodes) //new go.GraphLinksModel(myNode.nodeDataArray, []);
    }
    
    // myDiagram.model = go.Model.fromJson(myDiagramNode);
    setTimeout(function () {
        whLoading.hide();
        $('#container').show();
    }, 1000)

    setViewMode(isEdit);
}

window.addEventListener("mousemove", function (event) {
    lastX = event.pageX;
    lastY = event.pageY;
});

window.addEventListener("click", function () {
    $("#tooltipInfo").hide();
});

if (window.init) {
    init();
    if (!isEdit) { // 非编辑模式
        $("#paletteWrap").hide();
        $("#nodeInfo").hide();
        $("#statusBtn").show();
    } else { // 编辑模式
        $("#paletteWrap").show();
        $("#statusBtn").hide();
    }
    load();
  // layout();
}
// 格式化节点信息
function formatGojsNode(e) {
    var data = null;
    e.diagram.selection.each(function (nodeOrLink) {
        if (nodeOrLink instanceof go.Node) {
            data = nodeOrLink.data;
        }
    });
    return data;
}
// 选择节点
function ChangedSelection(e) {
    var data = formatGojsNode(e);
    // console.log(111, data);
    var positonObj = {
        "0 0.5 0 0": "Left",
        "0.5 0.5 0 0": "Center",
        "1 0.5 0 0": "Right",
        "0 0 0 0": "TopLeft",
        "0.5 0 0 0": "TopCenter",
        "1 0 0 0": "TopRight",
        "0 1 0 0": "BottomLeft",
        "0.5 1 0 0": "BottomCenter",
        "1 1 0 0": "BottomRight",
    };
    if (data) {
        $("#nodeInfo-form").show();
        selectNode = data;
        var formData = {
            equipName: data.id,
            fill: data.fill || "#fff",
            strokeColor: data.strokeColor || "#000",
            textAlign: positonObj[data.textAlign] || "Center",
            textWidth: data.textWidth || ""
        };

        form.val("edit-equip", formData);
        function renderColorPicker(elem, color) {
            colorpicker.render({
                elem: elem,
                color: color,
                done: function (selectedColor) {
                    $(elem.replace('-form', '')).val(selectedColor);
                }
            });
        }

        renderColorPicker("#fillColor-form", formData.fill);
        renderColorPicker("#strokeColor-form", formData.strokeColor);
    } else {
        $("#nodeInfo-form").hide();
        selectNode = null;
    }
}
// 更新编辑节点
function updateNode() {
    var info = form.val("edit-equip");
    var existNodeNum = isExistNode(info.equipName);
    // console.log(selectNode, info, isExistNode(info.equipName), selectNode.id !== info.equipName);
    if (selectNode) {
        if (info.equipName) {
            if (existNodeNum > 0 && existNodeNum > 1) {
                layer.alert("设备名称已存在！！", {
                    btn: ["确定"],
                    title: "提示",
                });
                return;
            }
            if (existNodeNum === 1 && selectNode.id !== info.equipName) {
                layer.alert("设备名称已存在！！", {
                    btn: ["确定"],
                    title: "提示",
                });
                return;
            }
            selectNode.id = info.equipName;
            selectNode.text = equipMap.get(info.equipName).Name;
            selectNode.data = equipMap.get(info.equipName);
        }
        selectNode.key = info.equipName;
        selectNode.fill = info.fill;
        selectNode.strokeColor = info.strokeColor;
        selectNode.textAlign = info.textAlign;
        if (info.textWidth) {
            selectNode.textWidth = Number(info.textWidth);
        }
        myDiagram.model.updateTargetBindings(selectNode);
        // console.log(selectNode);
    }
}
// 双击节点
function doubleClickNode(e, node) {
  // var data = formatGojsNode(node);
  console.log(node, node.data);
}

// 删除节点
function deleteNode(e, node) {
  var nodeInfo = formatGojsNode(e);
  myDiagram.model.removeNodeData(nodeInfo);
  $("#nodeInfo-form").hide();
}
// 查看节点信息
function viewNode(e, node) {
  var nodeInfo = formatGojsNode(e);
  console.log(2222, nodeInfo);
}

// 更新节点数据
function updateNodeData(updatedData) {
  // 遍历每个更新的数据对象
  updatedData.forEach(function (data) {
    const node = myDiagram.findNodeForKey(data.key);
    if (node) {
      // 更新节点的数据
      myDiagram.model.setDataProperty(node.data, "fill", data.fill);
    }
  });
}
var models = [
  {
    text: "YEOXT11CCTV",
    figure: "Square",
    loc: "-142 -6",
    key: "1A3F804BE92548949E2F37DB7BED183A",
    angle: 330.5545712700744,
    id: "1A3F804BE92548949E2F37DB7BED183A",
    data: { ID: "1A3F804BE92548949E2F37DB7BED183A", Name: "YEOXT11CCTV" },
    fill: "red",
    strokeColor: "#000",
  },
];
setTimeout(function () {
  // updateNodeData(models);
}, 3000);

// 查找节点 显示
function findNode() {
  console.log(getAllNode());
  return;
  var data = {
    text: "YPCDH11CCTV",
    figure: "Hexagon",
    fill: "#bf2d2d",
    strokeColor: "#000",
    id: "1A3FAF0F13303F8186479898AF7B64B1",
    data: { ID: "1A3FAF0F13303F8186479898AF7B64B1", Name: "YPCDH11CCTV" },
    key: "1A3FAF0F13303F8186479898AF7B64B1",
    loc: "1157.4569604806377 722.3658096331134",
  };
  var node = myDiagram.findNodeForKey(data.key);
  myDiagram.centerRect(node.actualBounds);
  myDiagram.select(node);
}
