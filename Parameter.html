﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>参数设置</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style>
		.layui-form-label {
			width: 120px !important;
		}

		.layui-input-block {
			margin-left: 150px !important;
		}

		#oracel-sid, #postgresql-schema {
			display: none;
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/request.js"></script>
	<script type="text/javascript">
		layui.use(['element', 'layer', 'jquery', 'form', 'upload'], function ()
		{
			$ = layui.$
				, form = layui.form
				, layer = layui.layer
				, upload = layui.upload;
			lang.call(this, $);

			$('#log_path').text(CS['Variables']['Logger']);
			$('#record_path').text(CS['ConfigData']['VideoRecordPath']);
			$('#record_args').text(CS['ConfigData']['VideoRecordArgs']);
			$('#record_way').text(CS['ConfigData']['VideoRecordWay']);

			form.val('form-setting', {
				sqlType: "Oracle",
				record: "",
			});

			form.on('select()', function (data)
			{
				if (data.elem.id == 'db-type')
				{
					clearForm();
					hideSqlField();
					var type = data.value;
					switch (type)
					{
						case "MySQL":
							$('#legend-name').html('MySQL 设置');
							break
						case "Oracle":
							$('#legend-name').html('Oracle 设置');
							$('#oracel-sid').show();
							break
						case "SqlServer":
							$('#legend-name').html('SqlServer 设置');
							break
						case "PostgreSQL":
							$('#legend-name').html('PostgreSQL 设置');
							$('#postgresql-schema').show();
							break
					}
				}
			});

			var select = 'dd[lay-value="' + $('#db-type').val() + '"]';
			$('#db-type').siblings("div.layui-form-select").find('dl').find(select).click();
		});

		function clearForm()
		{
			$('#db-name').val('');
			$('#db-server').val('');
			$('#db-account').val('');
			$('#db-password').val('');
			$('#db-sid').val('');
			$('#db-schema').val('');
		}

		function hideSqlField()
		{
			$('#oracel-sid').hide();
			$('#postgresql-schema').hide();
		}

		function submitForm()
		{
			var data = {};
			data['DatabaseProvider'] = $('#db-type').val();
			data['ConnectionString'] = 'Server=' + $('#db-server').val() + ';Database=' + $('#db-name').val() + ';Uid=' + $('#db-account').val() + ';Pwd=' + $('#db-password').val() + ';charset=gb2312;Pooling=True;MinimumPoolSize=10;MaximumPoolSize=500;ConnectionLifeTime=300;ProcedureCacheSize=0;Default Command Timeout=20;Connection Timeout=5;AllowBatch=True;';
			data['VideoRecordPath'] = $('#record-path').val();
			data['VideoRecordArgs'] = $('#record-args').val();
			data['VideoRecordWay'] = $('#record-way').val();
			data['Logger'] = $('#log-path').val();
			data['LDAP'] = $('#ldap-url').val();
			AjaxApi.request({
				type: "post",
				url: "Setting.do",
				data: data,
				success: function (result)
				{
					layui.layer.msg('OK');
				},
				error: function (q)
				{
					layui.layer.msg(lang('操作失败，请重试！\r\n\r\n' + q.responseText))
				}
			})
			return false;
		}
	</script>
	<script type="text/javascript">
		function upgrade(data)
		{
			if (!Number.isInteger(data))
			{
				if (data == 'Delete')
				{
					window.top.Data('Upgrade', data);
					return;
				}

				layui.layer.msg(data);
				$('#msg-upgrade').text(data);
				return;
			}

			if (!data)
				$.get('../Upgrade.do?MachineName=&_' + Date.now(), arguments.callee);//?MachineName=
			else if (data > 0)
				$.get('../Upgrade.do?File=&_' + Date.now(), arguments.callee);
			else
				$.get('../Upgrade.do?Delete=Delete&_' + Date.now(), arguments.callee);
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-setting" id="form-setting" style="min-width: 800px; min-height: 600px; margin: 5px; " onsubmit="javascript: return submitForm.call(this);">
		<fieldset class="layui-elem-field layui-field-title">
			<legend>参数设置</legend>
		</fieldset>
		<div class="layui-field-box" style="width: 60%; margin: 10px auto;">
			<div class="layui-form-item">
				<label class="layui-form-label">数据库类型:</label>
				<div class="layui-input-block">
					<select name="sqlType" id="db-type" lay-filter="selRoleVideo">
						<option value="MySQL">MySQL</option>
						<option value="Oracle">Oracle</option>
						<option value="SqlServer">SqlServer</option>
						<option value="PostgreSQL">PostgreSQL</option>
					</select>
				</div>
			</div>
			<fieldset class="layui-elem-field" style="width: calc(100% - 150px); margin-left: 150px;">
				<legend id="legend-name">MySQL 设置</legend>
				<div class="layui-field-box">
					<div class="layui-form-item">
						<label class="layui-form-label">数据库名称:</label>
						<div class="layui-input-block">
							<input type="text" id="db-name" name="mysqlName" autocomplete="off" placeholder="请输入数据库名称" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">数据库地址:</label>
						<div class="layui-input-block">
							<input type="text" id="db-server" name="mysqlHost" autocomplete="off" placeholder="请输入数据库地址" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">用户名:</label>
						<div class="layui-input-block">
							<input type="text" id="db-account" name="mysqlUser" autocomplete="off" placeholder="请输入用户名" class="layui-input">
						</div>
					</div>
					<div class="layui-form-item">
						<label class="layui-form-label">密码:</label>
						<div class="layui-input-block">
							<input type="text" id="db-password" name="mysqlPass" autocomplete="off" placeholder="请输入密码" class="layui-input">
						</div>
					</div>
					<div id="oracel-sid" class="layui-form-item">
						<label class="layui-form-label">服务名或SID:</label>
						<div class="layui-input-block">
							<input type="text" id="db-sid" name="oracleSid" autocomplete="off" placeholder="请输入服务名或SID" class="layui-input">
						</div>
					</div>
					<div id="postgresql-schema" class="layui-form-item">
						<label class="layui-form-label">Schema:</label>
						<div class="layui-input-block">
							<input type="text" id="db-schema" name="postgresqlSchema" autocomplete="off" placeholder="请输入Schema" class="layui-input">
						</div>
					</div>
				</div>
			</fieldset>
			<div class="layui-form-item">
				<label class="layui-form-label">日志存放路径:</label>
				<div class="layui-input-block">
					<input type="text" id="log-path" name="logPath" autocomplete="off" placeholder="请输入日志存放路径" class="layui-input">
					当前运行值：<span id="log_path" style="color: coral;">%DataPath%\Logs\{0:dd}.log</span>
				</div>
			</div>
			<div class="layui-form-item">
				<lable class="layui-form-label">视频存放路径:</lable>
				<div class="layui-input-block">
					<input type="text" class="layui-input" id="record-path" name="videoPath" autocomplete="off" placeholder="请输入视频存放路径">
					<br />
					当前运行值：<span id="record_path" style="color: coral;">%DataPath%\Snapshot\{Authority}\{Time,,-1:yyyyMMdd\\HHmm}.mp4</span>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">录屏方式:</label>
				<div class="layui-input-block">
					<select id="record-way" name="record">
						<option value="" selected="selected">手动录屏</option>
						<option value="C">操作时</option>
						<option value="W">观看时</option>
						<option value="A">始终录屏</option>
					</select>
					<br />
					<span style="color: green;">系统默认设置，机台独立设置会覆盖此设置；</span>当前运行值：<span id="record_way" style="color: coral;">N</span>
				</div>
			</div>
			<div class="layui-form-item">
				<lable class="layui-form-label">录屏参数:</lable>
				<div class="layui-input-block">
					<input type="text" class="layui-input" id="record-args" name="videoArgs" autocomplete="off" placeholder="请输入录屏参数">
					<br />
					<span id="record_args" style="color: coral;">-f image2pipe -use_wallclock_as_timestamps 1 -i - -c:v libx264 -pix_fmt yuv420p -fps_mode passthrough -y "{0}"</span>
					<br />
					<span style="color: green;">专业人员设置，可以配置视频帧率，视频质量，编码方案等</span>
				</div>
			</div>
			<div class="layui-form-item">
				<lable class="layui-form-label">域地址:</lable>
				<div class="layui-input-block">
					<input type="text" class="layui-input" id="ldap-url" name="ldapUrl" autocomplete="off" placeholder="请输入域地址">
					<br />
					<span style="color: coral;">LDAP://127.0.0.1:389/CN=Users,DC=Cowin,DC=com</span>
				</div>
			</div>
			<div class="layui-form-item" style="text-align: center;">
				<button type="submit" class="layui-btn" lay-filter="userSearch" lay-submit=""><span>确 认</span></button>
				<br />
				<span style="color: red;">系统配置将在下次服务端启动时生效</span>
			</div>
		</div>
	</form>
	<div>
		<input type="button" value=" 检查客户端更新 " onclick="javascript: upgrade.call(this, 0);" />
		-
		<input type="button" value=" 生成客户端更新 " onclick="javascript: upgrade.call(this, 1);" />
		-
		<input type="button" value=" 删除客户端更新 " onclick="javascript: upgrade.call(this, -1);" />
		-
		<input type="button" value=" 清除本地更新 " onclick="javascript: upgrade.call(this, 'Delete');" />
		-
		<span id="msg-upgrade">-</span>
	</div>
</body>
</html>
