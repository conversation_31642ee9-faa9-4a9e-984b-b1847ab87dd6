﻿/* for NAURA_N8 */
/*<!--.css 以下是有效的 CSS 代码，切勿移除 **************************
.god, .god-a-t, .god-a-vm, .god-a-vl, .god-a-vh, .god-a-s, .god-v-e, .god-v-ra, .god-v-rw, .god-v-rs, .god-v-a, .god-a-m, .god-e-mi, .naura_n8 { display: none !important; }
/*--><!--.js 以下是 JS 代码 ***************************************** */
if (!!window.godCSS)
	window.CS.css = window.godCSS('.god, .god-a-t, .god-a-vm, .god-a-vl, .god-a-vh, .god-a-s, .god-v-e, .god-v-ra, .god-v-rw, .god-v-rs, .god-v-a, .god-a-m, .god-e-mi, .naura_n8');
window.CS.menus =
	[
		{
			'key': 'ToolManagement', 'text': '机台管理', 'child':
				[
					{ 'key': 'AreaSetup', 'text': '区域设置', 'child': [{ 'key': 'AreaOptionSet', 'text': '增删改' }] },
					{ 'key': 'ToolGroupSetup', 'text': '机台组设置', 'child': [{ 'key': 'ToolGroupOptionSet', 'text': '增删改' }] },
					{ 'key': 'ToolSetup', 'text': '机台设置', 'child': [{ 'key': 'ToolOptionSet', 'text': '增删改' }] }
				]
		},
		{
			'key': 'LogManagement', 'text': '日志管理', 'child':
				[
					{ 'key': 'ToolLog', 'text': '机台日志', 'child': [] },
					{ 'key': 'SystemLog', 'text': '系统日志', 'child': [] }
				]
		},
		{
			'key': 'SystemSetup', 'text': '系统管理', 'child':
				[
					{ 'key': 'UserSetup', 'text': '用户管理', 'child': [{ 'key': 'UserOptionSet', 'text': '增删改' }] },
					{ 'key': 'RoleSetup', 'text': '角色管理', 'child': [{ 'key': 'RoleOptionSetup', 'text': '增删改' }] },
					{ 'key': 'AuthSetup', 'text': '机台权限', 'child': [{ 'key': 'AuthOptionSet', 'text': '增删改' }] },
					{ 'key': 'NoticeSetup', 'text': '公告设置', 'child': [] }
				]
		},
	];
window.CS.title = 'N8远程控制管理系统(RCM)';
window.CS.company = '北方华创(NAURA)';
window.CS.exe = 'http://192.168.103.54:65535/Temp/RCM-Client(N8).exe';
window.CS.localRecording = function (action) { return !action ? ':Stop' : '%MyVideos%\\' + window.video.Name + '-' + window.login.Name + '-' + new Date().toString('yyyymmddHHnnss') + '.mpeg'; };//!action?'2':'1'//value%2==0
window.CS.noUserTitle = true;
window.CS.onready = function ()
{
	if (window.webName == 'index')
	{
		$('#LayoutSetup').remove();
		$('#AlarmSetup').remove();
		//$('#SummarySearch').remove();
		if (window.login.AuthenticationType == 'LDAP')
		{
			$('#ParamSetup').remove();
		}
	}
	else if (window.webName == 'toolbar')
	{
		window.CS.sideUrl = 'side.naura.html';
		window.CS.noSideMini = 1;
	}
};
//window.getVideoHub = function ()
//{
//	return '**************';
//};
//window.getVideoPort = function (ip)
//{
//	return 0;
//};
//window.getCtrlPort = function (ip)
//{
//	return 0;
//};
/*-->*/
