﻿/* for NAURA */
/*<!--.css 以下是有效的 CSS 代码，切勿移除 **************************
.god, .god-a-t, .god-a-vm, .god-a-vl, .god-a-vh, .god-a-s, .god-v-e, .god-v-ra, .god-v-rw, .god-v-rs, .god-v-a, .god-a-m, .god-e-mi, .naura { display: none !important; }
/*--><!--.js 以下是 JS 代码 ***************************************** */
if (!!window.godCSS)
	window.CS.css = window.godCSS('.god, .god-a-t, .god-a-vm, .god-a-vl, .god-a-vh, .god-a-s, .god-v-e, .god-v-ra, .god-v-rw, .god-v-rs, .god-v-a, .god-a-m, .god-e-mi, .naura');
window.CS.menus =
	[
		{
			'key': 'ToolManagement', 'text': '机台管理', 'child':
				[
					{ 'key': 'AreaSetup', 'text': '区域设置', 'child': [{ 'key': 'AreaOptionSet', 'text': '增删改' }] },
					{ 'key': 'ToolGroupSetup', 'text': '机台组设置', 'child': [{ 'key': 'ToolGroupOptionSet', 'text': '增删改' }] },
					{ 'key': 'ToolSetup', 'text': '机台设置', 'child': [{ 'key': 'ToolOptionSet', 'text': '增删改' }] }
				]
		},
		{
			'key': 'LogManagement', 'text': '日志管理', 'child':
				[
					{ 'key': 'ToolLog', 'text': '机台日志', 'child': [] },
					{ 'key': 'SystemLog', 'text': '系统日志', 'child': [] }
				]
		},
		{
			'key': 'SystemSetup', 'text': '系统管理', 'child':
				[
					{ 'key': 'UserSetup', 'text': '用户管理', 'child': [{ 'key': 'UserOptionSet', 'text': '增删改' }] },
					{ 'key': 'RoleSetup', 'text': '角色管理', 'child': [{ 'key': 'RoleOptionSetup', 'text': '增删改' }] },
					{ 'key': 'AuthSetup', 'text': '机台权限', 'child': [{ 'key': 'AuthOptionSet', 'text': '增删改' }] },
					{ 'key': 'NoticeSetup', 'text': '公告设置', 'child': [] }
				]
		},
	];
window.CS.title = 'N1远程控制管理系统(RCM)';
window.CS.company = '北方华创(NAURA)';
window.CS.exe = 'http://192.168.103.54:65535/Temp/RCM-Client(N1).exe';
window.CS.localRecording = function (action) { return !action ? ':Stop' : '%MyVideos%\\' + window.video.Name + '-' + window.login.Name + '-' + new Date().toString('yyyymmddHHnnss') + '.mpeg'; };//!action?'2':'1'//value%2==0
window.CS.noUserTitle = true;
window.CS.onready = function ()
{
	if (window.webName == 'index')
	{
		$('#LayoutSetup').remove();
		$('#AlarmSetup').remove();
		$('#SummarySearch').remove();
		if (window.login.AuthenticationType == 'LDAP')
		{
			$('#ParamSetup').remove();
		}
	}
	else if (window.webName == 'toolbar')
	{
		window.CS.sideUrl = 1;//
		window.CS.noSideMini = 1;
	}
};
//window.getVideoHub = function ()
//{
//	return '**************';
//};
//window.getVideoPort = function (ip)
//{
//	return 0;
//};
//window.getCtrlPort = function (ip)
//{
//	return 0;
//};
/*-->*/
/*
systemctl enable firewalld
sudo firewall-cmd --list-all

sudo firewall-cmd --permanent --add-masquerade
sudo firewall-cmd --reload

sudo firewall-cmd --permanent --add-port=1-65535/tcp
sudo firewall-cmd --permanent --add-port=1-65535/udp
sudo firewall-cmd --reload

echo net.ipv4.ip_forward = 1 >> /etc/sysctl.conf
sudo sysctl -p

sudo firewall-cmd --permanent --add-rich-rule="rule family=ipv4 forward-port port=80 protocol=tcp to-port=80 to-addr=************"
sudo firewall-cmd --permanent --add-rich-rule="rule family=ipv4 forward-port port=6000 protocol=tcp to-port=6000 to-addr=************"
sudo firewall-cmd --permanent --add-rich-rule="rule family=ipv4 forward-port port=8080 protocol=tcp to-port=8080 to-addr=************"
sudo firewall-cmd --permanent --add-rich-rule="rule family=ipv4 forward-port port=8001 protocol=udp to-port=8001 to-addr=************"
sudo firewall-cmd --permanent --add-rich-rule="rule family=ipv4 forward-port port=50070 protocol=udp to-port=50070 to-addr=************"
sudo firewall-cmd --reload

firewall-cmd --permanent --add-forward-port=port=21248:proto=tcp:toport=6000:toaddr=**************
firewall-cmd --permanent --add-forward-port=port=21248:proto=udp:toport=8001:toaddr=**************
firewall-cmd --permanent --add-forward-port=port=37010:proto=tcp:toport=6000:toaddr=************
firewall-cmd --permanent --add-forward-port=port=37010:proto=udp:toport=8001:toaddr=************
firewall-cmd --permanent --add-forward-port=port=37011:proto=tcp:toport=6000:toaddr=************
firewall-cmd --permanent --add-forward-port=port=37011:proto=udp:toport=8001:toaddr=************
sudo firewall-cmd --reload
*/
