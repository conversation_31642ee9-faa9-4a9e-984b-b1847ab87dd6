﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>区域页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<link rel="stylesheet" href="lib/layui/treetable/treetable.css" />
	<style>
		#addArea .layui-inline {
			margin-right: 0px;
		}

		#addArea .edit {
			width: 200px;
		}

		.borderStyle {
			border: 1px solid red;
		}

        .ew-tree-table .ew-tree-table-tool {
			height: 50px;
		}

		.ew-tree-table-cell-content:hover {
			color: #666 !important;
		}

        @media screen and (max-width: 450px) {
            .layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/index.js?v=1"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/excel.js"></script>
	<script src="js/request.js"></script>
	<script type="text/javascript">
        var areaTreeTable;
        var col = null;
        layui.config({ base: 'lib/layui/' });
        layui.extend({ treeTable: './treeTable-lay' });
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel', 'upload', 'dropdown', 'treeTable'], function () {
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, upload = layui.upload
				, excel = layui.excel
				, dropdown = layui.dropdown;
			lang.call(this, $);
			configData = null;
			limitPageCount = 20;
			//创建Table的列名
			treetable = layui.treeTable;
			RenderTreeTable([]);

			pageCount = 1;
			FillDataTable();

			//头工具栏事件
			treetable.on('toolbar(arealist)', function (obj) {
				switch (obj.event) {
					case 'isAdd':
						index = layer.open({
							title: lang('增加区域')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addArea')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {
								$($('p')[0]).hide();
							}
							, btn1: function (index) {
								var data = form.val("form-addArea");
								if (data.areaname.trim().length == 0) {
									layer.alert(lang('区域名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								AjaxApi.request({
									url: "/Area/AddArea.do",
									data: data,
									dataType: 'text',
									success: function (result) {
										var res = $.eval(result);
										if (res.code == "Success") {
											setTimeout(FillDataTable, 300);
											layer.alert(lang('保存成功,是否继续?'), {
												title: lang('增加区域')
												, btn: [lang('继续'), lang('取消')]
												, btnAlign: 'c'
												, btn1: function (subIndex) {
													$('#addArea')[0].reset();
													layer.close(subIndex);
												}
												, btn2: function (subIndex) {
													$('#addArea')[0].reset();
													layer.close(subIndex);
													layer.close(index);
													// FillDataTable();
												}
											});
										}
										else if (res.code == "Exist") {
											layer.alert(lang('区域已存在！'), { btn: [lang('确定')], title: lang("增加区域"), });
										}
										else {
											layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加区域"), });
										}
									},
									error: function (r) {
										layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加区域"), });
									}
								})
							}
							, end: function () {
								$('#addArea')[0].reset();
								$('#addArea').css('display', 'none');
							}
						});
						break;
					case 'BatchDelete':
						{
							BatchDelete();
							break;
						}
					case 'Unfold':
						{
							$('#FoldBtn').css('display', 'block');
							$('#UnfoldBtn').css('display', 'none');
							areaTreeTable.expandAll();
							break;
						}
					case 'Fold':
						{
							$('#FoldBtn').css('display', 'none');
							$('#UnfoldBtn').css('display', 'block');
							areaTreeTable.foldAll();
							break;
						}
				};
			});

			//监听行工具事件
			treetable.on('tool(arealist)', function (obj) {
				var data = obj.data;
				if (obj.event === 'del') {
					Delete(data);
				}
				else if (obj.event == 'add') {
					AddSubArea(data);
				}
				else if (obj.event === 'edit') {
					if (data.Role == '') {
						layer.alert(lang('此用户禁止修改！'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}

					$($('p')[0]).show();
					var oldrole = data.Role;
					var des = data.Description;
					var index1 = layer.open({
						title: lang('修改区域')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#addArea')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function () {
							form.val('form-addArea', {
								"areaname": data.Name
								, "description": data.Note
							});

							$('#addArea input[name=areaname]').attr('disabled', true);
							$('#addArea input[name=areaname]').addClass('layui-disabled');
						}
						, btn1: function (value, index) {
							var data1 = form.val("form-addArea");
							if (data1.areaname.length == 0) {
								layer.alert(lang('区域不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}

							data1["ID"] = data.ID;
							AjaxApi.request({
								url: "/Area/EditArea.do",
								data: data1,
								dataType: 'text',
								success: function (result) {
									var res = $.eval(result);
									if (res.code == "Success") {
										layer.msg(lang('修改成功'), { icon: 1 });
										layer.close(index1);
										FillDataTable();
									}
									else {
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
							})
						}
						, end: function () {
							$('#addArea')[0].reset();
							$('#addArea').css('display', 'none');
							$('#addArea input[name=areaname]').attr('disabled', false);
							$('#addArea input[name=areaname]').removeClass('layui-disabled');
						}
					});
				}
				else if (obj.event == 'singleclick') {
					var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
					if (checkCell.length > 0) {
						checkCell.click();
					}
				}
			});

			form.on('submit(areaSearch)', function (data) {
				var data = form.val('form-select');
				areaTreeTable.filterData(data.areaname);
				return false;
			});
			$('#resetBtn').on("click", function () {
				document.getElementById('form-select').reset();
				FillDataTable();
			})
		})

		function Delete(data) {
			var ids = ',';
			var names = ',';
			ids += data.ID;
			names += data.Name;
			
			if (data.children && data.children.length !== 0) {
				for (var i = 0; i < data.children.length; i++) {
					if (i == data.children.length - 1) {
                        ids += "," + data.children[i].ID;
                        names += "," + data.children[i].Name;
					} else {
                        ids += "," + data.children[i].ID + ",";
                        names += "," + data.children[i].Name + ",";
					}
                }
			}
			layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
				AjaxApi.request({
					url: "/Area/DeleteArea.do",
					data: { ID: ids, names: names },
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);
						if (res.code == "Success") {
							layer.msg(lang('删除成功'), { icon: 1 });
							FillDataTable();
						}
						else {
							layer.msg(lang('删除失败'), { icon: 2 });
						}
					},
					error: function (r) {
						layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
					}
				})
				layer.close(index);
			});
		}

        function BatchDelete() {
			var checkedArr = areaTreeTable.checkStatus();
			checkedItems = checkedArr.filter(function (item) { return item.isIndeterminate == false });

            if (checkedItems.length == 0) {
                layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
                return;
            }
            layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
                AjaxApi.request({
                    type: "post",
                    url: "/Area/BatchDelete.do",
                    data: JSON.stringify(checkedItems),
                    dataType: 'text',
                    success: function (result) {
                        var res = $.eval(result);
                        if (res.code == "Success") {
                            checkedItems = [];
                            table.reload('arealist', {
                                cols: [col],
                            })

                            $('#normaldiv').show();
                            $('#batchdiv').hide();

                            layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
                        }
                        else {
                            layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除区域"), });
                        }
                        checkedItems = [];
                        FillDataTable();
                    }
                })
                layer.close(index);
            });
        }

		function AddSubArea(data) {
			var index1 = layer.open({
				title: lang('增加子区域')
				, type: 1
				, resize: false
				, id: 'edit'
				, content: $('#addArea')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
				, success: function () {
					form.val('form-addArea', {
						"mainareaname": data.Name
					});

					$('#RootDiv').show();
				}
				, btn1: function (value, index) {
					var data1 = form.val("form-addArea");
					if (data1.areaname.length == 0) {
						layer.alert(lang('区域不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
						return false;
					}

					data1['parent'] = data.ID;
					if (!data.Path)
						data1['root'] = data.ID;
					else
						data1['root'] = data.Path;

					AjaxApi.request({
						url: "/Area/AddSubArea.do",
						data: data1,
						dataType: 'text',
						success: function (result) {
							var res = $.eval(result);
							if (res.code == "Success") {
								layer.msg(lang('添加成功'), { icon: 1 });
								layer.close(index1);
                                setTimeout(FillDataTable, 300);
							}
							else if (res.code == 'Exist') {
								layer.alert(lang('已存在') + data.Name, { btn: [lang('确定')], title: lang('提示') });
								$('#addArea')[0].reset();
								$('#addArea').css('display', 'none');
								$('#RootDiv').hide();
							}
							else {
								layer.alert(lang('添加失败！'), { btn: [lang('确定')], title: lang('提示') });
								$('#addArea')[0].reset();
								$('#addArea').css('display', 'none');
								$('#RootDiv').hide();
							}
						}
					})
				}
				, end: function () {
					$('#addArea')[0].reset();
					$('#addArea').css('display', 'none');
					$('#RootDiv').hide();
				}
			});
		}

		//选择导入文件
		function initupload() {
			upload.render({
				elem: '#LAY-excel-upload' //绑定元素
				, auto: false //选择文件后不自动上传
				, accept: 'file'
				, choose: function (obj) {// 选择文件回调
					var files = obj.pushFile()
					// var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
					var fileArr = Object.keys(files).map(function (key) {
						return files[key];
					});
					// 用完就清理掉，避免多次选中相同文件时出现问题
					for (var index in files) {
						if (files.hasOwnProperty(index)) {
							delete files[index]
						}
					}
					$('#LAY-excel-upload').next().val('');
					init(fileArr);
				}
			});
		}
		//导入数据
		function init(files) {
			excel.importExcel(files, {}, function (data) {
				data = excel.filterImportData(data, {
					'Name': 'A'
					, 'Data2': 'B'
				});

				if (data[0].Sheet1.length > 0) {
					data[0].Sheet1.splice(0, 1);
					if (data[0].Sheet1.length > 200) {
						layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					for (var i = 0; i < data[0].Sheet1.length; i++) {
						if ($.trim(data[0].Sheet1[i].Name + '') == '') {
							layer.alert(lang('区域名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}

						if (data[0].Sheet1[i].Data2.length > 200) {
							data[0].Sheet1[i].Data2 = data[0].Sheet1[i].Data2.substring(0, 200);
						}
					}
				}
				AjaxApi.request({
					type: "post",
					url: "../Area/ImportData.do",
					data: JSON.stringify(data[0].Sheet1),
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);
						var names = '';
						if (!!res.msg) {
							var msgArr = res.msg.split('|');
							if (msgArr.length > 1) {
								res.msg = msgArr[0];
								names = msgArr[1];
							}
						}
						if (res.code == "Success") {
							layer.alert(lang('导入成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else if (res.msg == "Exist") {
							layer.alert(names + lang(' 区域已存在！'), { btn: [lang('确定')], title: lang("增加区域"), });
						}
						else {
							layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加区域"), });
						}
						FillDataTable();
					}
				})
			});
		}
		var username = '';
		var gData = '';
		function FillDataTable() {
			if (window.top) {
				window.top.FillFavTreeTable();
			}

            var data = form.val('form-select');
            //gData = { areaNameLike: data.areaname };
            AjaxApi.request({
				url: '../ListConfig.do?Type=' + window.ConfigData.TYPE_AREA + '&Flags=0',
                // data: gData,
                dataType: 'text',
                success: function (result) {
                    var res = $.eval(result);
					username = top.login.Name;
                    for (var i = 0; i < res.data.length; i++) {
                        $.parseUrl(res.data[i].Data, res.data[i]);
                        $.parseUrl(res.data[i].Data9, res.data[i]);

                        if (!!res.data[i].Parent && !!res.data[i].Parent.length)
                            res.data[i].Parent = res.data[i].Parent.substring(0, 32);

                        res.data[i].xuhao = i + 1;
                    }

					areaTreeTable.refresh(res.data);
					areaTreeTable.expandAll('#arealist');
                }
            })
			return

			AjaxApi.request({
				url: "/Json/area.json",
				data: {},
				dateType: 'text',
				success: function (result) {
					if (result == '') {
                        //delete contain third
                        AjaxApi.request({
                            url: "/Area/Restore.do",
							dateType: 'text',
							success: function () {
                                var data = form.val('form-select');
                                gData = { areaNameLike: data.areaname, pageCount: 0, limitCount: limitPageCount };
                                AjaxApi.request({
                                    url: "/Area/GetAreaListAll.do",
                                    data: gData,
                                    dataType: 'text',
                                    success: function (result) {
                                        var res = $.eval(result);
                                        username = top.login.Name;
                                        laypage.render({
                                            elem: 'footer'
                                            , count: res.count
                                            , theme: '#FF5722'
                                            , limit: limitPageCount
                                            , prev: lang('上一页')
                                            , next: lang('下一页')
                                            , layout: ['count', 'prev', 'page', 'next', 'skip']
                                            , jump: function (obj, first) {
                                                pageCount = obj.curr;
                                                if (first) {
                                                    for (var i = 0; i < res.data.length; i++) {
                                                        $.parseUrl(res.data[i].Data, res.data[i]);
                                                        $.parseUrl(res.data[i].Data9, res.data[i]);

                                                        if (!!res.data[i].Parent && !!res.data[i].Parent.length)
                                                            res.data[i].Parent = res.data[i].Parent.substring(0, 32);

                                                        res.data[i].xuhao = i + 1;
                                                    }

                                                    //col[col.length - 1].hide = true;
                                                    //加载treetable数据
                                                    RenderTreeTable(res.data);
                                                }
                                                else {
                                                    setTable({ deptLike: data.Deptment, nameLike: data.username, pageCount: pageCount - 1, limitCount: limitPageCount });
                                                }
                                                laypageEN();//翻页翻译成英文方法
                                            }
                                        });
                                    }
                                })
							},
                            error: function () { }
                        });


					}
					else {
						var res = $.eval(result);
						if (!!res && !!res.length) {
							if (res.length == 0) {
								res = [{ "area": "ThirdArea1", "subarea": ["ThirdArea1-1", "ThirdArea1-2"] }, { "area": "ThirdArea2", "subarea": ["ThirdArea2-1", "ThirdArea2-2"] }, { "area": "ThirdArea3", "subarea": ["ThirdArea3-1", "ThirdArea3-2"] }];
							}

                            AjaxApi.request({
                                url: "/Area/ToThird.do",
                                dateType: 'text',
								error: function () { },
								success: function () {
                                    //add area and sub-area
                                    res.forEach(value => {
                                        AjaxApi.request({
                                            url: "/Area/AddArea.do",
                                            data: { 'areaname': value.area },
                                            dateType: 'text',
                                            error: function () { }
                                        });
                                    })

                                    //show
                                    var data = form.val('form-select');
                                    gData = { areaNameLike: data.areaname, pageCount: 0, limitCount: limitPageCount };
                                    AjaxApi.request({
                                        url: "/Area/GetAreaListAll.do",
                                        data: gData,
                                        dataType: 'text',
                                        success: function (result) {
                                            var res = $.eval(result);
                                            username = top.login.Name;
                                            laypage.render({
                                                elem: 'footer'
                                                , count: res.count
                                                , theme: '#FF5722'
                                                , limit: limitPageCount
                                                , prev: lang('上一页')
                                                , next: lang('下一页')
                                                , layout: ['count', 'prev', 'page', 'next', 'skip']
                                                , jump: function (obj, first) {
                                                    pageCount = obj.curr;
                                                    if (first) {
                                                        for (var i = 0; i < res.data.length; i++) {
                                                            $.parseUrl(res.data[i].Data, res.data[i]);
                                                            $.parseUrl(res.data[i].Data9, res.data[i]);

                                                            if (!!res.data[i].Parent && !!res.data[i].Parent.length)
                                                                res.data[i].Parent = res.data[i].Parent.substring(0, 32);

                                                            res.data[i].xuhao = i + 1;
                                                        }

                                                        //col[col.length - 1].hide = true;
                                                        //加载treetable数据
                                                        RenderTreeTable(res.data);
                                                    }
                                                    else {
                                                        setTable({ deptLike: data.Deptment, nameLike: data.username, pageCount: pageCount - 1, limitCount: limitPageCount });
                                                    }
                                                    laypageEN();//翻页翻译成英文方法
                                                }
                                            });
                                        }
                                    })
								}
                            });
						}
					}
				},
				error: function () {
					//delete contain third
                    AjaxApi.request({
                        url: "/Area/Restore.do",
						dateType: 'text',
						success: function () {
                            var data = form.val('form-select');
                            gData = { areaNameLike: data.areaname, pageCount: 0, limitCount: limitPageCount };
                            AjaxApi.request({
                                url: "/Area/GetAreaListAll.do",
                                data: gData,
                                dataType: 'text',
                                success: function (result) {
                                    var res = $.eval(result);
                                    username = top.login.Name;
                                    laypage.render({
                                        elem: 'footer'
                                        , count: res.count
                                        , theme: '#FF5722'
                                        , limit: limitPageCount
                                        , prev: lang('上一页')
                                        , next: lang('下一页')
                                        , layout: ['count', 'prev', 'page', 'next', 'skip']
                                        , jump: function (obj, first) {
                                            pageCount = obj.curr;
                                            if (first) {
                                                for (var i = 0; i < res.data.length; i++) {
                                                    $.parseUrl(res.data[i].Data, res.data[i]);
                                                    $.parseUrl(res.data[i].Data9, res.data[i]);

                                                    if (!!res.data[i].Parent && !!res.data[i].Parent.length)
                                                        res.data[i].Parent = res.data[i].Parent.substring(0, 32);

                                                    res.data[i].xuhao = i + 1;
                                                }

                                                //col[col.length - 1].hide = true;
                                                //加载treetable数据
                                                RenderTreeTable(res.data);
                                            }
                                            else {
                                                setTable({ deptLike: data.Deptment, nameLike: data.username, pageCount: pageCount - 1, limitCount: limitPageCount });
                                            }
                                            laypageEN();//翻页翻译成英文方法
                                        }
                                    });
                                }
                            })
						},
                        error: function () { }
                    });
				}
			});
		}

        var treeNodeClose = false;

        function foldBtnStatus() {
            if (treeNodeClose) {
                $('#FoldBtn').css('display', 'none');
                $('#UnfoldBtn').css('display', 'block');
            } else {
                $('#FoldBtn').css('display', 'block');
                $('#UnfoldBtn').css('display', 'none');
            }
        }
		function RenderTreeTable(data) {
			if (!!treetable) {
				areaTreeTable = treetable.render(
                    {
                        elem: '#arealist',
                        height: 'full-160',
                        toolbar: '#areatoolbar',
                        defaultToolbar: [],
                        data: data,
                        sort: true,
                        tree: {
                            iconIndex: 2,
                            idName: 'ID',
                            pidName: 'Parent',
							isPidData: true,
                        },
						cols: [
                            { type: "checkbox"}
                            , { title: lang("序号"), type: "numbers", width: 80 }
                            , { title: lang("区域名称"), field: "Name", align: "center" }
                            , { title: lang('更新者'), field: 'EditUser', align: "center" }
                            , { title: lang("更新时间"), field: "Edit", templet: templet, align: "center" }
                            , { title: lang("描述"), field: "Note", align: "center" }
							, { fixed: 'right', width: 270, title: lang("操作"), align: "center", toolbar: "#operation", hide: !hasButtonPermission('AreaOptionSet') }
						],
						done: function (res, curr, count) {
                            initupload();
							foldBtnStatus();
                        }
                    }
				);

				if (!hasButtonPermission('AreaOptionSet'))
					$('#divBC').hide();
			}
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px;padding: 5px;">
		<fieldset class="layui-elem-field">
			<legend><span>区域设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<div class="layui-inline">
							<lable class="layui-form-label" style="width:85px;">区域名称：</lable>
						</div>
						<div class="layui-inline">
							<input type="text" class="layui-input" name="areaname" autocomplete="off" placeholder="请输入区域名称"
								   oninput="cleanSpelChar(this)">
						</div>
						<div class="layui-inline">
							<button type="submit" class="layui-btn" lay-filter="areaSearch" lay-submit=""><span>查询</span></button>
						</div>
						<div class="layui-inline">
							<button id="resetBtn" type="button" class="layui-btn"><span>重置</span></button>
						</div>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-table" id="arealist" lay-filter="arealist"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="areaListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="areatoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="Unfold" id="UnfoldBtn">{{lang('全部展开')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="Fold" id="FoldBtn" style="display: none;">{{lang('全部折叠')}}</button>
					</div>
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm m_UserBC" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
					</div>
				</div>
				<!--<div class="layui-inline" id="batchdiv" style="display:none" name="batchdiv">
					<div class="layui-inline">
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm m_UserBC" lay-event="ConfirmBatchDelete" id="batchdeleteConfirmBtn">{{lang('批量删除确定')}}</button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="CancelBatchDelete" id="batchdeleteCancelBtn">{{lang('取消')}}</button>
					</div>
				</div>-->
			</div>
		</script>
		<script type="text/html" id="operation">
			{{#  if(!d.Parent){ }}
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventAdd" lay-event="add">{{lang('增加子区域')}}</a>
			{{# } }}
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改区域')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
		</script>
		<script type="text/html" id="checkedOne">
			<input type="checkbox" name="like1[write]" lay-skin="primary">
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="addArea" class="layui-form" lay-filter="form-addArea" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item" id="RootDiv" style="display:none;">
			<div class="layui-inline">
				<label class="layui-form-label">根区域名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input layui-disabled" name="mainareaname" autocomplete="off" placeholder="请输入区域名称"
					   oninput="cleanSpelChar(this)" disabled="disabled">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">区域名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="areaname" autocomplete="off" placeholder="请输入区域名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">描述</label>
			</div>
			<div class="layui-inline edit">
				<textarea type="text" class="layui-textarea" style="height: 200px; width:200px; white-space: pre-wrap;overflow:auto;" name="description" autocomplete="off" placeholder="请输入少于200字的描述"
						  oninput="limitTextAreaCount(this)"></textarea>
			</div>
		</div>
	</form>
</body>
</html>