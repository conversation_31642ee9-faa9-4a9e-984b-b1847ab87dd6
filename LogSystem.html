﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>AlarmList</title>
    <link href="lib/layui/css/layui.css" rel="stylesheet" />
    <link href="css/common.css" rel="stylesheet" />
    <link rel="stylesheet" href="/user/loginMenu.docss" />
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script type="text/javascript" src="lib/layui/layui.js"></script>
    <script type="text/javascript" src="lib/pickgold.js"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="/Config/List.do?Type=STOREHOUSE&js=storehouse"></script>
    <script src="lib/layui/excel.js"></script>
    <script src="js/xm-select.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
    <script src="js/request.js"></script>
    <style>
        @media screen and (max-width: 450px) {
            .layui-form-item .layui-inline {
                display: inline-block;
                margin-bottom: 0px !important;
            }
        }
    </style>
</head>
<body style="overflow-x: auto;">
    <form class="layui-form" lay-filter="logform" id="logform" style="margin: 10px;">
        <fieldset class="layui-elem-field">
            <legend><span>日志查询</span></legend>
            <div class="layui-field-box" style="text-align:center;">
                <div class="layui-form-item">
                    <div class="layui-inline" id="systemlbl" style="margin-right: 5px;">
                        <lable class="layui-form-label" style="padding: 0px; width: 85px; ">系统日志：</lable>
                    </div>
                    <div class="layui-inline" id="systemselect" style="width:120px;">
                        <select name="systemtype" id="systemtype" lay-search="">
                            <!--<option value="u">请选择</option>-->
                            <option value="TYPE_USER">用户日志</option>
                            <option value="TYPE_ROLE">角色日志</option>
                            <option value="TYPE_AUTH">权限日志</option>
                            <option value="TYPE_AREA">区域日志</option>
                            <option value="TYPE_TOOLGROUP">机台组日志</option>
                            <option value="Log">登录日志</option>
                        </select>
                    </div>
                    <div id="userlbl" class="layui-inline" style="margin-right: 5px;">
                        <lable class="layui-form-label" style="padding: 0px; width: 85px;">用户名称：</lable>
                    </div>
                    <div id="userinput" class="layui-inline" style="width: 150px;">
                        <input type="text" class="layui-input" name="username" autocomplete="off" placeholder="请输入用户名称">
                    </div>
                    <div id="timelbl" class="layui-inline" style="margin-right: 5px;">
                        <lable class="layui-form-label" style="padding: 0px; width: 85px;">日期范围：</lable>
                    </div>
                    <div id="timeinput" class="layui-inline" style="width: 300px;">
                        <input type="text" class="layui-input" id="datetime" name="datetime" autocomplete="off" placeholder="请选择">
                    </div>
                    <div class="layui-inline" style="margin-right: 5px;">
                        <button type="submit" class="layui-btn" lay-filter="logSearch" lay-submit=""><span>查询</span></button>
                    </div>
                    <div class="layui-inline" style="margin-right: 5px;">
                        <button type="button" class="layui-btn"><span>重置</span></button>
                    </div>
                </div>
            </div>
        </fieldset>
        <table class="layui-table" id="loglist" lay-filter="loglist"></table>
        <div id="footer" style="text-align: center; margin-top: 10px;"></div>
    </form>
</body>
</html>

<script type="text/javascript">
    var limitpagecount = 20, currentPage = 1;

    layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate', 'excel', 'upload'], function () {
        $ = layui.$
            , laydate = layui.laydate
            , form = layui.form
            , table = layui.table
            , layer = layui.layer
            , element = layui.element
            , excel = layui.excel
            , laypage = layui.laypage
            , upload = layui.upload;

        var endDate = new Date();
        var startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        function formatDate(date) {
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var day = date.getDate().toString().padStart(2, '0');
            return year + '-' + month + '-' + day;
        }

        var defaultDateRange = formatDate(startDate) + ' 00:00:00' + ' 至 ' + formatDate(endDate) + ' 23:59:59';

        laydate.render({
            elem: '#datetime',
            type: 'datetime',
            range: "至",
            max: '{:date("YY-mm-dd 23:59:59")}',
            value: defaultDateRange,
            done: function (value, date, endDate) {
                // $('#datetime').data('date', { startdate: date, enddate: endDate });
            }
        });

        form.val('logform', {
            "tooltype": "VIDEO_RECORD",
            "datetime": defaultDateRange
        });

        table.on('tool(loglist)', function (obj) {
            var sweep = obj.data;
            if (obj.event === 'view') {
                if (!!sweep.record) {
                    var src = '/file/' + sweep.record.Data1.replace('C:\\ProgramData\\rcs\\', '').replace('\\', '/');
                    var index1 = layer.open({
                        title: sweep.Video + ' 机台录屏回放'
                        , type: 1
                        , resize: false
                        , area: ['70%', '70%']
                        , content: '<video src="' + src + '" style="height: 100%; width: 100%; background-color: #393D49;" controls="" autoplay=""></video>'
                        , shadeClose: true
                        , success: function () {
                        }
                        , end: function () {
                            layer.close(index1);
                        }
                    });
                }
            }
        });

        $('button[type=button]').on('click', function () {
            document.getElementById('logform').reset();
            form.val('logform', {
				"tooltype": "VIDEO_RECORD",
				"datetime": defaultDateRange
			});
            FillTableData();
        });

        form.on('submit(logSearch)', function (data) {
            FillTableData();
            return false;
        });
        form.val('logform', {
            "systemtype": "TYPE_USER",
        });
        FillTableData();
        function RenderTable(data) {
            var columnsArray = [
				{
					title: lang("序号"), field: "xuhao", width: 80, templet: function (e)
					{
						return limitpagecount * (currentPage) + e.LAY_INDEX
					}, align: 'center', event: 'singleclick',
				},
				{ title: lang("用户"), field: "Name", width: 120, align: 'center', event: 'singleclick', },
				{ title: lang("操作记录"), field: "Type", width: 150, align: 'center', event: 'singleclick', },
				{ title: lang("详细内容"), field: "Data", align: 'center', event: 'singleclick', },
				{ title: lang("编辑时间"), field: "StartTime", width: 200, align: 'center', event: 'singleclick', },
            ];

            table.render({
                elem: '#loglist'
                , even: true
                , sort: true
                , loading: false
                , id: "loglist"
                , limit: limitpagecount
                , cols: [columnsArray]
                , data: data
                , height: 'full-200'
            });
        }

        var dataRecord = {
            "A": '始终录屏', "N": '不录屏', "W": '观看时录屏', "C": '操作时录屏',
        };
        var dataRole = {
            "USER": '用户', "ROLE": '角色', "AREA": '区域', "TOOLGROUP": '机台组', "AUTH": '权限', "VIDEO": '机台'
        }
        var dataOp = {
            "ADD": '添加', "EDIT": '编辑', "DELETE": '删除', "BATCHDELETE": '批量删除', "ADDSUB": '添加',
            "REGISTERTOOL": '注册', "UNREGISTERTOOL": '注销',
        };

        function FillTableData() {
            var data = form.val('logform');
            data.pageCount = 0;
            data.limitCount = limitpagecount;

            data.logtype = "S";
            data.tooltype = "";

            if ($('#systemselect').css('display') != 'none') {
                data.logtype = data.systemtype;
            }

            var date = data.datetime;
            var start_date = "";
            var end_date = "";
            if (!date == false) {
                var times = date.split("至")
                start_date = new Date(times[0]).getTime();
                end_date = new Date(times[1]).getTime();
            }

            data.start_date = start_date;
            data.end_date = end_date;

            AjaxApi.request({
                url: "/Log/GetAllLog.do",
                data: data,
                dataType: 'text',
                success: function (result) {
                    var res = $.eval(result);
                    ////加载table数据
                    for (var i = 0; i < res.data.length; i++) {
                        $.parseUrl(res.data[i].Data, res.data[i]);

                        var date = new Date(parseInt(res.data[i].Time));
                        res.data[i].StartTime = date.getFullYear() + "/" +
                            parseInt(date.getMonth() + 1) + "/" +
                            date.getDate() + " ";
                        res.data[i].StartTime +=
                            date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                        res.data[i].StartTime += ':';
                        res.data[i].StartTime +=
                            date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                        res.data[i].StartTime += ':';
                        res.data[i].StartTime +=
                            date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

                        date = new Date(parseInt(res.data[i].Edit));
                        res.data[i].EndTime = date.getFullYear() + "/" +
                            parseInt(date.getMonth() + 1) + "/" +
                            date.getDate() + " ";
                        res.data[i].EndTime +=
                            date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                        res.data[i].EndTime += ':';
                        res.data[i].EndTime +=
                            date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                        res.data[i].EndTime += ':';
                        res.data[i].EndTime +=
                            date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();

                        if (!!res.record)
                            res.data.record = res.record;

                        //if (!!res.data[i].Recording) {
                            res.data[i].Recording = dataRecord[res.data[i].Recording];
                            //if (res.data[i].Type == 'VIDEO_SWEEPER') {
                            //    res.data[i].Name = res.data[i].Data.split('：')[1];
                            //}

                            var type = res.data[i].Type.split('_');
                            if (type.length == 3) {
                                for (var key in dataRole) {
                                    if (type[1].indexOf(key) >= 0) {
                                        res.data[i].Type = dataRole[key]
                                    }
                                }
                                for (var key in dataOp) {
                                    if (type[2].indexOf(key) >= 0) {
                                        res.data[i].Type += dataOp[key]
                                    }
                                }
                            }
                        //}
                    }
                    laypage.render({
                        elem: 'footer'
                        , count: res.count
                        , theme: '#FF5722'
                        , limit: limitpagecount
                        , prev: lang('上一页')
                        , next: lang('下一页')
                        , layout: ['count', 'prev', 'page', 'next', 'skip']
                        , jump: function (obj, first) {
                            currentPage = obj.curr - 1;
                            if (first) {
                                //加载treetable数据
                                RenderTable(res.data);
                            }
                            else {
                                setTable(currentPage);
                            }
                        }
                    });
                }
            })
        }

		function formatLogDate(date)
		{
			return (
				date.getFullYear() + "/" +
				(date.getMonth() + 1).toString().padStart(2, '0') + "/" +
				date.getDate().toString().padStart(2, '0') + " " +
				date.getHours().toString().padStart(2, '0') + ":" +
				date.getMinutes().toString().padStart(2, '0') + ":" +
				date.getSeconds().toString().padStart(2, '0')
			);
		}

        function setTable(pageCount) {
            var data = form.val('logform');
            data.pageCount = pageCount;
            data.limitCount = limitpagecount;

            data.logtype = "S";
            data.tooltype = "";
            if ($('#systemselect').css('display') != 'none') {
                data.logtype = data.systemtype;
            }

            var date = data.datetime;
            var start_date = "";
            var end_date = "";
            if (!date == false) {
                var times = date.split("至")
                start_date = new Date(times[0]).getTime();
                end_date = new Date(times[1]).getTime();
            }

            data.start_date = start_date;
            data.end_date = end_date;

            AjaxApi.request({
                url: "/Log/GetAllLog.do",
                data: data,
                dataType: 'text',
                success: function (result) {
                    var res = $.eval(result);
                    ////加载table数据
                    for (var i = 0; i < res.data.length; i++) {
                        $.parseUrl(res.data[i].Data, res.data[i]);

						var date = new Date(parseInt(res.data[i].Time));
						res.data[i].StartTime = formatLogDate(date);

						var endDate = new Date(parseInt(res.data[i].Edit));
						res.data[i].EndTime = formatLogDate(endDate);

                        if (!!res.record)
                            res.data.record = res.record;

                        if (!!res.data[i].Recording) {
                            res.data[i].Recording = dataRecord[res.data[i].Recording];
                        }

                        var type = res.data[i].Type.split('_');
                        if (type.length == 3) {
                            for (var key in dataRole) {
                                if (type[1].indexOf(key) >= 0) {
                                    res.data[i].Type = dataRole[key]
                                }
                            }
                            for (var key in dataOp) {
                                if (type[2].indexOf(key) >= 0) {
                                    res.data[i].Type += dataOp[key]
                                }
                            }
                        }
                    }

                    RenderTable(res.data);
                }
            })
        }
    });
</script>