﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>机台管理</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style>
		#addtool .layui-inline {
			margin-right: 0px;
		}

		#addtool .edit {
			width: 200px;
		}

		.borderStyle {
			border: 1px solid red;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/request.js"></script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>机台设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">机台名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="toolname" autocomplete="off" placeholder="请输入机台名称"
							   oninput="cleanSpelChar(this)">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="toolSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="toollist" lay-filter="tooltable"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="userListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="tooltoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="operation">
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改机台')}}</a>
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventView" lay-event="view">{{lang('查看设备')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
		</script>
		<script type="text/html" id="checkedOne">
			<input type="checkbox" name="like1[write]" lay-skin="primary">
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="addtool" class="layui-form" lay-filter="form-addtool" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">机台名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入机台名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">机台编码</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="toolcode" autocomplete="off" placeholder="请输入机台编码"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">机台属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaid" id="areaname" lay-filter="areaname" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">机台属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupid" id="toolgroupname" lay-filter="toolgroupname" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">描述</label>
			</div>
			<div class="layui-inline edit">
				<textarea type="text" class="layui-textarea" style="height: 200px; width:200px; white-space: pre-wrap;overflow:auto;" name="description" autocomplete="off" placeholder="请输入少于200字的描述"
						  oninput="limitTextAreaCount(this)"></textarea>
			</div>
		</div>
	</form>

	<div id="viewDevice" style="display: none; min-width:300px; padding: 15px;">
		<table class="layui-hide" id="devicelist" lay-filter="devicelist"></table>
	</div>
</body>
</html>

<script type="text/javascript">
	var arealist = [];
	var toolgrouplist = [];
	layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel', 'upload'], function () {
		$ = layui.$
			, form = layui.form
			, table = layui.table
			, layer = layui.layer
			, laypage = layui.laypage
			, upload = layui.upload
			, excel = layui.excel;

		lang.call(this, $);
		toolData = null;
		table.init('tooltable', []);
		limitPageCount = 20;
		currentFirstIndex = 1;
		col = toolColumnsName();
		//创建Table的列名
		tableIni = table.render({
			elem: '#toollist'
			, even: true
			, sort: true
			, loading: false
			, toolbar: hasButtonPermission('ToolOptionSet') ? '#tooltoolbar' : []
			, defaultToolbar: ['filter']
			, id: "toollist"
			, title: lang('机台数据表')
			, cols: [col]
			, height: 'full-140'
			, page: {
				theme: '#FF5722'
				, layout: ['count', 'prev', 'page', 'next', 'skip']
                , prev: lang('上一页')
				, next: lang('下一页')
                , limit: limitPageCount
			}
			, data: []
		});

		getAllAreaAndGroup();
		//头工具栏事件
		table.on('toolbar(tooltable)', function (obj) {
			var checkStatus = table.checkStatus(obj.config.id);
			switch (obj.event) {
				case 'getCheckData':
					var data = checkStatus.data;
					Delete(data);
					break;
				case 'isAdd':
					setAreaAndGroup();
					index = layer.open({
						title: lang('增加机台')
						, type: 1
						, resize: false
						, id: "isAdd"
						, content: $('#addtool')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function () {
							$($('p')[0]).hide();
						}
						, btn1: function (index) {
							AddUpdateData({}, 'add', index);
						}
						, end: function () {
							$('#addtool')[0].reset();
							$('#addtool').css('display', 'none');
						}
					});
					break;
				case 'BatchDelete':
					{
						BatchDelete();
						break;
					}
			};
		});

		function BatchDelete() {
			if (checkedItems.length == 0) {
				layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
				return;
			}

			layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
				AjaxApi.request({
					type: 'post',
					url: "/tool/BatchDelete.do",
					data: JSON.stringify(checkedItems),
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);
						if (res.code == "Success") {

							table.reload('toollist', {
								cols: [col],
							})

							$('#normaldiv').show();
							$('#batchdiv').hide();

							layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else {
							layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除机台"), });
						}
						FillDataTable();
					}
				})
			});
		}

		//监听行工具事件
		table.on('tool(tooltable)', function (obj) {
			var data = obj.data;
			if (obj.event === 'del') {
				Delete(data);
			}
			else if (obj.event === 'edit') {
				if (data.Role == '') {
					layer.alert(lang('此用户禁止修改！'), { btn: [lang('确定')], title: lang('提示') });
					return;
				}

				var index1 = layer.open({
					title: lang('修改机台')
					, type: 1
					, resize: false
					, id: 'edit'
					, content: $('#addtool')
					, btn: [lang('确认'), lang('取消')]
					, btnAlign: 'c'
					, success: function () {
						setAreaAndGroup(data.Data1, data.Data2);
						form.render('select');
						form.val('form-addtool', {
							name: data.Name,
							toolcode: data.Data6,
                            description: data.description
						});
					}
					, btn1: function (value, index) {
						AddUpdateData(data, 'edit', index1);
					}
					, end: function () {
						$('#addtool')[0].reset();
						$('#addtool').css('display', 'none');
					}
				});
			}
			else if (obj.event === 'view') {
                getMachineDevice(data.ID);
			}
			else if (obj.event == 'singleclick') {
				var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
				if (checkCell.length > 0) {
					checkCell.click();
				}
			}
		});

		var checkedItems = [];
		table.on('checkbox(tooltable)', function (obj) {
			if (obj.checked) {
				if (obj.type == 'all') {
					checkedItems = table.cache.toollist;
				} else {
					checkedItems.push(obj.data);
				}
			} else {
				if (obj.type == 'all') {
                    checkedItems = [];
				} else {
					var i = 0;
					$.each(checkedItems, function (key, value) {
						if (value.id == obj.data.id) {
							checkedItems.splice(i, 1);
							return false;
						}
						i++;
					})
				}
			}
		});

		form.on('submit(toolSearch)', function (data) {
			FillDataTable();
			return false;
		});
		form.on('select(selRoleVideo)', function (data) {
			FillDataTable();
		})

        form.on("select(areaname)", function (data) {
            form.render('select');
            addSelectIcon('');
        });
        form.on("select(toolgroupname)", function (data) {
            form.render('select');
            addSelectIcon('');
        });

		$('button[type=button]').on('click', function () {
			document.getElementById('form-select').reset();
			FillDataTable();
		})
		function toolColumnsName() {
			var columnsArray = [];
			var columnsNames = [
				{ title: lang("序号"), field: "xuhao", width: 80 }
				, { title: lang("机台名称"), field: "Name" }
				, { title: lang("区域名称"), field: "AreaName" }
				, { title: lang("机台组名称"), field: "UnitName" }
                , { title: lang("备注"), field: "description" }
				, { title: lang("更新时间"), field: "Edit", templet: templet }
			];

			for (var i = 0; i < columnsNames.length; i++) {
				var o = {
					title: columnsNames[i].title,
					field: columnsNames[i].field,
					align: 'center',
					event: 'singleclick',
					templet: columnsNames[i].templet,
					hide: columnsNames[i].hide
                };
                if (columnsNames[i].width) o.width = columnsNames[i].width;
				columnsArray.push(o);
			}

			var columnsLast = {
				fixed: 'right',
				minWidth: 220,
				title: lang("操作"),
				toolbar: "#operation",
				event: 'singleclick',
				align: 'center',
				hide: !hasButtonPermission('ToolOptionSet')
			}
			columnsArray.push(columnsLast);

			var columnsLastAgain = {
				title: lang("选中"),
				type: 'checkbox',
				event: 'singleclick',
				align: 'center',
				hide: false,
			}
			columnsArray.unshift(columnsLastAgain);

			return columnsArray;
		}
		function Delete(data) {
			layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
				AjaxApi.request({
                    url: "../Config/delete.do",
                    data: { id: data.ID },
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);
						if (res.code == "Success") {
                            layer.msg(lang('删除成功'), { icon: 1 });
							FillDataTable();
						}
						else {
							layer.alert(lang('删除失败'), { icon: 2 });
						}
					},
					error: function (r) {
                        layer.alert(lang('删除失败'), { icon: 2 });
						// layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
					}
				})
				layer.close(index);
			});
		}

		// 设备table
        table.render({
            elem: '#devicelist'
            , even: true
            , sort: true
            , loading: false
            , defaultToolbar: []
            , id: "devicelist"
            , cols: [[
                { title: lang("Device名称"), field: "Name" }
                , { title: lang("Device类型"), field: "Type" }
                , { title: lang("Url"), field: "Url" }
                , { title: lang("地标"), field: "Landmark" }
			]]
			, height: '400'
			, page: false
            , data: []
        });
	});
	// 添加、更新
	function AddUpdateData(data, type, dialog) {
        var data1 = form.val("form-addtool");
        if (data1.name.trim().length == 0) {
            layer.alert(lang('名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
            return false;
        }
        if (data1.toolgroupid == '不选')
            data1.toolgroupid = "";

        if (data1.areaid == "不选")
            data1.areaid = "";

        if (data1.toolgroupid.trim().length == 0) {
            layer.alert(lang('组不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
            return false;
        }
        if (data1.areaid.trim().length == 0) {
            layer.alert(lang('区域不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
            return false;
        }
		var newData = {}
        newData['ID'] = data.ID ? data.ID : undefined;
        newData['Name'] = data1.name;
        newData['Data6'] = data1.toolcode;
        newData['Data1'] = data1.areaid;
        newData['Data2'] = data1.toolgroupid;
        newData['description'] = data1.description;
        newData['Type'] = 'Machine';
        newData['Save'] = 1;

        AjaxApi.request({
            url: "../Config/Save.do",
            data: newData,
            dataType: 'text',
            success: function (result) {
                var res = $.eval(result);
				if (res.code == "Success") {
					if (type == 'edit') {
						layer.msg(lang('修改成功'), { icon: 1 });
                        layer.close(dialog);
						FillDataTable();
					} else {
                        layer.alert(lang('保存成功,是否继续?'), {
                            title: lang('增加机台')
                            , btn: [lang('继续'), lang('取消')]
                            , btnAlign: 'c'
                            , btn1: function (subIndex) {
                                $('#addtool')[0].reset();
                                layer.close(subIndex);
                                FillDataTable();
                            }
                            , btn2: function (subIndex) {
                                $('#addtool')[0].reset();
                                layer.close(subIndex);
                                layer.close(dialog);
                                FillDataTable();
                            }
                        });
					}
                    
                }
                else {
                    layer.alert(res.msg, { btn: [lang('确定')], title: lang("错误"), });
                }
            },
            error: function (r) {
                layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("错误"), });
            }
        })
	}
	var col = null;

	var username = '';
	var gData = '';
	function FillDataTable() {
		/*if (window.top) {
			window.top.FillFavTreeTable();
		}*/

		var data = form.val('form-select');
		AjaxApi.request({
			url: "../Config/list.do",
            data: { Type: 'Machine', namelike: data.toolname },
			dataType: 'text',
			success: function (result) {
				var res = $.eval(result);
                var res_data = refillData(res);
                table.reload('toollist', {
                    data: res_data,
					cols: [col],
					page: {
                        theme: '#FF5722'
                        , layout: ['count', 'prev', 'page', 'next', 'skip']
                        , prev: lang('上一页')
						, next: lang('下一页')
						, limit: limitPageCount
						, curr: 1
					}
                });
			}
		})
	}

	function refillData(data) {
        for (var i = 0; i < data.length; i++) {
            var DataStringObj = $.parseUrl(data[i].Data, data[i]);

			data[i].description = DataStringObj.Note;
            data[i].xuhao = i + 1;

			var data1 = data[i].Data1;
            var data2 = data[i].Data2;
            var name1 = arealist.filter(function (i, index, arr) { return i.ID == data1 });
			if (name1.length !== 0) data[i].AreaName = name1[0].Name;
            var name2 = toolgrouplist.filter(function (i, index, arr) { return i.ID == data2 });
            if (name2.length !== 0) data[i].UnitName = name2[0].Name;
        }

        return data;
    }

	function setAreaAndGroup(area, unit) {
		AjaxApi.request({
			url: "/Tool/GetAreaAndGroupListAll.do",
			data: {},
			dataType: 'text',
			success: function (result) {
				var res = $.eval(result);

                arealist = buildTree(res.arealist);
                toolgrouplist = buildTree(res.toolgrouplist);

				$("#areaname").empty();
				$("#toolgroupname").empty();

				$("#areaname").append("<option>不选</option>");
				for (var i = 0; i < arealist.length; i++) {
                    var value = arealist[i].ID;
					/*if (arealist[i].Parent.length != 0)
						value = arealist[i].Parent + arealist[i].ID;
					else
						value = arealist[i].ID + arealist[i].ID;*/

					var option = '';
					if (!!area && !!area.length && area == value)
						option = "<option selected value='" + value + "'>" + arealist[i].Name + "</option>";
					else
						option = "<option value='" + value + "'>" + arealist[i].Name + "</option>";

					$("#areaname").append(option);
				}
                for (var i = 0; i < arealist.length; i++) {
                    if (arealist[i].Parent) {
                        arealist[i].icon =
                            '<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
                    } else {
                        arealist[i].icon =
                            '<i class="layui-icon layui-icon-layer"></i>';
                    }
                }

				$("#toolgroupname").append("<option>不选</option>");
				for (var i = 0; i < toolgrouplist.length; i++) {
                    var value = toolgrouplist[i].ID;
					/*if (toolgrouplist[i].Parent.length != 0)
						value = toolgrouplist[i].Parent + toolgrouplist[i].ID;
					else
						value = toolgrouplist[i].ID + toolgrouplist[i].ID;*/

					var option = '';
					if (!!unit && !!unit.length && unit == value)
						option = "<option selected value='" + value + "'>" + toolgrouplist[i].Name + "</option>";
					else
						option = "<option value='" + value + "'>" + toolgrouplist[i].Name + "</option>";

					$("#toolgroupname").append(option);
				}

                for (var i = 0; i < toolgrouplist.length; i++) {
                    if (toolgrouplist[i].Parent) {
                        toolgrouplist[i].icon =
                            '<i class="layui-icon layui-icon-file" style="padding-left: 15px;"></i>';
                    } else {
                        toolgrouplist[i].icon =
                            '<i class="layui-icon layui-icon-layer"></i>';
                    }
                }

				form.render('select');

                addSelectIcon('')
			}
		})
	}

    function addSelectIcon(id) {
        var areaSelect = $("#areaname" + id);
		var groupSelect = $("#toolgroupname" + id);
        // 添加自定义图标
        var areaDl = areaSelect.next("div.layui-form-select").find("dl");
        areaDl.find("dd:not(.layui-select-tips)").each(function (index) {
            if ($(this).text() != '不选' && arealist[index - 1]) {
				var icon = arealist[index - 1].icon;
                $(this).html(
                    '<span class="option-icon">' +
                    icon +
                    '</span><span class="option-text" style="padding-left: 10px;">' +
                    $(this).text() +
                    "</span>"
                );
            }
        });
        var groupDl = groupSelect.next("div.layui-form-select").find("dl");
        groupDl.find("dd:not(.layui-select-tips)").each(function (index) {
            if ($(this).text() != '不选' && toolgrouplist[index - 1]) {
                var icon = toolgrouplist[index - 1].icon;
                $(this).html(
                    '<span class="option-icon">' +
                    icon +
                    '</span><span class="option-text" style="padding-left: 10px;">' +
                    $(this).text() +
                    "</span>"
                );
            }
		});
    }

	function getAllAreaAndGroup() {
		AjaxApi.request({
			url: "/Tool/GetAreaAndGroupListAll.do",
			data: {},
			dataType: 'text',
			success: function (result) {
				var res = $.eval(result);

				arealist = res.arealist;
				toolgrouplist = res.toolgrouplist;
				FillDataTable();
			}
		})
	}

    function viewDevices(arr) {
        var viewDialog = layer.open({
            title: lang('查看设备')
            , type: 1
			, resize: false
			, area: '60%'
            , id: "viewDeviceDialog"
            , content: $('#viewDevice')
            , btn: [lang('确认'), lang('取消')]
            , btnAlign: 'c'
			, success: function () {
				table.resize('devicelist'); 
                table.reload('devicelist', {
                    data: arr,
                })
            }
			, btn1: function (index) {
                layer.close(viewDialog);
            }
            , end: function () {
                layer.close(viewDialog);
            }
        });
	}

	function getMachineDevice(id) {
        table.reload('devicelist', {
            data: [],
        })
		AjaxApi.request({
			url: "/Equipment/GetEquipment.do",
            data: { machine: id },
			dataType: 'text',
			success: function (result) {
				var res = $.eval(result);
				viewDevices(res.data);
			}
		})
	}

    function buildTree(items) {
        var result = [];
        var map = {};
        var i, item;

        for (i = 0; i < items.length; i++) {
            item = items[i];
            map[item.ID] = item;
        }

        for (i = 0; i < items.length; i++) {
            item = items[i];
            if (item.Parent) {
                var parent = map[item.Parent];
                if (parent) {
                    if (!parent.children) {
                        parent.children = [];
                    }
                    parent.children.push(item);
                }
            } else {
                result.push(item);
            }
        }

        function flattenTree(arr) {
            var flatArray = [];

            function flatten(nodes) {
                var i;
                for (i = 0; i < nodes.length; i++) {
                    flatArray.push(nodes[i]);
                    if (nodes[i].children && nodes[i].children.length) {
                        flatten(nodes[i].children);
                        delete nodes[i].children;
                    }
                }
            }

            flatten(arr);
            return flatArray;
        }

        return flattenTree(result);
    }
</script>