window.onload = function() {
    //配置
    var config = {
        vx: 4,	//小球x轴速度,正为右，负为左
        vy: 1,	//小球y轴速度
        height: 4,	//小球高宽，其实为正方形，所以不宜太大
        width: 4,
        count: 100,		//点个数
        color: "255,255,255", 	//点颜色
        stroke: "255,255,255", 		//线条颜色
        dist: 20000, 	//点吸附距离
        e_dist: 20000, 	//鼠标吸附加速距离
        max_conn: 10 	//点到点最大连接数
    }

    //调用
    CanvasParticle(config);
}