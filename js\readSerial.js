﻿function readSerial(cb)
{
	if (typeof (cb) == typeof (arguments.callee))
	{
		window['readSerial/cb'] = cb;
		return arguments.callee.call(this);
	}

	if (arguments.length > 1)
	{
		var v = cb;
		var ms = [];
		for (var i = 0; i < arguments.length; i++)
			ms.push(arguments[i]);
		cb = window['readSerial/cb'];
		if (!cb)
			return;

		setTimeout(arguments.callee, 500);
		if (typeof (v) != typeof (''))
			return cb.apply(this, ms);

		if (v.indexOf('{') != 0 || v.lastIndexOf('}') != v.length - 1)
			return cb.apply(this, ms);

		try
		{
			v = eval('(' + v + ')');
		}
		catch (x)
		{
			ms.push(x);
			return cb.apply(this, ms);
		}

		if (!v.now)
			return;

		ms[0] = v;
		v.n = v.date = v.time = v.now = new Date(v.now);
		v.mLast = v.rLast = v['msd-last'] = new Date(v['msd-last']);
		v.hasM = v.hasMsd = v.hasMSD = v.hasR = v.hasRfid = v.hasRFID = v['has-rfid'] = new Date(v['has-rfid']);
		v.hasB = v.hasBarcode = v['has-barcode'] = new Date(v['has-barcode']);
		v.isM = v.isMsd = v.isMSD = v.isR = v.isRfid = v.isRFID = v['rfid-connected'] = new Date(v['rfid-connected']);
		v.isB = v.isBarcode = v['barcode-connected'] = new Date(v['barcode-connected']);
		v.bLast = v['barcode-last'] = new Date(v['barcode-last']);
		v.sLast = v['steelyard-last'] = new Date(v['steelyard-last']);
		v.s = v.sData = v.steelyard = v['steelyard-data'];
		if (v.steelyard != '')
		{
			var ts = v.steelyard.split(' ');
			v.w = ts[ts.length - 1];
			if (v.w.indexOf('kg') > 0)
				v.w = (parseFloat(v.w.replace('kg', '')) * 1000).toFixed(3) + 'g';
			else if (v.w.indexOf('lb') > 0)
				v.w = (parseFloat(v.w.replace('lb', '')) * 458.18).toFixed(3) + 'g';
			v.weight = v.w;
		}
		v.b = v.bData = v.barcode = v['barcode-data'];
		if (v.barcode == '')
		{
			if (v.data0 != '' && v.data1 != '' && v.data2 != '' && v.data3 != '')
				v.b = v.bData = v.barcode = v['barcode-data'] = '%' + v.data0 + '%' + v.data1 + '%' + v.data2 + '%' + v.data3 + '%';
		}
		else if (v.data0 == '' && v.data1 == '' && v.data2 == '' && v.data3 == '')
		{
			var ts = v.barcode.split('%');
			if (ts.length > 5)
			{
				v.data0 = ts[1];
				v.data1 = ts[2];
				v.data2 = ts[3];
				v.data3 = ts[4];
				v.data = '%' + v.data0 + '%' + v.data1 + '%' + v.data2 + '%' + v.data3 + '%' + v.data4 + '%';
			}
		}
		v.d = v.data;
		v.d0 = v.data0;
		v.d1 = v.data1;
		v.d2 = v.data2;
		v.d3 = v.data3;
		v.d4 = v.data4;
		v.r = v.rfid = v.t = v.tag = v.data4;
		if (!v['rfid-set'] || v['rfid-set'].indexOf('=') < 0)
			v['rfid-set'] = null;
		v.mSet = v.msdSet = v['msd-set'] = v.rSet = v.rfidSet = v['rfid-set'];
		if (!v['barcode-set'] || v['barcode-set'].indexOf('=') < 0)
			v['barcode-set'] = null;
		v.bSet = v.barcodeSet = v['barcode-set'];
		if (!v['steelyard-set'] || v['steelyard-set'].indexOf('=') < 0)
			v['steelyard-set'] = null;
		v.sSet = v.steelyardSet = v['steelyard-set'];
		return cb.apply(this, ms);
	}

	var ajax = {};
	ajax.url = 'http://localhost:7822/serial';
	ajax.cache = false;
	ajax.dataType = 'text';
	ajax.success = arguments.callee;
	ajax.error = arguments.callee;
	$.ajax(ajax);
}
