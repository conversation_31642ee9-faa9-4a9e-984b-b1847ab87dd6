var selectNode = null; // 当前选中节点
var isEdit = true; // 是否处于编辑状态
var isContextMenuActive = false; // 是否处于右键菜单状态
getQueryVariable('isEdit') == '0' ? (isEdit = false) : (isEdit = true);
var lastX = 0,
  lastY = 0; // 记录鼠标坐标
var MAKE = go.GraphObject.make; // 定义MAKE函数，用于创建go.GraphObject对象

// 画布设置
var myDiagramConfig = {
  grid: MAKE(
    go.Panel,
    "Grid",
    { gridCellSize: new go.Size(8, 8) },
    MAKE(go.Shape, "LineH", { stroke: "lightgray", strokeWidth: 0.5 }),
    MAKE(go.Shape, "LineV", { stroke: "lightgray", strokeWidth: 0.5 })
  ), // 网格
  "draggingTool.isGridSnapEnabled": true,
  "toolManager.mouseWheelBehavior": go.ToolManager.WheelZoom, // 滚轮缩放
  // initialAutoScale: go.Diagram.Uniform, // 图表的初始加载时自动缩放方式为统一缩放
  // "linkingTool.direction": go.LinkingTool.ForwardsOnly, // 设置链接工具的单向链接方向为仅向前
  layout: MAKE(go.LayeredDigraphLayout, {
    isInitial: false,
    isOngoing: false,
    layerSpacing: 50,
  }),
  "undoManager.isEnabled": isEdit ? true : false, // 启用快捷键 复制 粘贴 后退
  "toolManager.hoverDelay": 500, // 悬停节点延时
};

var nodeSelectionAdornmentTemplate = MAKE(
  go.Adornment,
  "Auto",
  MAKE(go.Shape, {
    fill: null,
    stroke: "deepskyblue",
    strokeWidth: 1.5,
    strokeDashArray: [4, 2],
  }),
  MAKE(
    "Button",
    {
      name: "delButton",
      alignment: go.Spot.TopRight,
      "ButtonBorder.fill": "red",
      "ButtonBorder.stroke": "red",
      _buttonFillOver: "#f14747",
      _buttonStrokeOver: "#f14747",
      desiredSize: new go.Size(20, 20),
      cursor: "pointer",
      visible: isEdit ? true : false, // 编辑时显示删除按钮
      click: function (e, obj) {
        deleteNode(e, "btn");
      },
    },
    MAKE(go.TextBlock, "x", {
      font: "8pt sans-serif",
      textAlign: "center",
      background: "red",
      verticalAlignment: go.Spot.Center,
      stroke: "white",
    })
  ),
  MAKE(go.Placeholder)
);
var nodeResizeAdornmentTemplate = MAKE(
  go.Adornment,
  "Spot",
  { locationSpot: go.Spot.Right },
  MAKE(go.Placeholder),
  MAKE(go.Shape, {
    alignment: go.Spot.TopLeft,
    cursor: "nw-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.Top,
    cursor: "n-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.TopRight,
    cursor: "ne-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.Left,
    cursor: "w-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.Right,
    cursor: "e-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.BottomLeft,
    cursor: "se-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.Bottom,
    cursor: "s-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    alignment: go.Spot.BottomRight,
    cursor: "sw-resize",
    desiredSize: new go.Size(6, 6),
    fill: "lightblue",
    stroke: "deepskyblue",
  })
);
var nodeRotateAdornmentTemplate = MAKE(
  go.Adornment,
  { locationSpot: go.Spot.Center, locationObjectName: "CIRCLE" },
  MAKE(go.Shape, "Circle", {
    name: "CIRCLE",
    cursor: "pointer",
    desiredSize: new go.Size(7, 7),
    fill: "lightblue",
    stroke: "deepskyblue",
  }),
  MAKE(go.Shape, {
    geometryString: "M-50 4 L0 4",
    isGeometryPositioned: true,
    stroke: "deepskyblue",
    strokeWidth: 1.5,
    strokeDashArray: [4, 2],
  })
);
nodeSelectionAdornmentTemplate.add();
// 画布中节点的模板
var nodeTemplateConfig = MAKE(
    go.Node,
    "Spot",
    {
        name: "NODE",
        minSize: new go.Size(50, 50),
    },
    { locationSpot: go.Spot.Center },
    new go.Binding("zOrder"), // 绑定节点层级
    new go.Binding("location", "loc", go.Point.parse).makeTwoWay(
        go.Point.stringify
    ), // 绑定节点位置
    {
        selectable: true,
        selectionAdornmentTemplate: nodeSelectionAdornmentTemplate,
    },
    {
        resizable: isEdit ? true : false,
        resizeObjectName: "RESIZE",
        resizeAdornmentTemplate: nodeResizeAdornmentTemplate,
    },
    new go.Binding("desiredSize", "size", go.Size.parse).makeTwoWay(
        go.Size.stringify
    ), // 绑定节点大小
    {
        rotatable: isEdit ? true : false,
        rotateObjectName: "ROTATE",
        rotateAdornmentTemplate: nodeRotateAdornmentTemplate,
    },
    new go.Binding("angle", "rotate").makeTwoWay(), // 绑定旋转角度
    MAKE(
        go.Panel,
        "Auto",
        MAKE(
            go.Shape,
            "Rectangle",
            {
                portId: "",
                fromLinkable: false,
                toLinkable: false,
                cursor: "pointer",
                fill: "white",
                strokeWidth: 2,
            },
            new go.Binding("stroke", "strokeColor").makeTwoWay(),
            new go.Binding("figure"),
            new go.Binding("fill", "fill").makeTwoWay()
        ),
        MAKE(
            go.TextBlock,
            {
                font: "10pt Helvetica, Arial, sans-serif",
                maxSize: new go.Size(160, NaN),
                // width: 20,
                overflow: go.TextBlock.OverflowVisible,
                wrap: go.TextBlock.WrapFit,
                textAlign: "center",
                editable: false,
                margin: 5,
                // margin: new go.Margin(0, 5, 0, 0),
                // isMultiline: false,
            },
            new go.Binding("text").makeTwoWay(),
            new go.Binding("width", "textWidth").makeTwoWay(),
            new go.Binding("alignment", "textAlign", go.Spot.parse).makeTwoWay(
                go.Spot.stringify
            ),
        )
    )
    // {
    //   // handle mouse enter/leave events to show/hide the ports
    //   mouseEnter: (e, node) => showDelIcon(node, true),
    //   mouseLeave: (e, node) => showDelIcon(node, false),
    // }
);
/*var groupTemplateConfig = MAKE(
    go.Group, "Auto",
    {
        background: "transparent",
        // 允许组节点被选中和移动
        selectable: true,
        selectionAdorned: true,
        // 设置组的布局
        layout: $(go.LayeredDigraphLayout, {
            direction: 0, columnSpacing: 10
            //wrappingColumn: 2,
            //spacing: new go.Size(5, 5)
        })
    },
    // 组的边框
    MAKE(go.Shape, "RoundedRectangle",
        {
            fill: "rgba(128,128,128,0.2)",
            stroke: "#4169E1",
            strokeWidth: 2,
            parameter1: 14
        }
    ),
    // 组的标题
    MAKE(go.Panel, "Vertical",
        MAKE(go.TextBlock,
            {
                alignment: go.Spot.Center,
                font: "Bold 12pt Sans-Serif",
                margin: 5
            },
            new go.Binding("text", "text")
        ),
        // 组内容容器
        MAKE(go.Placeholder,
            {
                padding: 10,
                alignment: go.Spot.TopLeft
            }
        )
    )
);*/
function showDelIcon(node, show) {
    // 鼠标悬浮在节点上时，显示添加节点按钮
    var delButton = node.findObject("delButton");
    if (delButton) delButton.visible = show;
}
// 左侧组件模板
var paletteNodeTemplate = MAKE(
    go.Node,
    "Spot",
    { locationSpot: go.Spot.Center },
    new go.Binding("desiredSize", "size", go.Size.parse).makeTwoWay(
        go.Size.stringify
    ), // 绑定节点大小
    MAKE(
        go.Panel,
        "Auto",
        MAKE(
            go.Shape,
            "Rectangle",
            {
                portId: "",
                cursor: "pointer",
                fill: "white",
                strokeWidth: 1,
            },
            new go.Binding("stroke", "strokeColor"),
            new go.Binding("figure"),
            new go.Binding("fill", "fill")
        ),
        MAKE(
            go.TextBlock,
            {
                font: "9pt Helvetica, Arial, sans-serif",
                margin: 5,
                overflow: go.TextBlock.OverflowVisible,
                wrap: go.TextBlock.WrapFit,
                textAlign: "center",
                editable: false,
            },
            new go.Binding("text")
        )
    )
);

// 左侧组件数据
var myPaletteNode = [
    // specify the contents of the Palette
    {
        text: "",
        figure: "Rectangle",
        fill: "#fff",
        strokeColor: "#000",
        size: "50 30",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "RoundedRectangle",
        fill: "#fff",
        strokeColor: "#000",
        size: "50 30",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "Triangle",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 40",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "TriangleDownLeft",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 40",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "Diamond",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 40",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "Ellipse",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 30",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "Pentagon",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 40",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "Hexagon",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 40",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "Trapezoid",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 30",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "FivePointedStar",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 40",
        id: "",
        data: {},
    },
    {
        text: "",
        figure: "LineH",
        fill: "#fff",
        strokeColor: "#000",
        size: "40 20",
        id: "",
        data: {},
    },

    // 组节点，包含子节点
    //{ key: "group1", text: "流程组", isGroup: true },
    //{ key: "child1", text: "步骤1", group: "group1", color: "yellow" },
    //{ key: "child2", text: "步骤2", group: "group1", color: "orange" },
    //{ key: "child3", text: "步骤3", group: "group1", color: "pink" }
];

// 画布节点JSON数据 nodes
var myDiagramNode = {
    class: "GraphLinksModel",
    nodeDataArray: [
        {
            text: "YPCDH11Touch",
            figure: "Circle",
            fill: "#09ff00",
            loc: "-32 320",
            key: "1A3FAF3636A55321A750CBB4DEFC74FC",
            id: "1A3FAF3636A55321A750CBB4DEFC74FC",
            data: { ID: "1A3FAF3636A55321A750CBB4DEFC74FC", Name: "YPCDH11Touch" },
            strokeColor: "#0000ff",
            rotate: 42.721981058889405,
            size: "150 150",
        },
        {
            text: "33333334",
            figure: "Square",
            loc: "-83.34167564050159 55.26650567326971",
            key: "1A9A1187BBB82A7B97AAA844E888E71A",
            id: "1A9A1187BBB82A7B97AAA844E888E71A",
            data: { ID: "1A9A1187BBB82A7B97AAA844E888E71A", Name: "33333334" },
            fill: "#4353bc",
            strokeColor: "#000",
            rotate: 40,
            size: "70 70",
        },
        {
            text: "YEOXT11PC#2",
            figure: "Triangle",
            fill: "#ff5e00",
            loc: "350.8778989150693 140.75971297985927",
            key: "1A3F8022A878C3B4ACC4D6F8A9528DB5",
            id: "1A3F8022A878C3B4ACC4D6F8A9528DB5",
            data: { ID: "1A3F8022A878C3B4ACC4D6F8A9528DB5", Name: "YEOXT11PC#2" },
            strokeColor: "#2aff00",
            rotate: 328.77422033227145,
            size: "204 112",
        },
        {
            text: "YPCDH11CCTV",
            figure: "Hexagon",
            fill: "#bf2d2d",
            strokeColor: "#000",
            id: "1A3FAF0F13303F8186479898AF7B64B1",
            data: { ID: "1A3FAF0F13303F8186479898AF7B64B1", Name: "YPCDH11CCTV" },
            key: "1A3FAF0F13303F8186479898AF7B64B1",
            loc: "4109.410015623088 1786.738933435125",
            size: "338.69182923782705 197.61111145019532",
        },
    ],
    linkDataArray: [],
};
