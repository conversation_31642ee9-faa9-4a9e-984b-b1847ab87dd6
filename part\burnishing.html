﻿<style type="text/css">
	.knob {
		margin: 0px;
		padding: 0px;
		display: inline-block;
		overflow: hidden;
		white-space: nowrap;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		border: 2px solid #98989a;
		box-shadow: 0 1px 12px 1px rgba(0, 0, 0, 0.26);
		background-image: radial-gradient(#00a900 10%, #00aa00 30%, #000000 81%);
	}

	.knob a, .knob i, .knob input {
		margin: 0px;
		padding: 0px;
		overflow: hidden;
		display: inline-block;
		position: relative;
		height: 40px;
		border: none;
	}

	.knob .L {
		background-color: green;
		width: 19px;
		float: left;
		background-image: radial-gradient(23px at 70% 50%, #00a900 10%, #00aa00 30%, #000000 81%);
	}

	.knob .R {
		width: 19px;
		background-color: green;
		float: right;
		background-image: radial-gradient(23px at 30% 50%, #00a900 10%, #00aa00 30%, #000000 81%);
	}

	.knob:hover {
		transform: rotate(30deg);
		background-image: radial-gradient(#EE7B00 10%, #FF8C00 30%, #000000 81%);
	}

	.knob:active {
		transform: rotate(30deg);
		background-image: radial-gradient(#EE0000 10%, #FF0000 30%, #000000 81%);
	}

	.knob .L:hover {
		transform-origin: 20px 20px;
		transform: rotate(-60deg);
	}

	.knob .L:hover ~ .M {
		transform: rotate(-60deg);
	}

	.knob .L:hover ~ .R {
		transform-origin: -1px 20px;
		transform: rotate(-60deg);
	}

	.knob a:hover, .knob input:hover {
		background-color: darkorange;
		background-image: radial-gradient(#EE7B00 10%, #FF8C00 30%, #000000 81%);
	}

	.knob a:active, .knob input:active {
		background-color: red;
		background-image: radial-gradient(#EE0000 10%, #FF0000 30%, #000000 81%);
	}
</style>
<span>
	<span class="knob">
		<input type="button" class="L" onclick="javascript: doExtra.call(this, 1);" />
		<input type="button" class="R" onclick="javascript: doExtra.call(this, 2);" />
	</span>
	<span class="knob">
		<input type="button" class="L" onclick="javascript: doExtra.call(this, 3);" />
		<input type="button" class="R" onclick="javascript: doExtra.call(this, 4);" />
	</span>
	<span class="knob">
		<input type="button" class="L" onclick="javascript: doExtra.call(this, 5);" />
		<input type="button" class="R" onclick="javascript: doExtra.call(this, 6);" />
	</span>
	<input type="button" class="knob" onclick="javascript: doExtra.call(this, 7);" />
	<input type="button" class="knob" onclick="javascript: doExtra.call(this, 8);" />
</span>
