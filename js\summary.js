﻿var gauge_option = {
    series: [
        {
            type: 'gauge',
            min: 0,
            max: 50,
            progress: {
                show: true,
                width: 18
            },
            axisLine: {
                lineStyle: {
                    width: 18,
                }
            },
            axisTick: {
                show: false
            },
            splitLine: {
                length: 15,
                lineStyle: {
                    width: 2,
                    color: '#999'
                }
            },
            axisLabel: {
                distance: 25,
                color: '#999',
                fontSize: 10
            },
            anchor: {
                show: true,
                showAbove: true,
                size: 25,
                itemStyle: {
                    borderWidth: 10
                }
            },
            title: {
                show: false
            },
            detail: {
                valueAnimation: true,
                fontSize: 30,
                offsetCenter: [0, '70%']
            },
            data: [
                {
                    value: 0
                }
            ]
        }
    ]
};

var eqp_ratio_option = {
    xAxis: {
        max: 'dataMax'
    },
    yAxis: {
        type: 'category',
        data: ['KVM1', 'KVM2', 'KVM3'],
        inverse: true,
        animationDuration: 300,
        animationDurationUpdate: 300,
        max: 2 // only the largest 3 bars will be displayed
    },
    series: [
        {
            realtimeSort: true,
            name: '使用次数',
            type: 'bar',
            data: [0, 0, 0],
            label: {
                show: true,
                position: 'right',
                valueAnimation: true
            }
        }
    ],
    legend: {
        show: true
    },
    animationDuration: 0,
    animationDurationUpdate: 3000,
    animationEasing: 'linear',
    animationEasingUpdate: 'linear'
};

var pie_option = {
    title: {
        left: 'center'
    },
    tooltip: {
        trigger: 'item'
    },
    legend: {
        left: 'center'
    },
    series: [
        {
            name: '设备状态：',
            type: 'pie',
            radius: '50%',
            data: [
                { value: 50, name: 'RUN', color: 'Green' },
                { value: 30, name: 'IDLE' },
                { value: 10, name: 'DOWN' },
                { value: 10, name: 'PM' },
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            },
            itemStyle: {
                normal: {
                    color: function (colors) {
                        var colorList = [
                            'Green',
                            'Yellow',
                            'Red',
                            'Blue',
                        ];
                        return colorList[colors.dataIndex];
                    }
                },
            }
        }
    ]
};

var line_option = {
    title: {
        text: '人员访问时长',
        left: '10px',
        top: '10px',
    },
    tooltip: {
        trigger: 'axis',
        formatter: function (params) {
            return params[0].name + ': ' + formatMinutes(params[0].value);
        }
    },
    /*legend: {
        top: '5%',
        right: '10px',
        data: ['Data', 'Data1', 'Data2']
    },*/
    grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
        axisLabel: {
            interval: 0,
            rotate: 25  // 旋转标签
        }
    },
    yAxis: {
        type: 'value',
        name: '时长（分钟）'
    },
    series: [
        {
            name: '时长',
            type: 'line',
            data: [12, 13, 11, 14, 9, 23, 21],
            smooth: false,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
                normal: {
                    width: 1
                }
            },
            areaStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        offset: 0,
                        color: 'rgba(1,231,230, 0.3)'
                    }, {
                        offset: 1,
                        color: 'transparent'
                    }], false),
                    shadowColor: 'rgba(0, 0, 0, 0.1)',
                    shadowBlur: 10
                }
            },
            itemStyle: {
                normal: {
                    color: 'rgb(1,231,230)',
                    borderColor: 'rgba(1,231,230,0.2)',
                    borderWidth: 12

                }
            },
        },
        /*{
            name: 'Data1',
            type: 'line',
            data: [33, 38, 39, 43, 49, 73, 71]
        },
        {
            name: 'Data2',
            type: 'line',
            data: [82, 93, 90, 93, 129, 133, 132]
        }*/
    ],
    dataZoom: [{
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 20,
        height: 15,
        bottom: '10px',
    }, {
        type: 'inside',
        xAxisIndex: [0],
        start: 0,
        end: 20
    }]
};

var circle_pie_option = {
    title: {
        text: 'KVM在线时长',
        left: '10px',
        top: '10px',
    },
    tooltip: {
        trigger: 'item',
        formatter: function (params) {
            return params.name + ': ' + formatMinutes(params.value);
        }
    },
    legend: {
        top: '20%',
        right: '10px',
        orient: 'vertical',
    },
    series: [
        {
            name: '时长',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: false
            },
            data: [
                { value: 1048, name: 'Data1' },
                { value: 735, name: 'Data2' },
                { value: 580, name: 'Data3' },
                { value: 484, name: 'Data4' },
                { value: 300, name: 'Data5' }
            ]
        }
    ]
};

var rose_pie_option = {
    legend: {
        top: 'top'
    },
    series: [
        {
            name: 'Nightingale Chart',
            type: 'pie',
            radius: [50, 125],
            center: ['50%', '50%'],
            roseType: 'area',
            itemStyle: {
                borderRadius: 8
            },
            data: [
                { value: 40, name: 'Data1' },
                { value: 38, name: 'Data2' },
                { value: 32, name: 'Data3' },
                { value: 30, name: 'Data4' },
                { value: 28, name: 'Data5' },
            ]
        }
    ]
};

var barWB_color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#7ecf51', '#3fb1e3', '#637bf8', '#9467bd', '#ffa940'];
var barWB_option = {
    title: {
        text: '机台访问时长',
        left: '10px',
        top: '10px',
    },
    tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
            type: 'shadow',
        },
        formatter: function (params) {
            return params[0].name + ': ' + formatMinutes(params[0].value);
        }
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'],
        axisTick: {
            alignWithLabel: true
        },
        axisLabel: {
            interval: 0,
            rotate: 25  // 旋转标签
        }
    },
    yAxis: {
        type: 'value',
        name: '时长（分钟）'
    },
    series: [
        {
            name: '时长',
            type: 'bar',
            data: [10, 52, 200, 334, 390, 330, 220],
            barWidth: '60%', //柱条的宽度，不设时自适应。
            itemStyle: {
                color: function (params) {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: '#' + Math.random().toString(16).slice(2, 8)
                        }, {
                            offset: 1,
                            color: 'transparent'
                        }
                    ]);
                },
                barBorderRadius: [10, 10, 0, 0], //柱条的圆角
            },
        }
    ],
    dataZoom: [{
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 20,
        height: 15,
        bottom: '10px',
    }, {
        type: 'inside',
        xAxisIndex: [0],
        start: 0,
        end: 20
    }]
};

function formatMinutes(totalMinutes) {
    const days = Math.floor(totalMinutes / (60 * 24));
    const hours = Math.floor((totalMinutes % (60 * 24)) / 60);
    const minutes = totalMinutes % 60;

    let result = '';

    if (days > 0) {
        result += days + '天 ';
    }
    if (hours > 0) {
        result += hours + '小时 ';
    }
    if (minutes > 0 || result === '') {
        result += minutes + '分钟 ';
    }

    return result.trim();
}