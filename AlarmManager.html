﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>AlarmList</title>
    <link href="lib/layui/css/layui.css" rel="stylesheet" />
    <link href="css/common.css" rel="stylesheet" />
    <link rel="stylesheet" href="/user/loginMenu.docss" />
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script type="text/javascript" src="lib/layui/layui.js"></script>
    <script type="text/javascript" src="lib/pickgold.js"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="/Config/List.do?Type=STOREHOUSE&js=storehouse"></script>
    <script src="lib/layui/excel.js"></script>
    <script src="js/xm-select.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
    <script type="text/javascript" src="js/layui-templet.js"></script>
    <script type="text/javascript" src="js/excel.js"></script>
    <script src="js/request.js"></script>
    <style>
        .Line {
            width: 1px;
            height: 500px;
            background: linear-gradient(244deg,rgba(0,150,136,0) 0%,rgba(0,150,136,1) 50%,rgba(0,150,136,0) 100%);
        }

        .selectTool {
            background-color: rgb(26, 188, 156) !important;
            color: white !important;
        }

        #form-alarmedit .edit {
            width: 200px;
        }

        .layui-form-checkbox i {
            position: static;
        }

        @media screen and (max-width: 450px) {
            .layui-form-item .layui-inline {
                display: inline-block;
                margin-bottom: 0px !important;
            }
        }

        #list-container {
            height: calc(100% - 90px);
            overflow: auto;
            position: relative;
        }

            #list-container .table-body {
                overflow: hidden; /* 隐藏滚动条 */
                max-height: 100%; /* 设置最大高度 */
            }

            #list-container table {
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
    </style>
    <script type="text/javascript">
        function onResize() {
            const windowInfo = {
                width: window.innerWidth,
                height: window.innerHeight
            }

            $('.layui-transfer-box').each(function (index, item) {
                $(item).css('height', windowInfo.height - 80);
            });

            $('.layui-transfer-box:eq(2)').css('width', windowInfo.width - 340);
            $('#list-container').css('height', windowInfo.height - 164);
            //ReloadAlarmTable(table.cache.alarmlist);
        }
        $(document).on('ready', function () { onResize() })
        window.addEventListener('resize', onResize);

        layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate', 'excel', 'upload'], function () {
            $ = layui.$
                , laydate = layui.laydate
                , form = layui.form
                , table = layui.table
                , layer = layui.layer
                , element = layui.element
                , excel = layui.excel
                , laypage = layui.laypage
                , upload = layui.upload;


            FillToolTable();

            $('button[type=button]').on('click', function () {
                document.getElementById('form-alarm').reset();
                var videoID = $('#toolist .selectTool').attr('id');
                if (!!videoID) {
                    FillToolAlarmList(videoID);
                }
            })

            form.on('submit(alarmSearch)', function (data) {
                var videoID = $('#toolist .selectTool').attr('id');
                if (!!videoID) {
                    FillToolAlarmList(videoID);
                }
                return false;
            });

            table.on('toolbar(alarmlist)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                switch (obj.event) {
                    case 'add':
                        index = layer.open({
                            title: lang('增加报警设置')
                            , type: 1
                            , resize: false
                            , id: "add"
                            , content: $('#form-alarmedit')
                            , btn: [lang('确认'), lang('取消')]
                            , btnAlign: 'c'
                            , btn1: function (index) {
                                var data = form.val("form-alarmedit");
                                if (data.alarmname.trim().length == 0) {
                                    layer.alert(lang('报警名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                    return false;
                                }

                                if (data.alarmcode.trim().length == 0) {
                                    layer.alert(lang('报警代码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                    return false;
                                }

                                data.VideoID = $('#toolist .selectTool').attr('id');
                                AjaxApi.request({
                                    url: "/Alarm/AddToolAlarm.do",
                                    data: data,
                                    dataType: 'text',
                                    success: function (result) {
                                        var res = $.eval(result);
                                        if (res.code == "Success") {
                                            layer.alert(lang('保存成功,是否继续?'), {
                                                title: lang('增加用户')
                                                , btn: [lang('继续'), lang('取消')]
                                                , btnAlign: 'c'
                                                , btn1: function (subIndex) {
                                                    $('#form-alarmedit')[0].reset();
                                                    layer.close(subIndex);
                                                    FillToolAlarmList(data.VideoID);
                                                }
                                                , btn2: function (subIndex) {
                                                    $('#form-alarmedit')[0].reset();
                                                    layer.close(subIndex);
                                                    FillToolAlarmList(data.VideoID);
                                                }
                                            });
                                        }
                                        else {
                                            layer.alert(res.msg, { btn: [lang('确定')], title: lang("增加报警设置"), });
                                        }
                                    },
                                    error: function (r) {
                                        layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加报警设置"), });
                                    }
                                })
                            }
                            , end: function () {
                                $('#form-alarmedit')[0].reset();
                                $('#form-alarmedit').css('display', 'none');
                            }
                        });
                        break;
                    case 'ExportData':
                        {
                            exportExcelData();
                            break;
                        }
                    case 'BatchDelete':
                        {
                            BatchDelete();
                            break;
                        }
                };
            });

            /**
            * 导出 Excel 数据
            */
            function exportExcelData() {
                var videoID = $('#toolist .selectTool').attr('id');
                var data = form.val('form-alarm');
                var gData = { VideoID: videoID, alarmNameLike: data.alarmname, code: data.alarmcode, pageCount: 0, limitCount: limitpagecount };

                AjaxApi.request({
                    url: "/Alarm/GetAlarmListAllExport.do",
                    data: gData,
                    dataType: 'text',
                    success: function (result) {
                        var res = $.eval(result);
                        for (var i = 0; i < res.data.length; i++) {
                            $.parseUrl(res.data[i].Data, res.data[i]);
                            $.parseUrl(res.data[i].Data9, res.data[i]);

                            if (!!res.data[i].Enable && res.data[i].Enable == 'on')
                                res.data[i].Enable = '启用';
                            else
                                res.data[i].Enable = '禁用';

                        }
                        //加载table数据
                        table.reload('alarmlistEx', {
                            data: res.data,
                            limit: res.data.length,
                            done: function (res, curr, count) {
                                exportDIYStyleExcel('alarmlistEx', excel, lang('报警设置.xlsx'));
                            }
                        });
                    }
                })
            }

            table.on('tool(alarmlist)', function (obj) {
                var data = obj.data;
                if (obj.event === 'del') {
                    layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
                        AjaxApi.request({
                            url: "/Alarm/DeleteAlarm.do",
                            data: { id: data.ID },
                            dataType: 'text',
                            success: function (result) {
                                var res = $.eval(result);
                                if (res.code == "Success") {
                                    layer.msg(lang('删除成功'), { icon: 1 });
                                    var videoID = $('#toolist .selectTool').attr('id');
                                    FillToolAlarmList(videoID);
                                }
                                else {
                                    layer.msg(lang('删除失败'), { icon: 2 });
                                }
                            },
                            error: function (r) {
                                layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
                            }
                        })
                        layer.close(index);
                    });
                }
                else if (obj.event == 'edit') {
                    var index1 = layer.open({
                        title: lang('修改报警设置')
                        , type: 1
                        , resize: false
                        , id: 'edit'
                        , content: $('#form-alarmedit')
                        , btn: [lang('确认'), lang('取消')]
                        , btnAlign: 'c'
                        , success: function () {
                            form.val('form-alarmedit', {
                                alarmname: data.Name,
                                alarmcode: data.Data1,
                                alarmlevel: data.Level,
                                alarmnote: data.Note,
                                alarmenable: data.enable,
                            });

                            $('#form-alarmedit input[name=alarmname]').attr("disabled", true);
                            $('#form-alarmedit input[name=alarmname]').addClass('layui-disabled');
                            $('#form-alarmedit input[name=alarmcode]').attr("disabled", true);
                            $('#form-alarmedit input[name=alarmcode]').addClass('layui-disabled');
                        }
                        , btn1: function (value, index) {
                            var data1 = form.val("form-alarmedit");
                            var videoID = $('#toolist .selectTool').attr('id');
                            data1.videoID = videoID;
                            data1.id = data.ID;

                            AjaxApi.request({
                                url: "/Alarm/EditAlarm.do",
                                data: data1,
                                dataType: 'text',
                                success: function (result) {
                                    var res = $.eval(result);
                                    if (res.code == "Success") {
                                        layer.msg(lang('修改成功'));
                                        layer.close(index1);

                                        FillToolAlarmList(videoID);
                                    }
                                    else {
                                        layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
                                    }
                                }
                            })
                        }
                        , end: function () {
                            $('#form-alarmedit')[0].reset();
                            $('#form-alarmedit').css('display', 'none');
                            $('#form-alarmedit input[name=alarmname]').attr("disabled", false);
                            $('#form-alarmedit input[name=alarmname]').removeClass('layui-disabled');
                            $('#form-alarmedit input[name=alarmcode]').attr("disabled", false);
                            $('#form-alarmedit input[name=alarmcode]').removeClass('layui-disabled');
                        }
                    });
                }
            });

            function FillToolTable() {
                var gdata = { pagecount: 0, limitCount: 2000, toolnameLike: '' };

                AjaxApi.request({
                    url: "/Equipment/GetAllAlarmEquipment.do",
                    data: gdata,
                    dataType: 'text',
                    success: function (result) {
                        var res = $.eval(result);
                        //todo test
                        listData = res.data
                        // RenderToolTable(res.data, res.data);
                        setField(res.data)
                    }
                })
            }

            var checkedItems = [];
            table.on('checkbox(alarmlist)', function (obj) {
                if (obj.checked) {
                    if (obj.type == 'all') {
                        checkedItems = table.cache.alarmlist;
                    } else {
                        checkedItems.push(obj.data);
                    }
                } else {
                    if (obj.type == 'all') {
                        checkedItems = [];
                    } else {
                        var i = 0;
                        $.each(checkedItems, function (key, value) {
                            if (value.id == obj.data.id) {
                                checkedItems.splice(i, 1);
                                return false;
                            }
                            i++;
                        })
                    }
                }
            });

            function BatchDelete() {
                if (checkedItems.length == 0) {
                    layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
                    return;
                }

                layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
                    AjaxApi.request({
                        type: "post",
                        url: "/Alarm/BatchDelete.do",
                        data: JSON.stringify(checkedItems),
                        dataType: 'text',
                        success: function (result) {
                            var res = $.eval(result);
                            if (res.code == "Success") {
                                checkedItems = [];
                                table.reload('alarmlist', {
                                    cols: [columnsName()],
                                })

                                $('#normaldiv').show();
                                $('#batchdiv').hide();

                                layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
                            }
                            else {
                                layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除报警设置"), });
                            }
                            checkedItems = [];

                            var videoID = $('#toolist .selectTool').attr('id');
                            FillToolAlarmList(videoID);
                        }
                    })
                    layer.close(index);
                });
            }
        });

        var limitpagecount = 20, currentPage = 1;
        // RAF优化动画效果
        var windowRequestAnimationFrame =
            window.requestAnimationFrame ||
            window.webkitRequestAnimationFrame ||
            window.mozRequestAnimationFrame ||
            window.oRequestAnimationFrame ||
            window.msRequestAnimationFrame ||
            function (callback) {
                return setTimeout(callback, 1000 / 60);
            }; //时间刻度自行设置
        var windowCancelAnimationFrame = window.cancelAnimationFrame || function (name) { clearTimeout(name) };
        var listData = [];
        var searchResult = []; // 筛选列表集合
        var clickListId = '';
        // 虚拟滚动列表
        var container;
        var itemHeight = 38;
        var visibleItemCount = 0;
        var lastStartIndex = 0;
        var lastEndIndex = 0;
        var scrollRAF;
        function scrollHandle() {
            if (scrollRAF) {
                windowCancelAnimationFrame(scrollRAF);
            }
            scrollRAF = windowRequestAnimationFrame(updateVisibleItems);
        }
        function setField(data) {
            listData = data;
            container = document.getElementById("list-container");
            visibleItemCount = Math.ceil(container.clientHeight / itemHeight) + 2;
            lastEndIndex = lastStartIndex + visibleItemCount;
            container.addEventListener("scroll", scrollHandle);
            getWrapperScrollHeight(listData);
            RenderToolTable(listData.slice(lastStartIndex, lastEndIndex));
        }
        window.addEventListener("beforeunload", () => {
            container.removeEventListener("scroll", scrollHandle);
        });
        function getWrapperScrollHeight(data) {
            var wrapper = document.getElementById("list-wrapper");
            wrapper.style.height = data.length * 38 + "px";
        }
        // 获取可视范围内的数据
        function updateVisibleItems() {
            var scrollTop = container.scrollTop;
            var startIndex = Math.floor(scrollTop / itemHeight);
            var endIndex = Math.min(startIndex + visibleItemCount, listData.length);
            var wrapper = document.getElementsByClassName("layui-table")[0];
            lastStartIndex = startIndex;
            lastEndIndex = endIndex;

            if (searchResult.length > 0) {
                RenderToolTable(searchResult.slice(startIndex, endIndex));
            } else {
                RenderToolTable(listData.slice(startIndex, endIndex));
            }
            wrapper.style.top = startIndex * itemHeight + "px";
        }
        function RenderToolTable(showdata) {
            var tb = '<tbody>';
            if (showdata.length === 0) return false;
            for (var i = 0; i < showdata.length; i++) {
                if (showdata[i].Type != CS.VideoData.TypeKVM)
                    continue;

                var tr = '';
                if (i % 2 == 1) {
                    tr = '<tr id="' + showdata[i].ID + '" style="background-color:#F8F8F8;" data-index="' + i + '">';
                }
                else {
                    i == 0 ? tr = '<tr id="' + showdata[i].ID + '" class="selectTool" data-index="' + i + '">' :
                    tr = '<tr id="' + showdata[i].ID + '" data-index="' + i + '">';
                }

                var td = '<td style="border: 0px;" data-field="Name" data-key="1-0-0" align="center" lay-event="singleclick">';

                var div = '<div>' + showdata[i].Name;
                div += '</div>';

                td += div;
                td += '</td>'

                tr += td;
                tr += '</tr>';
                tb += tr;
            }

            tb += '</tbody>';
            $('#toolist').html(tb);
            if (!!clickListId) { //选中的li重新加上背景色
                $('#' + clickListId).addClass('selectTool')
            }
            
            toolSelected(document.getElementById(showdata[0].ID));
            $('#toolist').on('click', 'tr', function () {
                toolSelected(this);
            });
            // $('#toolist').data('data', tabledata);
            //onResize();
        }

        function columnsName(checked) {
            var columnsArray = [];

            var columnsFirst = {
                type: 'checkbox',
                event: 'singleclick',
                align: 'center',
                hide: false,
            }
            columnsArray.push(columnsFirst);

            var columnsNames = [
                {
                    title: lang("序号"), field: "xuhao", templet: function (e) {
                        return limitpagecount * (currentPage - 1) + e.LAY_INDEX
                    }
                }
                , { title: lang("报警名称"), field: "Name" }
                , { title: lang("报警代码"), field: "Data1" }
                , { title: lang("报警等级"), field: "Level" }
                , { title: lang("报警启用"), field: "Enable" }
                , { title: lang("报警描述"), field: "Note" }
                , { title: lang('更新者'), field: 'EditUser' }
                , { title: lang("更新时间"), field: "Edit", templet: templet }
            ];

            for (var i = 0; i < columnsNames.length; i++) {
                var o = {
                    title: columnsNames[i].title,
                    field: columnsNames[i].field,
                    align: 'center',
                    event: 'singleclick',
                    templet: columnsNames[i].templet,
                    hide: columnsNames[i].hide
                };
                columnsArray.push(o);
            }

            var columnsLast = {
                minWidth: 120,
                title: lang("操作"),
                toolbar: "#operation",
                event: 'singleclick',
                align: 'center',
				hide: !hasButtonPermission('AlarmOptionSet')
            }
            columnsArray.push(columnsLast);

            return columnsArray;
        }

        function renderAlarmTable() {
            table.render({
                elem: '#alarmlist'
                , even: true
                , sort: true
                , loading: false
				, toolbar: hasButtonPermission('AlarmOptionSet') ? '#toolbar' : []
                , defaultToolbar: ['filter']
                , id: "alarmlist"
                , title: lang('报警数据表')
                , limit: limitpagecount
                , cols: [columnsName()]
                , data: []
                , height: 'full-225'
            });

            table.render({
                elem: '#alarmlistEx'
                , even: true
                , sort: true
                , loading: false
                , toolbar: '#toolbar'
                , defaultToolbar: ['filter']
                , id: "alarmlistEx"
                , title: lang('报警数据表')
                , limit: limitpagecount
                , cols: [columnsName()]
                , data: []
                , height: 0
            });
        }

        function toolSelected(elem) {
            $('#toolist tr').each(function (index, item) {
                if (index % 2 == 1) {
                    $('#' + item.id).css('background-color', '#F8F8F8');
                }
                else {
                    $('#' + item.id).css('background-color', 'white');
                }

                $('#' + item.id).css('color', '#666');
                $('#' + item.id).removeClass('selectTool');
            })

            $('#' + elem.id).addClass('selectTool');
            clickListId = elem.id
            renderAlarmTable();
            FillToolAlarmList(elem.id);
        }

        function FillToolAlarmList(id) {
            var data = form.val('form-alarm');
            var gData = { VideoID: id, alarmNameLike: data.alarmname, code: data.alarmcode, pageCount: 0, limitCount: limitpagecount };
            AjaxApi.request({
                url: "/Alarm/GetToolAlarmListAll.do",
                data: gData,
                dataType: 'text',
                success: function (result) {
                    var res = $.eval(result);
                    ////加载table数据
                    for (var i = 0; i < res.data.length; i++) {
                        $.parseUrl(res.data[i].Data, res.data[i]);
                        $.parseUrl(res.data[i].Data9, res.data[i]);

                        res.data[i].enable = res.data[i].Enable;
                        if (!!res.data[i].Enable && res.data[i].Enable == 'on')
                            res.data[i].Enable = '启用';
                        else
                            res.data[i].Enable = '禁用';

                    }

                    laypage.render({
                        elem: 'footer'
                        , count: res.count
                        , theme: '#FF5722'
                        , limit: limitpagecount
                        , prev: lang('上一页')
                        , next: lang('下一页')
                        , layout: ['count', 'prev', 'page', 'next', 'skip']
                        , jump: function (obj, first) {
                            pageCount = obj.curr;
                            currentPage = obj.curr;
                            if (first) {
                                //加载treetable数据
                                ReloadAlarmTable(res.data);
                            }
                            else {
                                setTable({ VideoID: id, alarmNameLike: data.alarmname, code: data.alarmcode, pageCount: pageCount - 1, limitCount: limitpagecount });
                            }
                        }
                    });
                }
            })
        }

        function setTable(data) {
            AjaxApi.request({
                url: "/Alarm/GetToolAlarmListAll.do",
                data: data,
                dataType: 'text',
                success: function (result) {
                    var res = $.eval(result);
                    for (var i = 0; i < res.data.length; i++) {
                        $.parseUrl(res.data[i].Data, res.data[i]);
                        $.parseUrl(res.data[i].Data9, res.data[i]);

                        if (!!res.data[i].Enable && res.data[i].Enable == 'on')
                            res.data[i].Enable = '启用';
                        else
                            res.data[i].Enable = '禁用';

                    }

                    //加载treetable数据
                    ReloadAlarmTable(res.data);
                }
            })
        }

        function ReloadAlarmTable(data) {
            table.reload('alarmlist', {
                data: data,
                cols: [columnsName()],
                done: function (res, curr, count) {
                    initupload();
                }
            });
        }

        function onToolSearch(elem) {
            searchResult = [];
            var table_data = listData;
            if (table_data == null)
                return;

            var search = elem.value;

            for (var i = 0; i < table_data.length; i++) {
                if (table_data[i].Name.toLowerCase().indexOf(search.toLowerCase()) >= 0) {
                    searchResult.push(table_data[i]);
                }
            }
            container.scrollTop = 0; // 重置滚轮高度
            getWrapperScrollHeight(searchResult);
            RenderToolTable(searchResult);

        }

        //选择导入文件
        function initupload() {
            upload.render({
                elem: '#LAY-excel-upload' //绑定元素
                , auto: false //选择文件后不自动上传
                , accept: 'file'
                , choose: function (obj) {// 选择文件回调
                    var files = obj.pushFile()
                    // var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
                    var fileArr = Object.keys(files).map(function (key) {
                        return files[key];
                    });
                    // 用完就清理掉，避免多次选中相同文件时出现问题
                    for (var index in files) {
                        if (files.hasOwnProperty(index)) {
                            delete files[index]
                        }
                    }
                    $('#LAY-excel-upload').next().val('');
                    init(fileArr);
                }
            });
        }

        //导入数据
        function init(files) {
            excel.importExcel(files, {}, function (data) {
                data = excel.filterImportData(data, {
                    'Name': 'A'
                    , 'Code': 'B'
                    , 'Level': 'C'
                    , 'Note': 'D'
                    , 'Enable': 'E'
                });

                var videoID = $('#toolist .selectTool').attr('id');

                if (data[0].Sheet1.length > 0) {
                    data[0].Sheet1.splice(0, 1);
                    if (data[0].Sheet1.length > 200) {
                        layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
                        return;
                    }
                    for (var i = 0; i < data[0].Sheet1.length; i++) {
                        if ($.trim(data[0].Sheet1[i].Name + '') == '') {
                            layer.alert(lang('报警名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
                            return;
                        }
                        if ($.trim(data[0].Sheet1[i].Code + '') == '') {
                            layer.alert(lang('报警代码不可为空！'), { btn: [lang('确定')], title: lang('提示') });
                            return;
                        }
                        else {
                            data[0].Sheet1[i].Data1 = data[0].Sheet1[i].Code;
                        }
                        if ($.trim(data[0].Sheet1[i].Level + '') == '') {
                            data[0].Sheet1[i].Level = '0';
                        }
                        if ($.trim(data[0].Sheet1[i].Enable + '') == '') {
                            data[0].Sheet1[i].Enable = 'on';
                        }
                        else {
                            if (data[0].Sheet1[i].Enable == '启用') {
                                data[0].Sheet1[i].Enable = 'on';
                            }
                            else {
                                data[0].Sheet1[i].Enable = 'off';
                            }
                        }
                        if (IsCheckSpecialCharUser(data[0].Sheet1[i].Name) > 0
                            || IsCheckSpecialCharUser(data[0].Sheet1[i].Code > 0)
                            || IsCheckSpecialCharUser(data[0].Sheet1[i].Level > 0)
                            || IsCheckSpecialCharUser(data[0].Sheet1[i].Note) > 0
                            || IsCheckSpecialCharUser(data[0].Sheet1[i].Enable) > 0) {
                            layer.alert(lang('存在特殊字符，禁止导入'), { btn: [lang('确定')], title: lang('提示') });
                            return;
                        }

                        if ($.trim(data[0].Sheet1[i].Note + '') == '') {
                            data[0].Sheet1[i].Data = '&Level=' + data[0].Sheet1[i].Level + '&' +
                                '&Enable=' + data[0].Sheet1[i].Enable + '&';
                        }
                        else {
                            data[0].Sheet1[i].Data = '&Note=' + data[0].Sheet1[i].Note + '&' +
                                '&Level=' + data[0].Sheet1[i].Level + '&' +
                                '&Enable=' + data[0].Sheet1[i].Enable + '&';
                        }

                        data[0].Sheet1[i].Parent = videoID;
                    }
                }
                AjaxApi.request({
                    type: "post",
                    url: "../Alarm/ImportData.do",
                    data: JSON.stringify(data[0].Sheet1),
                    dataType: 'text',
                    success: function (result) {
                        var res = $.eval(result);
                        if (res.code == "Success") {
                            FillToolAlarmList(videoID);
                        }
                    }
                });
            });
        }
    </script>
</head>
<body style="overflow-x: auto;">
    <div style="margin:10px;">
        <fieldset class="layui-elem-field layui-field-title">
            <legend><span>报警设置</span></legend>
        </fieldset>

        <div style="height: calc(100% - 66px); min-width: 1074px; min-height: 600px;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <div class="layui-transfer-box" style="width: 258px !important; height: 100% !important; min-height: 600px;">
                        <div class="layui-transfer-header">
                            <span style="width: 100%; text-align:center; display:inline-block">全部机台</span>
                        </div>

                        <div class="layui-transfer-search">
                            <i class="layui-icon layui-icon-search"></i>
                            <input type="text" class="layui-input" placeholder="请输入机台" onkeyup="onToolSearch(this)">
                        </div>

                        <div id="list-container">
                            <div id="list-wrapper">
                                <table style="border: 0px;" class="layui-table" id="toolist" lay-filter="toollistevent" lay-skin="row"></table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-transfer-box Line" style="width: 3px; height: calc(100% - 60px); border-radius: 5px; border: none; min-height: 600px;"></div>
                </div>

                <div class="layui-inline">
                    <div class="layui-transfer-box" style="min-width: 740px; min-height: 600px;">
                        <form class="layui-form" lay-filter="form-alarm" id="form-alarm">
                            <div class="layui-field-box" style="text-align:center;">
                                <div class="layui-form-item" style="margin:0px;">
                                    <div class="layui-inline">
                                        <lable class="layui-form-label" style="width:85px;">报警名称：</lable>
                                    </div>
                                    <div class="layui-inline">
                                        <input style="width:130px;" type="text" class="layui-input" name="alarmname" autocomplete="off" placeholder="请输入报警名称"
                                               oninput="cleanSpelChar(this)">
                                    </div>
                                    <div class="layui-inline">
                                        <lable class="layui-form-label" style="width:85px;">报警代码：</lable>
                                    </div>
                                    <div class="layui-inline">
                                        <input style="width:130px;" type="number" min="0" class="layui-input" name="alarmcode" placeholder="请输入报警代码">
                                    </div>
                                    <div class="layui-inline">
                                        <button type="submit" class="layui-btn" lay-filter="alarmSearch" lay-submit=""><span>查询</span></button>
                                    </div>
                                    <div class="layui-inline">
                                        <button type="button" class="layui-btn"><span>重置</span></button>
                                    </div>
                                </div>
                            </div>
                            <table class="layui-table" id="alarmlist" lay-filter="alarmlist"></table>
                            <div class="layui-inline" style="display:none;">
                                <table class="layui-hide" id="alarmlistEx" lay-filter="alarmlistEx"></table>
                            </div>
                            <script type="text/html" id="toolbar">
                                <div class="layui-btn-container" style="float:left;">
                                    <div id="normaldiv" class="layui-inline">
                                        <div id="divBC" class="layui-inline" style="margin: 0px;">
                                            <button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="add">{{lang('增加')}}</button>
                                        </div>
                                        <div id="divMB" class="layui-inline" style="margin: 0px;">
                                            <a class="layui-btn layui-btn-sm m_UserMB" href="doc/AlarmTemplate.xls">{{lang('下载模板')}}</a>
                                        </div>
                                        <div id="divDR" class="layui-inline" style="margin: 0px;">
                                            <button type="button" class="layui-btn layui-btn-sm m_UserDR" id="LAY-excel-upload">{{lang('导入')}}</button>
                                        </div>
                                        <div id="divDC" class="layui-inline" style="margin: 0px;">
                                            <button type="button" class="layui-btn layui-btn-sm m_ExportFile" lay-event="ExportData">{{lang('导出')}}</button>
                                        </div>
                                        <div id="divBC_d" class="layui-inline" style="margin: 0px;">
                                            <button type="button" class="layui-btn-danger layui-btn layui-btn-sm m_UserBC" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
                                        </div>
                                    </div>
                                    <div class="layui-inline" id="batchdiv" style="display:none" name="batchdiv">
                                        <div class="layui-inline" style="margin: 0px;">
                                            <button type="button" class="layui-btn-danger layui-btn layui-btn-sm m_UserBC" lay-event="ConfirmBatchDelete" id="batchdeleteConfirmBtn">{{lang('批量删除确定')}}</button>
                                        </div>
                                        <div class="layui-inline" style="margin: 0px;">
                                            <button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="CancelBatchDelete" id="batchdeleteCancelBtn">{{lang('取消')}}</button>
                                        </div>
                                    </div>
                                </div>
                            </script>
                            <script type="text/html" id="operation">
                                <a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
                                <a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
                            </script>
                            <div id="footer" style="text-align:center;"></div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form class="layui-form" lay-filter="form-alarmedit" id="form-alarmedit" style="display: none; padding-top: 20px; padding-right: 20px;">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:119px;">报警名称</label>
            </div>
            <div class="layui-inline edit">
                <input type="text" class="layui-input" name="alarmname" autocomplete="off" placeholder="请输入报警名称"
                       oninput="cleanSpelChar(this)">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:119px;">报警代码</label>
            </div>
            <div class="layui-inline edit">
                <input type="number" class="layui-input" min="0" name="alarmcode" placeholder="请输入报警代码"
                       oninput="cleanSpelChar(this)">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:119px;">报警等级</label>
            </div>
            <div class="layui-inline edit">
                <input type="text" class="layui-input" name="alarmlevel" autocomplete="off" placeholder="请输入报警名称"
                       oninput="cleanSpelChar(this)">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:119px;">报警启用</label>
            </div>
            <div class="layui-inline edit">
                <input style="height:20px;" type="checkbox" class="layui-input" name="alarmenable"
                       lay-skin="switch" lay-text="开启|关闭">
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:119px;">报警描述</label>
            </div>
            <div class="layui-inline edit">
                <textarea type="text" class="layui-textarea" style="height: 200px; width:200px; white-space: pre-wrap;overflow:auto;" name="alarmnote" autocomplete="off" placeholder="请输入少于200字的描述"
                          oninput="limitTextAreaCount(this)"></textarea>
            </div>
        </div>
    </form>
</body>
</html>