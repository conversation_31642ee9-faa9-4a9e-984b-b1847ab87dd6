﻿/*!
 * index.js v24.7.30
 *
 * Copyright 2028 Cowin
 * Author: <PERSON>
 * Editor: <PERSON>
 *
 * Date: 2024-07-30
 */

if (!!window.require)
{
	try
	{
		eval('(const { parse } = require("../lib/layui/excel");)');
	}
	catch (x) { }
}

window.videoDB = function (name, value)
{
	var i = arguments.length;
	var ovs = window.openVideoState;
	if (!ovs)//初始化状态
	{
		if (!window.login || !window.login.Alive)
			return 0;

		ovs = {};
		ovs.db = arguments.callee;
		ovs.sid = window.ldb('(v)sid');
		if (ovs.sid != window.login.Alive)
		{
			ovs.sid = window.login.Alive;
			window.ldb('(v)', 0, 'clear');//清理所有 openVideo 数据
			window.ldb('(v)sid', ovs.sid);
		}
		ovs.opens = {};
		window.openVideoState = ovs;
	}
	if (!i)
		return ovs;

	if (i > 2)
	{
		arguments[0] = '(v)' + name;
		return window.ldb.apply(this, arguments);
	}

	if (i > 1)//set
	{
		ovs[name] = value;
		return window.ldb.call(this, '(v)' + name, value);
	}

	return window.ldb.call(this, '(v)' + name);
};
window.vdb = window.videoDB;

/**
 * 
 * @param {any} id
 * @param {any} url
 * @param {any} data Attech Query; eg 
 * @returns
 */
window.openVideo = function (id, url, data)
{
	var i = arguments.length;
	if (!i)
	{
		window.openVideoInterval.call(this);//无参数
		return -1;
	}

	var ovs = window.vdb();
	if (!ovs)//初始化状态
	{
		if (!layui.notice)
			layui.layer.msg(lang('未登录！'));
		else
			layui.notice.warning(lang('未登录！'));
		return -2;
	}

	var ajax = {};
	ajax.now = Date.now();
	if (!id)
	{
		if (url == setInterval || url == 'setInterval')
			return window.openVideoInterval.call(this, id, url);
	}
	else
	{
		if (!isNaN(id.Sn))//close from openVideo (other process close callback)
		{
			data = id;
			id = data.ID;
			url = data.Url;
			if (!layui.notice)
				layui.layer.msg(data.Sn + ' Video(' + url + ') was exited.');
			else
				layui.notice.info(data.Sn + ' Video(' + url + ') was exited.');
			window.serverMessage.call(window, 'JobVideo?ID=' + id + '&User=' + ovs.sid + '&Sweep=Close');
			return 4;
		}

		if (!!id.Url)
		{
			url = id.Url;
			id = id.ID;
		}
	}
	if (!window.Data)
	{
		layui.notice.error(lang('不支持操作界面！'));
		return -3;
	}

	ajax.json = window.CS.VideoData[id];
	if (!ajax.json)
	{
		ajax.result = window.ldb('#' + id);
		if (!ajax.result)
		{
			layui.notice.error(lang('设备不存在或没有该设备的权限！'));
			return -4;
		}

		try
		{
			ajax.json = $.eval(ajax.result);
		}
		catch (x)
		{
			layui.notice.error(lang('无效的设备数据，无法打开指定设备的操作界面！'));
			return -5;
		}
	}
	url = ajax.json.Url;
	if (!url || !url.indexOf)
	{
		if (!layui.notice)
			layui.layer.msg(lang('请输入有效地址！'));
		else
			layui.notice.error(lang('请输入有效地址！'));
		return -6;
	}

	if (!window.CS.toolbar || !window.CS.toolbar.indexOf)
		ajax.url = location.protocol + '//' + location.host + '/RCS.Core.Program/WpfToolBar.xaml?';//
	else if (window.CS.toolbar.indexOf('?') < 0)
		ajax.url = window.CS.toolbar + '?';
	else
		ajax.url = window.CS.toolbar + '&';
	ajax.url = ajax.url + 'ID=' + id +
		'&Url=' + encodeURIComponent(url) +
		'&UserID=' + window.login.ID +
		'&UserName=' + encodeURIComponent(window.login.Name) +
		'&UserAlive=' + window.login.Alive;
	if (!!ajax.json.SweepID)
		ajax.url = ajax.url + '&Master=' + ajax.json.SweepID;
	if (!!data)
	{
		if (typeof (data) != typeof (''))
		{
			for (var k in data)
			{
				if (!!k && !!k.toUpperCase)
					ajax.url = ajax.url + '&' + k + '=' + data[k];
			}
		}
		else
		{
			ajax.url = ajax.url + '&' + data;
		}
	}
	$.parseUrl(ajax.json.Data, ajax.json);
	data = ajax.json.AuthData;
	if (!!data)
	{
		if (data.indexOf('Work') >= 0)
		{
			ajax.url = ajax.url + '&AuthVideo=Work';
		}
		else if (data.indexOf('Part') >= 0)
		{
			if (!ajax.json.AreaDenial)
				ajax.url = ajax.url + '&AuthVideo=Work';
			else
				ajax.url = ajax.url + '&AuthVideo=Part&Denial=' + ajax.json.AreaDenial;
		}
		else
		{
			ajax.url = ajax.url + '&AuthVideo=View';
		}
		ajax.url = ajax.url + '&Auth=' + data;
	}
	ajax.url = ajax.url + '&_=' + ajax.now;
	if (!!window.Trace)
		window.Trace(i + '.OpenVideo = ' + ajax.url);
	if (!!console.log)
		console.log(i + '.OpenVideo = ' + ajax.url);
	if (!window.shell)
	{
		ajax.url = window.getSlaveUrl('OpenVideo.do?' + ajax.url);
		$('#frm-hide').attr('src', ajax.url);//$('#imgOpenVideo').attr('src', ajax.url);//$.ajax(ajax);//
		i = window.undefined;//#error
	}
	else
	{
		ajax.vs = window.Data('OpenVideo', ajax.url);
		console.log(ajax.vs);//OpenVideo=5,1416,DESKTOP-FN4LG6V&I1=P30356,H12365,1A4A937D295943E6B4203886CF4E9CFA,mc,10.171.120.143&I2=P61656,H5566&I3=P73364,H43234&I4=P40700,H33122&I5=P42176,H12090&Sn=1,2,3,4,5
		if (ajax.vs.indexOf('=' + id + ',') < 0)
			return;

		ajax.ps = $.parseUrl(ajax.vs);
		ajax.sns = ajax.ps.Sn.split(',');
		for (i = 0; i < ajax.sns.length; i++)
		{
			ajax.sn = ajax.sns[i];
			if (!ajax.sn)
				continue;

			ajax.sn = ajax.ps['I' + ajax.sn];
			if (!!ajax.sn && !!ajax.sn.indexOf && !ajax.sn.indexOf(id + ','))
				break;
		}
		if (i >= ajax.sns.length)
			return;

		ajax.sn = parseInt(ajax.sns[i]);
		i = ajax.sn;
	}
	window.openVideoInterval(null, setInterval, 1000);
	return 0;
};
window.openVideoInterval = function ()
{
	if (!arguments.length)
		return -1;

	var ovs = window.vdb();
	if (!window.Data)
		return;

	ovs.qs = window.Data('openVideo', '');//OpenVideo=5,1416,DESKTOP-FN4LG6V&I1=P30356,H12365,1A4A937D295943E6B4203886CF4E9CFA,mc,10.171.120.143&I2=P61656,H5566&I3=P73364,H43234&I4=P40700,H33122&I5=P42176,H12090&Sn=1,2,3,4,5
	if (!ovs.qs)
		return -2;

	ovs.ps = $.parseUrl(ovs.qs);
	if (!ovs.ps)
		return -3;

	var sends = 0;
	ovs.sns = ovs.ps.Sn.split(',');
	for (var i in ovs.sns)
	{
		if (isNaN(i))
			continue;

		var ajax = {};
		ajax.sn = ovs.sns[i];
		ajax.ts = ovs.ps['I' + ajax.sn];
		ajax.ts = ajax.ts.split(',');
		if (ajax.ts.length <= 2)//search(`&{id}={sn};`);
			continue;

		ajax.id = ajax.ts[0];
		ajax.json = window.ldb('#' + ajax.id);
		if (!ajax.json)
			continue;

		try
		{
			ajax.vj = $.eval(ajax.json);
		}
		catch (x)
		{
			console.error(x.message);
			continue;
		}

		ajax.pid = parseInt(ovs.ps['P' + ajax.sn]);
		ajax.hwnd = parseInt(ovs.ps['H' + ajax.sn]);
		ajax.work = parseInt(ovs.ps['W' + ajax.sn]);
		ajax.record = parseInt(ovs.ps['R' + ajax.sn]);
		ajax.last = parseInt(ovs.ps['L' + ajax.sn]);
		ajax.url = 'JobVideo?ID=' + ajax.id + '&User=' + ovs.sid;
		if (!!ajax.vj.SweepID)//有副屏
			ajax.id = ajax.vj.SweepID;
		if (!ajax.pid)
		{
			ajax.url = ajax.url + '&Sweep=Close';
		}
		else if (!!ajax.work)
		{
			if (!!window.RCS && !!window.RCS.ConfigData && !!window.RCS.ConfigData.WorkUltra)//忽略权限
				window.Data('openVideo', '?Sn=' + ajax.sn + '&Work=' + window.CS.ExtraData.STATUS_WORKING);
			ajax.url = ajax.url + '&Sweep=1';
		}
		else
		{
			ajax.url = ajax.url + '&Sweep=0';
		}
		if (!!ajax.record && !window.CS.localRecording)
			ajax.url = ajax.url + '&Snapshots=1';
		window.serverMessage.call(window, ajax.url);
		sends++;
	}//for i
	if (!sends)
	{
		if (!ovs.interval)
			return sends;

		clearInterval(ovs.interval);
		ovs.interval = 0;
		return sends;
	}

	if (window.webName != 'index')
		return sends;

	var l = window.RCS.ConfigData.IdleTimeout;
	if (!window.shell)
		l = Date.now() - window.lastInput - l;
	else
		l = window.Value('ProcessInput', -l);// is busy; gt(0) = set, eq(0) = get, eq(-1) = now - value, eq(-2) = now - value - timeout, lt(-2) = now - value + arg
	if (l > 0)
	{
		window.idle = 15 - Math.floor(l / 1000) % 16;
		window.Data('openVideo', '?Sn=&Idle=' + window.idle);
		console.log('?Sn=&Idle=', window.idle);
	}
	else if (!!window.idle)
	{
		window.idle = 0;
		window.Data('openVideo', '?Sn=&Idle=86400');
		console.log('?Sn=&Idle=86400');
	}
	if (!ovs.interval)
		ovs.interval = setInterval(arguments.callee, 1000, null, setInterval, 1000);
	return sends;
};
setTimeout(window.openVideoInterval, 10, null, setInterval, 1000);
window.doWork = function (data) { window.openVideoInterval(null, setInterval, 1000); };//controller callback

window.onLogoff = function ()
{
	if (!window.Data)
		return;

	t = window.Data('OpenVideo', '');
	if (!t)
		return 1;

	window.openVideos = $.parseUrl(t);
	if (!window.openVideos || !window.openVideos.Sn)
		return 2;

	window.openVideos.sns = window.openVideos.Sn.split(',');
	if (!window.openVideos.sns || !window.openVideos.sns.length)
		return 3;

	var ovs = window.vdb();
	ovs.db('', null, 'clear');//ov 清理 logoff
	return 0;
};
// 定时探测登录状态，是否被挤下线？
window.testLogin = function (data)
{
	if (!window.login || !window.login.ID)
		return;

	var now = Date.now();
	var msg = 'User?ID=' + window.login.ID;
	if (!window.shell)
	{
		if (now - window.lastInput < 10000)
			msg = msg + '&input=' + window.lastInput;
	}
	else
	{
		var i = window.Value('SessionInput', -10000);// is busy; gt(0) = set, eq(0) = get, eq(-1) = now - value, eq(-2) = now - value - timeout, lt(-2) = now - value + arg
		if (i < 0)
			msg = msg + '&input=' + i;
	}
	window.serverMessage(msg + '&_' + now);
};
setInterval(window.testLogin, 10000);//10s

// 定时检测更新，并自动下载更新
window.doUpgrade = function (data)
{
	if (!!window.Data)
		window.Data('Upgrade', window.location.href);
};
setInterval(window.doUpgrade, 90000);//1m30s

window.onReceiveGYF = function (data, json, msg, ws)//谷云飞(gyf) 警报 仿真
{
	if (ws != window.serverMsg)
		return;

	if (json.type == CS.ConfigData.TYPE_ALARM)
	{
		if (!!Monitor)
			Monitor.alarmOccur(json);
	}
	else if (json.type == "Status")
	{
		if (!!Monitor && !!Monitor.changedStatus)
			Monitor.changedStatus(json);
		if (!!window.layout && !!window.layout.changedColor)
			window.layout.changedColor(json);
	}
};

window.onUrlAuth = function (data, json, msg, ws)
{
	if (!!window.CS.ignoreAuthEdit || window.login.Role.indexOf(data.Domain) < 0 || (data.Path.indexOf('/User') != 0 && data.Path.indexOf('/' + window.login.ID) != 0))// !(inrole && (alluser || isme)) == !inrole || (!alluser && !isme)
		return;

	if (!!window.CS.isConfigUser())
	{
		data = lang('当前用户权限有改变，需要重新登录，管理员已忽略');
		if (!layui.notice)
			layui.layer.msg(data);
		else
			layui.notice.info(data);
		return;
	}

	layui.layer.open({
		content: lang('权限已修改，请重新登陆!'), end: function ()
		{
			window.onLogoff.call(window);
			window.location.href = 'Login.html';
		}, shadeClose: true
	});
};
window.onReceiveVideo = function (data, json, msg, ws)
{
	var ovs = window.openVideoState;
	if (!ovs || !window.openVideoInterval.call(this))
		return;

	var t = json['$:aim'];
	if (t == 'Sweep' || t == 'Sweeper')//Sweep://
	{
		t = json['SweepData'];
		if (!!t)
		{
			json.sweepData = new Uri(t);
			t = json.sweepData.Path;
			if (!!t)
			{
				t = lang('机台 ') + json.Name;
				if (!json.sweepData.Search)
					t += lang(' 用户已全部退出。');
				else
					t += lang(' 有用户进出。');
				if (!layui.notice)
					layui.layer.msg(t);
				else
					layui.notice.info(t);
			}
			window.onMessageUrl.call(json, json.sweepData);
		}
	}
};
window.onMessageText = function (data, msg, ws)
{
	return false;
};
window.onMessageJson = function (data, json, msg, ws)
{
	return false;
};
window.onMessageUrl = function (url, msg, ws)
{
	if (!url || !url.Href)
		return;

	url.cb = window['onUrl' + url.Scheme];
	if (!url.cb)
	{
		url.cb = window['onUrl' + url.protocol];
		if (!url.cb)
		{
			url.cb = window['onUrl' + url.PROTOCOL];
			if (!url.cb)
				url.cb = window['onUrl' + url.scheme.substring(0, 1).toUpperCase() + url.scheme.substring(1, url.scheme.length)];
		}
	}
	if (!!window.matrix && !!window.matrix.onMessageUrl && !!window.matrix.onMessageUrl.call)
		window.matrix.onMessageUrl(url.Host, url.Path, url.Search, url.scheme, url.Href);//sweep,pilot,lock
	if (!!url.cb && !!url.cb.call && !!url.cb(url, msg, ws))
		return true;

	if (url.scheme == 'login')//"Login://"
	{
		if (url.Host != window.login.ID)
		{
			var i = url.Path.indexOf('/');
			if (i > 0)
			{
				url.Path = lang('用户 ') + url.Path.substring(i + 1, url.Path.length) + lang(' 已登录。');
				if (!layui.notice)
					layui.layer.msg(url.Path);
				else
					layui.notice.info(url.Path);
			}
			return true;
		}

		if (!window.login.Alive || url.Path.indexOf(window.login.Alive) > 0)
			return true;

		layui.layer.open({
			content: lang('您的账号已在别处登录，当前会话被迫中断，如果这不是您的授权操作，请立即联系管理员修改密码或冻结账户。'), end: function ()
			{
				window.top.matrixSN = Date.now();
				if (!!window.matrix && !window.matrix.top.matrixSN)
				{
					try
					{
						window.matrix.window.Data('close', '');
					}
					catch (x) { }
				}

				window.onLogoff.call(window);
				window.location.href = location.href.replace(/index\.html/img, 'Login.html');
			}, shadeClose: true
		});
		window.login = {};
		window.serverMsg.close();
		return true;
	}

	if (url.scheme == 'sweep')// "Sweep://vid"
	{
		window.Data('openVideo', '?Sn=0&SweepData=' + encodeURIComponent(url));
		return true;
	}

	if (url.scheme == 'pilot')// "Pilot://Authority/Pilot" // "Pilot://VID/Pilot"
	{
		window.Data('openVideo', '?Sn=0&PilotData=' + encodeURIComponent(url));
		return true;
	}

	return false;
};

if (!!window.WebSocket)
{
	window.onMessageOpen = function (e)
	{
		if (e.target.url.indexOf('') > 0)
			e.target.send('topic=,Client,');
		else if (!!window.topic)
			e.target.send(window.topic);
		console.log(e.target.url + ' message websocket open');
		return 0;
	};
	window.onMessageError = function (e)
	{
		console.log(e.target.url + ' message websocket error');
		return 0;
	};
	window.onMessageClose = function (e)
	{
		if (!window.login || !window.login.ID)
			return -10;

		var ws = e.target;
		if (ws == window.serverMsg || ws == 'Server' || e == 'Server')
		{
			console.log('server message websocket close');
			ws = 'ws://' + window.location.host + '/Message.do?Server';
			if (!!window.WebSocket)
				ws = new WebSocket(ws);
			else if (!!window.webSocket)
				ws = new webSocket(ws);
			else if (!!window.websocket)
				ws = new websocket(ws);
			window.serverMsg = ws;
		}
		else
		{
			console.log('local message websocket close');
			ws = window.getSlaveUrl('Message.do?Client');
			ws = 'ws' + ws.substring(ws.indexOf('://'), ws.length);
			if (!!window.WebSocket)
				ws = new WebSocket(ws);
			else if (!!window.webSocket)
				ws = new webSocket(ws);
			else if (!!window.websocket)
				ws = new websocket(ws);
			window.localMsg = ws;
		}
		ws.onopen = window.onMessageOpen
		ws.onmessage = window.onMessageReceive;
		ws.onerror = window.onMessageError
		ws.onclose = window.onMessageClose;
		return 0;
	};
	window.onMessageReceive = function (e)
	{
		var t = e.data;
		if (!t)
		{
			if (!e)
				return -1;

			if (this.readyState != WebSocket.OPEN)
				return -2;

			try
			{
				this.send(e);
				return 0;
			}
			catch (x) { }
			return -128;
		}

		var ws = e.target;
		if (t == 'Unauthorized')
		{
			window.onLogoff.call(window);
			window.location.href = location.href.replace(/index\.html/img, 'Login.html');
			window.login = {};
			ws.close();
		}
		else if (t.indexOf('{') == 0 && t.lastIndexOf('}') == t.length - 1)
		{
			var v = $.eval(t);
			t = v['$:type'];
			if (!!t)
			{
				var f = window['onReceive' + t];
				if (!!f && !!f.call)
					f(e.data, v, e, ws);
			}
			else
			{
				if (!window.onMessageJson.call(ws, e.data, v, e, ws))
					window.onMessageText.call(ws, e.data, e, ws);
			}
		}
		else if (/^[a-zA-Z0-9-]+:\/\//.test(t))
		{
			if (!window.onMessageUrl.call(ws, new Uri(t), e, ws))
				window.onMessageText.call(ws, t, e, ws);
		}
		else
		{
			var v = t.indexOf('-LastInput-');
			if (v > 0)//SessionInput + "-LastInput-" + ProcessInput //123456789-LastInput-123456789
				window.lastInput = parseFloat(t.substring(0, v));
			else
				window.onMessageText.call(ws, e.data, e, ws);
		}
		if (!!window.Trace)
			window.Trace('message websocket message ' + e.data, -1);
		return 0;
	};
	window.onMessageClose.call(window, 'Server');
	window.onMessageClose.call(window, 'Client');
	window.localMessage = function (data)
	{
		return window.onMessageReceive.call(window.localMsg, data);
	};
	window.serverMessage = function (data)
	{
		return window.onMessageReceive.call(window.serverMsg, data);
	};
}
else
{
	window.localMessage = function ()
	{
		return 0;
	}
	window.serverMessage = function ()
	{
		return 0;
	}
}
if (!!window.Data && !!window.login)
	window.Data('User', 'ID=' + window.login.ID + '&Name=' + window.login.Name + '&Alive=' + window.login.Alive);
