/***
** <PERSON><PERSON><PERSON><PERSON>
** <EMAIL>
** 2018-08-14 15:35:00.00000
** text/javascript
***/

(function ($)
{
	if (!!window.pickGold && window.pickGold.jQuery)
		return;

	window.pickGold = function () { };
	window.pickGold.$ = $;
	window.pickGold.jQuery = window.jQuery;
	window.pickGold.ctor = arguments.callee;

	function DF(format)//SimpleDateFormat
	{
		this.input = format;
		this.Y = 0;
		this.y = 0;
		this.m = 0;
		this.d = 0;
		this.H = 0;
		this.h = 0;
		this.n = 0;
		this.s = 0;
		this.f = 0;
		this.w = 0;
		this.z = 0;
	}

	DF.mmm = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
	DF.prototype.mmm = DF.mmm;
	DF.mmmm = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
	DF.prototype.mmmm = DF.mmmm;
	DF.T = [String.fromCharCode(0x3007), String.fromCharCode(0x4E00), String.fromCharCode(0x4E8C), String.fromCharCode(0x4E09), String.fromCharCode(0x56DB), String.fromCharCode(0x4E94), String.fromCharCode(0x516D), String.fromCharCode(0x4E03), String.fromCharCode(0x516B), String.fromCharCode(0x4E5D), String.fromCharCode(0x5341)]; // 〇一二三四五六七八九十
	DF.prototype.T = DF.T;
	DF.W = [String.fromCharCode(0x65E5), DF.T[1], DF.T[2], DF.T[3], DF.T[4], DF.T[5], DF.T[6], DF.T[7], DF.T[8], DF.T[9]]; // 日一二三四五六七八九十
	DF.prototype.W = DF.W;
	DF.WW = ['Sun', 'Mon', 'Tue', 'Wed', 'Thr', 'Fri', 'Sat'];
	DF.prototype.WW = DF.WW;
	DF.WWW = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
	DF.prototype.WWW = DF.WWW;

	DF.prototype._YYYY = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.T[Math.floor(this.Y / 1000)] + this.T[Math.floor((this.Y % 1000) / 100)] + this.T[Math.floor((this.Y % 100) / 10)] + this.T[this.Y % 10];
	};

	DF.prototype._YY = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.T[Math.floor((this.y % 100) / 10)] + this.T[this.y % 10];
	};

	DF.prototype._Y = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (y > 10 ? T[Math.floor((y % 100) / 10)] : '') + T[y % 10];
	};

	DF.prototype.__Y = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 4)
			return this._YYYY(match);

		if (match.length >= 2)
			return this._YY(match);

		return this._Y(match);
	};

	DF.prototype._yyyy = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.Y;
	};

	DF.prototype._yy = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.y > 9 ? '' : '0') + this.y;
	};

	DF.prototype._y = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.y;
	};

	DF.prototype.__y = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 4)
			return this._yyyy(match);

		if (match.length >= 2)
			return this._yy(match);

		return this._y(match);
	};

	DF.prototype._M = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.m >= 10 ? this.T[10] : '') + (this.m % 10 > 0 ? this.T[this.m % 10] : '');
	};

	DF.prototype.__M = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this._M(match);
	};

	DF.prototype._mmmm = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.mmmm[this.m];
	};

	DF.prototype._mmm = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.mmm[this.m];
	};

	DF.prototype._mm = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.m > 9 ? '' : '0') + this.m;
	};

	DF.prototype._m = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.m;
	};

	DF.prototype.__m = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 4)
			return this._mmmm(match);

		if (match.length >= 3)
			return this._mmm(match);

		if (match.length >= 2)
			return this._mm(match);

		return this._m(match);
	};

	DF.prototype._D = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.d >= 30 ? (this.T[3] + this.T[10]) : this.d >= 20 ? (this.T[2] + this.T[10]) : this.d >= 10 ? this.T[10] : '') + (this.d % 10 > 0 ? this.T[this.d % 10] : '');
	};

	DF.prototype.__D = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this._D(match);
	};

	DF.prototype._dd = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.d > 9 ? '' : '0') + this.d;
	};

	DF.prototype._d = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.d;
	};

	DF.prototype.__d = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._dd(match);

		return this._d(match);
	};

	DF.prototype._HH = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.H > 9 ? '' : '0') + this.H;
	};

	DF.prototype._H = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.H;
	};

	DF.prototype.__H = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._HH(match);

		return this._H(match);
	};

	DF.prototype._hh = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return (this.h > 9 ? '' : '0') + this.h;
	};

	DF.prototype._h = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.h;
	};

	DF.prototype.__h = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._hh(match);

		return this._h(match);
	};

	DF.prototype._nn = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.n > 9 ? '' : '0') + this.n;
	};

	DF.prototype._n = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.n;
	};

	DF.prototype.__n = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._nn(match);

		return this._n(match);
	};

	DF.prototype._ss = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return (this.s > 9 ? '' : '0') + this.s;
	};

	DF.prototype._s = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this.s;
	};

	DF.prototype.__s = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._ss(match);

		return this._s(match);
	};

	DF.prototype._f = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (!match || !match.length)
			return this.f;

		var v = this.f + '';
		return v.substring(0, match.length);
	};

	DF.prototype.__f = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		return this._f(match);
	};

	DF.prototype._WW = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.WWW[this.w];
	};

	DF.prototype._W = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.W[this.w];
	};

	DF.prototype.__W = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._WW(match);

		return this._W(match);
	};

	DF.prototype._ww = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.WW[this.w];
	};

	DF.prototype._w = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.w;
	};

	DF.prototype.__w = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._ww(match);

		return this._w(match);
	};

	DF.prototype._TT = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return String.fromCharCode(this.H >= 12 ? 19979 : 19978);
	};

	DF.prototype._T = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.H >= 12 ? 'P' : 'A';
	};

	DF.prototype.__T = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match.substring(1, match.length);

		if (match.length >= 2)
			return this._TT(match);

		return this._T(match);
	};

	DF.prototype._t = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.H >= 12 ? 'p' : 'a';
	};

	DF.prototype.__t = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this._t(match);
	};

	DF.prototype._ZZ = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return -this.z / 60;
	};

	DF.prototype._Z = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.z / 60;
	};

	DF.prototype.__Z = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		if (match.length >= 2)
			return this._ZZ(match);

		return this._Z(match);
	};

	DF.prototype._zz = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return - this.z;
	};

	DF.prototype._z = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		return this.z;
	};

	DF.prototype.__z = function (match)
	{
		if (!!match && match.substring(0, 1) == '\\')
			return match;

		if (match.length >= 2)
			return this._zz(match);

		return this._z(match);
	};

	DF.prototype.format = function (input)
	{
		var me = this;
		var date = input;
		format = me.input;
		if (!format || format == '')//var df = new SimpleDateFormat();
			format = 'yyyy-mm-dd'; // return this.getFullYear() + '-' + (this.getMonth() + 1) + '-' + this.getDate();

		if (format == 'G')
			return date.toGMTString();

		if (format == 'U')
			return date.toUTCString();

		if (format == 'UT')
			return date.toUTCTimeString();

		if (format == 'T')
			return date.getTime();

		me.Y = date.getFullYear();
		me.y = date.getYear() % 100;
		me.m = date.getMonth() + 1;
		me.d = date.getDate();
		me.H = date.getHours();
		me.h = me.H % 12 == 0 ? me.H : me.H % 12;
		me.n = date.getMinutes();
		me.s = date.getSeconds();
		me.f = date.getMilliseconds();
		me.w = date.getDay();
		me.z = date.getTimezoneOffset();
		format = format.replace(/\\?T+/mg, function () { return me.__T.apply(me, arguments); }); // 大写英文上下午：A
		//format = format.replace(/\\?TT+/img, function () { return me._TT.apply(me, arguments); }); // 中文上下午：上
		//format = format.replace(/\\?T+/mg, function () { return me._T.apply(me, arguments); }); // 大写英文上下午：A
		format = format.replace(/\\?t+/mg, function () { return me.__t.apply(me, arguments); }); // 小写英文上下午：a
		//format = format.replace(/\\?t+/mg, function () { return me._t.apply(me, arguments); }); // 小写英文上下午：a
		format = format.replace(/\\?Z+/mg, function () { return me.__Z.apply(me, arguments); }); // 时区：8
		//format = format.replace(/\\?ZZ+/mg, function () { return me._ZZ.apply(me, arguments); }); // 时区：8
		//format = format.replace(/\\?Z+/mg, function () { return me._Z.apply(me, arguments); }); // 时区：8
		format = format.replace(/\\?z+/mg, function () { return me.__z.apply(me, arguments); }); // 时区：480
		//format = format.replace(/\\?zz+/mg, function () { return me._zz.apply(me, arguments); }); // 时区：480
		//format = format.replace(/\\?z+/mg, function () { return me._z.apply(me, arguments); }); // 时区：480
		format = format.replace(/\\?f+/img, function () { return me.__f.apply(me, arguments); }); // 毫秒
		//format = format.replace(/\\?f+/img, function () { return me._f.apply(me, arguments); }); // 毫秒
		format = format.replace(/\\?s+/img, function () { return me.__s.apply(me, arguments); }); // 独数秒：01
		//format = format.replace(/\\?ss+/img, function () { return me._ss.apply(me, arguments); }); // 双数秒：01
		//format = format.replace(/\\?s+/img, function () { return me._s.apply(me, arguments); }); // 独数秒：01
		format = format.replace(/\\?n+/img, function () { return me.__n.apply(me, arguments); }); // 独数分钟：01
		//format = format.replace(/\\?nn+/img, function () { return me._nn.apply(me, arguments); }); // 双数分钟：01
		//format = format.replace(/\\?n+/img, function () { return me._n.apply(me, arguments); }); // 独数分钟：01
		format = format.replace(/\\?H+/mg, function () { return me.__H.apply(me, arguments); }); // 24小时制独数小时：1
		//format = format.replace(/\\?HH+/mg, function () { return me._HH.apply(me, arguments); }); // 24小时制双数小时：13
		//format = format.replace(/\\?H+/mg, function () { return me._H.apply(me, arguments); }); // 24小时制独数小时：1
		format = format.replace(/\\?h+/mg, function () { return me.__h.apply(me, arguments); }); // 12小时制独数小时：1
		//format = format.replace(/\\?hh+/mg, function () { return me._hh.apply(me, arguments); }); // 12小时制双数小时：1
		//format = format.replace(/\\?h+/mg, function () { return me._h.apply(me, arguments); }); // 12小时制独数小时：1
		format = format.replace(/\\?D+/mg, function () { return me.__D.apply(me, arguments); }); // 大写日期：二十五
		//format = format.replace(/\\?D+/mg, function () { return me._D.apply(me, arguments); }); // 大写日期：二十五
		format = format.replace(/\\?d+/mg, function () { return me.__d.apply(me, arguments); }); // 数字双日期：01
		//format = format.replace(/\\?dd+/mg, function () { return me._dd.apply(me, arguments); }); // 数字双日期：01
		//format = format.replace(/\\?d+/mg, function () { return me._d.apply(me, arguments); }); // 数字独日期：1
		format = format.replace(/\\?M+/mg, function () { return me.__M.apply(me, arguments); }); // 大写月：十二
		//format = format.replace(/\\?M+/mg, function () { return me._M.apply(me, arguments); }); // 大写月：十二
		format = format.replace(/\\?m+/mg, function () { return me.__m.apply(me, arguments); }); // 英文全名月：January
		//format = format.replace(/\\?mmmm+/mg, function () { return me._mmmm.apply(me, arguments); }); // 英文全名月：January
		//format = format.replace(/\\?mmm+/mg, function () { return me._mmm.apply(me, arguments); }); // 英文半名月： Jan
		//format = format.replace(/\\?mm+/mg, function () { return me._mm.apply(me, arguments); }); // 数字双月：01
		//format = format.replace(/\\?m+/mg, function () { return me._m.apply(me, arguments); }); // 数字独月：1
		format = format.replace(/\\?Y+/mg, function () { return me.__Y.apply(me, arguments); }); // 大写全年：一九八一
		//format = format.replace(/\\?YYYY+/mg, function () { return me._YYYY.apply(me, arguments); }); // 大写全年：一九八一
		//format = format.replace(/\\?YY+/mg, function () { return me._YY.apply(me, arguments); }); // 大写双年：〇一
		//format = format.replace(/\\?Y+/mg, function () { return me._Y.apply(me, arguments); }); // 大写独年：一
		format = format.replace(/\\?y+/mg, function () { return me.__y.apply(me, arguments); }); // 数字全年：1981
		//format = format.replace(/\\?yyyy+/mg, function () { return me._yyyy.apply(me, arguments); }); // 数字全年：1981
		//format = format.replace(/\\?yy+/mg, function () { return me._yy.apply(me, arguments); }); // 数字双年：01
		//format = format.replace(/\\?y+/mg, function () { return me._y.apply(me, arguments); }); // 数字独年：1
		format = format.replace(/\\?W+/mg, function () { return me.__W.apply(me, arguments); }); // 中文周：日
		//format = format.replace(/\\?WW+/mg, function () { return me._WW.apply(me, arguments); }); // 英文全周：Sunday
		//format = format.replace(/\\?W+/mg, function () { return me._W.apply(me, arguments); }); // 中文周：日
		format = format.replace(/\\?w+/mg, function () { return me.__w.apply(me, arguments); }); // 数字周：0
		//format = format.replace(/\\?ww+/mg, function () { return me._ww.apply(me, arguments); }); // 英文半周：Sun
		//format = format.replace(/\\?w+/mg, function () { return me._w.apply(me, arguments); }); // 数字周：0
		return format;
	};

	SimpleDateFormat = DF;
	Date.SimpleDateFormat = DF;
	window.SimpleDateFormat = DF;
	document.SimpleDateFormat = DF;
	window.pickGold.DF = DF;

	window.pickGold.dateToUTC = function ()
	{
		return new Date(this.getTime() + this.getTimezoneOffset() * 60 * 1000); // cst = -480
	};
	Date.prototype.pgToUTC = window.pickGold.dateToUTC;
	if (!Date.prototype._pgToUTC)
		Date.prototype.toUTC = window.pickGold.dateToUTC;

	window.pickGold.dateToLocal = function ()
	{
		return new Date(this.getTime() - this.getTimezoneOffset() * 60 * 1000); // cst = -480
	};
	Date.prototype.pgToLocal = window.pickGold.dateToLocal;
	if (!Date.prototype._pgToLocal)
		Date.prototype.toLocal = window.pickGold.dateToLocal;

	window.pickGold.dateToUTCTimeString = function ()
	{
		return new Date(this.getTime() + this.getTimezoneOffset() * 60 * 1000).toLocaleTimeString(); // cst = -480
	};
	Date.prototype.pgToUTCTimeString = window.pickGold.dateToUTCTimeString;
	if (!Date.prototype._pgToUTCTimeString)
		Date.prototype.toUTCTimeString = window.pickGold.dateToUTCTimeString;

	window.pickGold.dateToDateString = function (format)
	{
		var df = new window.pickGold.DF(format);
		return df.format(this);
	};
	Date.prototype.pgToDateString = window.pickGold.dateToDateString;
	if (!Date.prototype._pgToDateString)
		Date.prototype.toDateString = window.pickGold.dateToDateString;

	window.pickGold.dateToString0 = Date.prototype.toString;
	window.pickGold.dateToString = function (format)
	{
		if (!arguments.length)
			return window.pickGold.dateToString0.call(this);

		if (!format || format == '')
			format = 'yyyy-mm-dd HH:nn:ss';
		return window.pickGold.dateToDateString.call(this, format);
	};
	Date.prototype.pgToString = window.pickGold.dateToString;
	if (!Date.prototype._pgToString)
		Date.prototype.toString = window.pickGold.dateToString;

	window.pickGold.numberBitOP = function (l, r, o)
	{
		if (arguments.length < 3)
		{
			o = r;
			r = l;
			l = this;
		}
		l = parseFloat(l);
		r = parseFloat(r);
		if (o == 0)//xor
		{
			if (isNaN(l) && isNaN(r))
				return 0;

			if (isNaN(l))
				return r;

			if (isNaN(r))
				return l;
		}
		else if (o > 0)//or
		{
			if (isNaN(l))
				return r;

			if (isNaN(r))
				return l;
		}
		else//and
		{
			if (isNaN(l) || isNaN(r))
				return 0;
		}
		var v = parseFloat(1);
		var bs = [v];
		for (var i = 0; i < 64; i++)
		{
			v *= parseFloat(2);
			bs.push(v);
		}
		v = parseFloat(0);
		for (i = bs.length - 1; i >= 0; i--)
		{
			if (o == 0)//xor
			{
				if ((l >= bs[i] && r < bs[i]) || (l < bs[i] && r >= bs[i]))
					v += bs[i];
			}
			else if (o > 0)//or
			{
				if (l >= bs[i] || r >= bs[i])
					v += bs[i];
			}
			else//and
			{
				if (l >= bs[i] && r >= bs[i])
					v += bs[i];
			}
			if (l >= bs[i])
				l -= bs[i];
			if (r >= bs[i])
				r -= bs[i];
		}
		return v;
	};
	Number.prototype.pgBitOP = window.pickGold.numberBitOP;
	Number.prototype.bitOP = window.pickGold.numberBitOP;

	window.pickGold.numberAnd = function (v)
	{
		return window.pickGold.numberBitOP.call(this, v, -1);
	};
	Number.prototype.pgAnd = window.pickGold.numberAnd;
	Number.prototype.and = window.pickGold.numberAnd;

	window.pickGold.numberOr = function (v)
	{
		return window.pickGold.numberBitOP.call(this, v, 1);
	};
	Number.prototype.pgOr = window.pickGold.numberOr;
	Number.prototype.or = window.pickGold.numberOr;

	window.pickGold.numberXor = function (v)
	{
		return window.pickGold.numberBitOP.call(this, v, 0);
	};
	Number.prototype.pgXor = window.pickGold.numberXor;
	Number.prototype.xor = window.pickGold.numberXor;

	window.pickGold.numberToRmbString = function ()
	{
		var c =
		{
			n: [38646, 22777, 36144, 21441, 32902, 20237, 38470, 26578, 25420, 29590, 25972, 36000], // "零壹贰叁肆伍陆柒捌玖整負";
			u: [20191, 20336, 25342, 20140, 20191, 20336, 25342, 21513, 20191, 20336, 25342, 20806, 20191, 20336, 25342, 20740, 20191, 20336, 25342, 33836, 20191, 20336, 25342, 22278, 35282, 20998, 21400, 27627], // "仟佰拾京仟佰拾吉仟佰拾兆仟佰拾億仟佰拾萬仟佰拾圆角分厘毫";//"仟佰拾京仟佰拾吉仟佰拾兆仟佰拾亿仟佰拾万仟佰拾圆角分厘毫"
			m: this
		};
		if (c.m < 0)
			c.m *= -1;
		for (var i = c.n.length - 1; i >= 0; i--)
		{
			c.t = c.n[i];
			c[c.t] = String.fromCharCode(c.t);
			c.n[i] = c[c.t];
		}
		for (var i = c.u.length - 1; i >= 0; i--)
		{
			c.t = c.u[i];
			c[c.t] = String.fromCharCode(c.t);
			c.u[i] = c[c.t];
		}
		c.t = c.m.toFixed(4).replace('.', '').replace(/^0+([0-9])/img, '$1');
		if (c.t == "0")
			return c[38646] + c[22278] + c[25972]; // "零圆整";

		if (c.t.length > c.u.length)
			throw { message: 'the input value too max.' };

		c.u = c.u.splice(c.u.length - c.t.length, c.t.length);
		for (var i = c.t.length - 1; i >= 0; i--)
			c.t = c.t.substring(0, i) + c.n[String.charCodeAt(c.t.substring(i, i + 1)) - String.charCodeAt('0')] + c.u[i] + c.t.substring(i + 1, c.t.length);
		c.m = new RegExp(c[38646] + '+[^' + c[22278] + c[33836] + c[20740] + c[20806] + c[21513] + c[20140] + ']', 'img');
		c.t = c.t.replace(c.m, c[38646]); // /零+[^圆萬億兆吉京]/img, '零' //t = "壹仟零佰零拾零京叁仟零佰伍拾贰吉零仟零佰零拾零兆叁仟伍佰贰拾零億肆仟零佰贰拾零萬零仟零佰零拾伍圆壹角零分零厘伍毫";
		c.m = new RegExp(c[38646] + '{2,}', 'img');
		c.t = c.t.replace(c.m, c[38646]); // /零{2,}/img, '零' //t = "壹仟零零零京叁仟零伍拾贰吉零零零零兆叁仟伍佰贰拾零億肆仟零贰拾零萬零零零伍圆壹角零零伍毫"
		c.m = new RegExp(c[38646] + '+([' + c[22278] + c[33836] + c[20740] + c[20806] + c[21513] + c[20140] + '])', 'img');
		c.t = c.t.replace(c.m, '$1'); // /零+([圆萬億兆吉京])/img, '' //t = "壹仟零京叁仟零伍拾贰吉零兆叁仟伍佰贰拾零億肆仟零贰拾零萬零伍圆壹角零伍毫"
		c.m = new RegExp('([' + c[33836] + c[20740] + c[20806] + c[21513] + c[20140] + '])[' + c[33836] + c[20740] + c[20806] + c[21513] + c[20140] + ']+', 'img');
		c.t = c.t.replace(c.m, '$1'); // /([萬億兆吉京])[萬億兆吉京]+/img, '$1' //t = "壹仟京叁仟零伍拾贰吉兆叁仟伍佰贰拾億肆仟零贰拾萬零伍圆壹角零伍毫"
		c.m = new RegExp('^[' + c[33836] + c[20740] + c[20806] + c[21513] + c[20140] + ']+', 'img');
		c.t = c.t.replace(c.m, ''); // /^[萬億兆吉京]+/img, '' //"壹仟京叁仟零伍拾贰吉叁仟伍佰贰拾億肆仟零贰拾萬零伍圆壹角零伍毫"
		if (c.t.substring(0, 1) == c[38646])// 零
			c.t = c.t.substring(1, c.t.length);
		if (c.t.substring(c.t.length - 1, c.t.length) == c[38646])// 零
			c.t = c.t.substring(0, c.t.length - 1);
		if (c.t.substring(c.t.length - 1, c.t.length) == c[22278])// 圆
			c.t = c.t + c[25972]; // "整"
		if (this >= 0)
			return c.t;

		return c[36000] + c.t; // 负 負
	};
	Number.prototype.pgToRmbString = window.pickGold.numberToRmbString;
	Number.prototype.toRmbString = window.pickGold.numberToRmbString;

	window.pickGold.numberToRmb = function ()
	{
		return window.pickGold.numberToRmbString.call(this);
	};
	Number.prototype.pgToRmb = window.pickGold.numberToRmb;
	Number.prototype.toRmb = window.pickGold.numberToRmb;

	window.pickGold.numberRmb = function ()
	{
		return window.pickGold.numberToRmbString.call(this);
	};
	Number.prototype.pgRmb = window.pickGold.numberRmb;
	Number.prototype.rmb = window.pickGold.numberRmb;

	window.pickGold.arrayIndexOf = function (item, start, end, error)
	{
		if (!start || start < 0)
			start = 0;
		if (!end || end < start || end >= this.length)
			end = this.length - 1;
		for (var i = start; i <= end; i++)
		{
			if (typeof (item) == typeof (this.reverse))
			{
				if (!!item(this[i], i, this))
					return i;

				continue;
			}

			if (this[i] == item)
				return i;
		}
		if (isNaN(error))
			error = -1;
		return error;
	};
	Array.prototype.pgIndexOf = window.pickGold.arrayIndexOf;
	if (!Array.prototype._pgIndexOf)
		Array.prototype.indexOf = window.pickGold.arrayIndexOf;

	window.pickGold.arrayLastIndexOf = function (item, start, end, error)
	{
		if (!start || start < 0)
			start = 0;
		if (!end || end < start || end >= this.length)
			end = this.length - 1;
		for (var i = end; i >= start; i--)
		{
			if (typeof (item) == typeof (this.reverse))
			{
				if (!!item(this[i], i, this))
					return i;

				continue;
			}

			if (this[i] == item)
				return i;
		}
		if (isNaN(error))
			error = -1;
		return error;
	};
	Array.prototype.pgLastIndexOf = window.pickGold.arrayLastIndexOf;
	if (!Array.prototype._pgLastIndexOf)
		Array.prototype.lastIndexOf = window.pickGold.arrayLastIndexOf;

	window.pickGold.eval = function (replace, error)
	{
		var t = this;
		if (arguments.length == 0)
			replace = true;
		t = t.replace(/^[\r\n\t\s ]*\/\/.*$/img, '');
		t = t.replace(/^[\r\n\t\s ]*<!--.*$/img, '');
		t = t.replace(/;[\r\n\t\s ]*$/img, '');
		if (arguments.length == 0 || arguments.length == 1)
		{
			if (!!replace)
			{
				if (typeof (replace) == typeof (arguments.callee))
					t = replace(t);
				else
					t = t.replace(/([^a-zA-Z0-9])null([^a-zA-Z0-9])/mg, '$1$.eval()$2');
			}
			t = window.eval('(' + t + ')');
			return t;
		}

		try
		{
			if (!!replace)
			{
				if (typeof (replace) == typeof (arguments.callee))
					t = replace(t);
				else
					t = t.replace(/([^a-zA-Z0-9])null([^a-zA-Z0-9])/mg, '$1$.eval()$2');
			}
			t = window.eval('(' + t + ')');
			return t;
		}
		catch (x)
		{
			if (typeof (error) == typeof (arguments.callee))
			{
				error(x)
				return;
			}
		}
		return error;
	};
	String.prototype.pgEval = window.pickGold.eval;
	if (!String.prototype._pgEval)
		String.prototype.eval = window.pickGold.eval;

	window.pickGold.parseDate = function (value)
	{
		var v = this;
		if (!value)
			value = NaN;
		if (!/([0-9]+)([^0-9]+0*([0-1]?[0-9])([^0-9]+0*([0-5]?[0-9]))?)?/img.exec(v))
			return value;

		var y = parseInt(RegExp.$1);
		if (isNaN(y))
			return value;

		var m = parseInt(RegExp.$3);
		if (isNaN(m))
			return value;

		var d = parseInt(RegExp.$5);
		if (isNaN(d))
			return value;

		y = '0000' + y;
		y = y.substring(y.length - 4, y.length);
		v = m + '/' + d + '/' + y + ' UTC'
		v = Date.parse(v);
		if (isNaN(v))
			return value;

		return new Date(v);
	};
	String.prototype.pgParseDate = window.pickGold.parseDate;
	if (!String.prototype._pgParseDate)
		String.prototype.parseDate = window.pickGold.parseDate;

	window.pickGold.parseTime = function (value)
	{
		var v = this;
		if (!value)
			value = NaN;
		if (!/([0-2]?[0-9])([^0-9]+0*([0-5]?[0-9])([^0-9]+0*([0-5]?[0-9]))?)?/img.exec(v))
			return value;

		v = '00' + RegExp.$5;
		v = v.substring(v.length - 2, v.length);
		v = '00' + RegExp.$3 + ':' + v;
		v = v.substring(v.length - 5, v.length);
		v = '00' + RegExp.$1 + ':' + v;
		v = v.substring(v.length - 8, v.length);
		v = Date.parse('1/1/1970 ' + v + ' UTC');
		if (isNaN(v))
			return value;

		return new Date(v);
	};
	String.prototype.pgParseTime = window.pickGold.parseTime;
	if (!String.prototype._pgParseTime)
		String.prototype.parseTime = window.pickGold.parseTime;

	window.pickGold.toDate = function (value)
	{
		return window.pickGold.parseDate.call(this, value);
	};
	String.prototype.pgToDate = window.pickGold.toDate;
	if (!String.prototype._pgToDate)
		String.prototype.toDate = window.pickGold.toDate;

	window.pickGold.toTime = function (value)
	{
		return window.pickGold.parseTime.call(this, value);
	};
	String.prototype.pgToTime = window.pickGold.toTime;
	if (!String.prototype._pgToTime)
		String.prototype.toTime = window.pickGold.toTime;

	window.pickGold.toUpper = function ()
	{
		return this.toUpperCase();
	};
	String.prototype.pgToUpper = window.pickGold.toUpper;
	if (String.prototype._pgToUpper)
		String.prototype.toUpper = window.pickGold.toUpper;

	window.pickGold.toLower = function ()
	{
		return this.toLowerCase();
	};
	String.prototype.pgToLower = window.pickGold.toLower;
	if (!String.prototype._pgToLower)
		String.prototype.toLower = window.pickGold.toLower;

	window.pickGold.upper = function ()
	{
		return this.toUpperCase();
	};
	String.prototype.pgUpper = window.pickGold.upper;
	if (!String.prototype._pgUpper)
		String.prototype.upper = window.pickGold.upper;

	window.pickGold.lower = function ()
	{
		return this.toLowerCase();
	};
	String.prototype.pgLower = window.pickGold.lower;
	if (!String.prototype._pgLower)
		String.prototype.lower = window.pickGold.lower;

	window.pickGold.trim = function ()
	{
		return this.replace(/^[\r\n\t\s ]+|[\r\n\t\s ]+$/img, '');
	};
	String.prototype.pgTrim = window.pickGold.trim;
	if (!String.prototype._pgTrim)
		String.prototype.trim = window.pickGold.trim;

	if (!String.prototype.charCodeAt && !!String.charCodeAt)
	{
		window.pickGold.charCodeAt = function (i)
		{
			return String.charCodeAt(this, i);
		};
		String.prototype.charCodeAt = window.pickGold.charCodeAt;
	}
	else if (!String.charCodeAt && !!String.prototype.charCodeAt)
	{
		window.pickGold.charCodeAt = function (t, i)
		{
			if (!t)
				return String.undefined;

			return t.charCodeAt(i);
		};
		String.charCodeAt = window.pickGold.charCodeAt;
	}

	var extend = {};

	window.pickGold.getDataTableJson = function (cs, rs)
	{
		var js = this;
		if (arguments.length == 0)
		{
			rs = js.Rows || js.rows;
			cs = js.Columns || js.columns;
		}
		else if (arguments.length == 1)
		{
			js = arguments[0];
			rs = js.Rows || js.rows;
			cs = js.Columns || js.columns;
		}
		else
		{
			js = null;
		}
		if (!cs || !rs || !cs.length || !rs.length)
			return [];

		var vs = new Array(rs.length);
		for (var r = rs.length - 1; r >= 0; r--)
		{
			var v = {};
			for (var c = cs.length - 1; c >= 0; c--)
				v[cs[c]] = rs[r][c];
			vs[r] = v;
		}
		if (!!js)
			js.Jsons = vs;
		return vs;
	};
	extend.getDataTableJson = window.pickGold.getDataTableJson;

	window.pickGold.getDTJ = extend.getDataTableJson;
	extend.getDTJ = extend.getDataTableJson;

	window.pickGold.parseUrl = function (url, data)
	{
		var k = '';
		var v = url;
		var ds = {};
		if (!arguments.length)
		{
			v = window.location.search;
		}
		else if (arguments.length == 1 && typeof (url) == typeof (ds))
		{
			data = v;
			v = window.location.search;
		}
		if (typeof (v) != typeof (k) && typeof (data) == typeof (k))
		{
			url = data;
			data = v;
			v = url
		}
		url = v;
		if (!url)
			return ds;

		var ks = [];
		var vs = [];
		for (; ;)
		{
			url = v.replace(/%([^0-9A-F]|.[^0-9A-F]|$)/img, '%25$1');
			if (v == url)
				break;

			v = url;
		}
		var ts = url.split('&');
		if (ts[0].indexOf('?') == 0)
			ts[0] = ts[0].substring(1, ts[0].length);
		for (var i = 0; i < ts.length; i++)
		{
			k = '';
			v = ts[i];
			var ii = v.indexOf('=');
			if (ii >= 0)
			{
				k = v.substring(0, ii);
				v = v.substring(ii + 1, v.length);
				v = decodeURIComponent(v);
			}
			else
			{
				k = v;
				v = null;
			}
			k = decodeURIComponent(k);
			if (typeof (ds[k]) == typeof (window.undefined))
			{
				ks.push(k);
				ds[k] = v;
				vs[k] = v;
			}
			else if (ds[k] != null && typeof (ds[k]) == typeof (vs))
			{
				ds[k].push(v);
				if (v == null)
					v = '';
				vs[k] = vs[k] + ',' + v;
			}
			else
			{
				ds[k] = [ds[k], v];
				if (vs[k] == null)
					vs[k] = '';
				if (v == null)
					v = '';
				vs[k] = vs[k] + ',' + v;
			}
			if (!!data)
				data[k] = vs[k];
			vs[ks.indexOf(k)] = vs[k];
		}
		k = '_url';
		for (; ;)
		{
			if (!vs[k])
			{
				vs[k] = url;
				break;
			}

			k = '_' + k;
		}
		k = '_keys';
		for (; ;)
		{
			if (!vs[k])
			{
				vs[k] = ks;
				break;
			}

			k = '_' + k;
		}
		k = '_values';
		for (; ;)
		{
			if (!vs[k])
			{
				vs[k] = ds;
				break;
			}

			k = '_' + k;
		}
		return vs;
	};
	extend.parseUrl = window.pickGold.parseUrl;

	extend.parseDate = function (input, value)
	{
		var g = parseFloat(input);
		if (!isNaN(g) && (typeof (input) != typeof ('') || !!/^(\-?[0-9]+)$/img.exec(input + '')))
			return new Date(g);

		if (!value)
			value = NaN;
		if (!input || typeof (input) != typeof (''))
			return value;

		return window.pickGold.parseDate.call(input, value);
	};

	extend.parseTime = function (input, value)
	{
		var v = parseFloat(input);
		if (!isNaN(v) && (typeof (input) != typeof ('') || !!/^(\-?[0-9]+)$/img.exec(input + '')))
			return new Date(v);

		if (!value)
			value = NaN;
		if (!input || typeof (input) != typeof (''))
			return value;

		return window.pickGold.parseTime.call(input, value);
	};

	extend.upper = function (input)
	{
		if (!!input)
			return input.toUpperCase();

		return input
	};

	extend.lower = function (input)
	{
		if (!!input)
			return input.toLowerCase();

		return input
	};

	extend.trim = function (input)
	{
		if (!!input)
			return input.replace(/^[\r\n\t\s ]+|[\r\n\t\s ]+$/img, '');

		return input
	};

	window.pickGold.tryGet = function (o, name)
	{
		var u = window.undefined;
		if (o == null || o == u)
			return u;

		if (!!name && !!name.toLowerCase)
			name = name.toLowerCase();
		for (var key in o)
		{
			if (key == name || (!!key && !!key.toLowerCase && key.toLowerCase() == name))
			{
				if (arguments.length > 1)
				{
					for (var i = 1; i < arguments.length; i++)
					{
						if (o[key] == arguments[i])
							return true;
					}

					return false;
				}

				return o[key];
			}
		}
		return u;
	};
	extend.tryGet = window.pickGold.tryGet;

	window.pickGold.toUTF8 = function (value)
	{
		return value.replace(/[^\u0000-\u00FF]/g, function ($0) { return escape($0).replace(/(%u)(\w{4})/gi, "&#x$2;") });
	};
	extend.toUTF8 = window.pickGold.toUTF8;

	window.pickGold.toGB2312 = function (value)
	{
		return unescape(value.replace(/&#x/g, '%u').replace(/\\u/g, '%u').replace(/;/g, ''));
	};
	extend.toGB2312 = window.pickGold.toGB2312;

	window.pickGold.$eval = function (input, replace, error)
	{
		if (arguments.length == 0 || !input)
			return input;

		if (typeof (input) == typeof (arguments.callee))
			input = input();
		if (typeof (input) != typeof (''))
			return input;

		return window.pickGold.eval.call(input, replace, error);
	};
	if (!$ || !$.eval)
		extend.eval = window.pickGold.$eval;

	extend.bitOP = Number.prototype.bitOP;

	parseDate = extend.parseDate;
	parseTime = extend.parseTime;
	window.getDataTableJson = extend.getDataTableJson;
	window.getDTJ = extend.getDTJ;
	window.parseDate = extend.parseDate;
	window.parseTime = extend.parseTime;
	document.parseDate = extend.parseDate;
	document.parseTime = extend.parseTime;

	var o = function ($, cb)
	{
		var sl = null;
		if (!!cb)
		{
			sl = function ()
			{
				if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete")
				{
					if (!!this.callback)
						this.callback();

					this.onload = this.onreadystatechange = null; // Handle memory leak in IE
					window.pickGold.ctor(window.jQuery);
				}
			};
		}
		var h = document.getElementsByTagName('HEAD').item(0);
		if (!$)
			$ = window.pickGold.jQuery;
		if (!!$ && !!$(document.body).dialog)
			return;

		var s = document.createElement("link");
		s.id = 'css-jqueryui';
		s.rel = 'stylesheet';
		s.rev = 'stylesheet';
		s.href = '//apps.bdimg.com/libs/jqueryui/1.9.2/themes/base/jquery-ui.css';
		s.type = 'text/css';
		h.appendChild(s);

		if (!$)
		{
			s = document.createElement("script");
			s.id = 'js-jquery';
			s.src = '//apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js';
			s.type = 'text/javascript';
			//s.defer = 'defer';
			//s.async = true;
			s.onload = sl;
			s.onreadystatechange = sl;
			s.callback = cb;
			h.appendChild(s);
		}

		s = document.createElement("script");
		s.id = 'js-jqueryui';
		s.src = '//apps.bdimg.com/libs/jqueryui/1.9.2/jquery-ui.min.js';
		s.type = 'text/javascript';
		s.defer = 'defer';
		s.async = true;
		s.onload = sl;
		s.onreadystatechange = sl;
		s.callback = cb;
		h.appendChild(s);

		if (!cb)
			return;

		setTimeout(cb, 100);
	};
	window.pickGold['hsj://load/resource'] = o;
	if (!!window.pickGold['hsj://html/loading'])
		return;

	if (!$)
		$ = window.jQuery;
	if (!$)
	{
		o($, arguments.callee);
		o = function (functionOrDocument)
		{
			this.ready = function (f)
			{
				if (!window.pickGold['hsj://function/ready'])
					window.pickGold['hsj://function/ready'] = [];
				window.pickGold['hsj://function/ready'].push(f);
			};
			if (typeof (functionOrDocument) == typeof (arguments.callee))
				this.ready(functionOrDocument);
			return this;
		};
		window.pickGold['hsj://function/$'] = o;
		window.pickGold['$'] = o;
		window['$'] = o;
		return;
	}

	window.pickGold['hsj://html/loading'] = '\
<img src="data:image/gif;base64,R0lGODlhHAAcAPYAAP///wAAAPDw8N7e3ri4uIKCguDg4KCgoIiIiGRkZO7u7szMzMrKyuTk5EBA\
QPr6+nZ2dlpaWtDQ0G5ubsTExGZmZjg4OJiYmOzs7GpqatbW1qKioj4+PkRERMjIyGBgYFJSUrS0tIaGhnBwcJCQkM7OzuLi4kxMTGxsbOrq\
6pycnIyMjLq6unJycvz8/KampiIiIsDAwISEhObm5o6OjsLCwp6enoqKira2tpSUlCQkJCYmJvLy8pKSkqysrKioqDIyMhoaGqqqqry8vNzc\
3Ojo6KSkpNLS0vT09Pb29oCAgC4uLhISEgQEBAAAAHh4eLKyshgYGHp6esbGxtra2vj4+L6+vtTU1NjY2FZWVlxcXJaWlpqamk5OTnx8fK6u\
rrCwsEJCQn5+fiwsLCgoKHR0dAoKClRUVFhYWEpKSmJiYjY2NjAwMBwcHF5eXjo6OiAgIAYGBgwMDFBQUGhoaEZGRjw8PBQUFCoqKhYWFh4e\
HggICEhISDQ0NAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAHAAc\
AAAH/4AAgoOEhYaHiIUKKYmNh0ofjoklL4RLUQ+DVZmSAAswOYIKTE1UglUCVZ0AGBYwPwBHTU44AFU8PKuCEzpARB5OTjYAPEi5jQYNgzE7\
QS1ET1JTD7iqgi6chAcOFRsmABUQBoQuSAIALjwpMwqHCBYcJyrHhulF9xiJFx0WMo0Y99o18oBCWSIXKZI0eoBhkaQHEA0JIIAAQoYPKiSl\
wIKFyIAUnAYUSBAhAogVkmZc0aChIz0ACiQQCLFAEhIMKXhkO8RiRqMqBnYe0iAigwoXiah4KMEI0QIII1rQyHeoypUFWH0aWjABAgkPLigI\
KUIIiQQNrDQs8EC2EAMKBlIV9EBgRAHWFEes1DiWpIjWRDVurCCCBAqUGUhqxEC7yoUNBENg4sChbICVaasw3PCBNAkLHAI1DBEoyQSObDGG\
ZMPyV5egElNcNxJAVbZtQoEAACH5BAkKAAAALAAAAAAcABwAAAf/gACCg4SFhoeIhUVFiY2HYlKOiUdDgw9hDg+DPjWSgh4WX4JYY2MagipO\
BJ4AGF0OnTVkZDEAX05mDawAXg5dGCxBQQRFTE5djkQYgwxhFghYSjIDZU6qgy6ahS8RSj6MEyImhAoFHYJJPAJIhz1ZERVfCi6HVelISDyJ\
NloRCI08ArJrdEQKEUcKtCF6oEDBDEkPIhoSwEKFDCktDkhyuAgDD3oADOR40qIFCi4bZywqkqIKISRYKAwpIalKwCQgD7kYMi6RC0aOsGxB\
8KLRDA1YBCQqsaLpBqU6DSDVsMzQFRkkXhwBcIUBVHREDmIYgOWKAkMMSpwFwINAiCkCTI5cEaCBwYKBVTAAnYQjBAYFVqx4XLBgwK6dIa4A\
UFCjxjIDDCTkdIQBzAJBPBrrA0DFw2ZJM2gKcjGFgsIBa3cNOrJVdaKArmMbCgQAIfkECQoAAAAsAAAAABwAHAAAB/+AAIKDhIWGh4iFRSmJ\
jYckK46JEjWECWqEQgSSghJnIYIzaSdFghdRQ5wAPBlalRIdHUcALzBrGKoAPVoJPBQWa1MNbDsJjgOMggtaaDkaCDREKG06OIMDHoYhEzRg\
pTQiWIQmCJhUEGxOT4dGEy1SYMmGLgVmTk5uiWBlLTQuiSTutXBERcSVRi5OWEtUBUMKE6r+FeJR48cFEjdeSEoigIfHJBIb/MixYgWCDZKQ\
eFz5gFAVE0cWHHRUJUmSKhIRHSnVCENORCZYhJjys5CAGUWQJCISAsdQHolSLCoC1ZABMASmGACApYQCQg+kAkCCocgMpYWIGEBLMQYDBVRM\
iPAwoUFDEkEPPDrCUiOGAAUePCioogFLg1wuPMSgAkDAggUCAMzQwFiVgCEzkzy+C6DBFbSSiogbJEECoQZfcxEiUlk1IpWuYxsKBAAh+QQJ\
CgAAACwAAAAAHAAcAAAH/4AAgoOEhYaHiIUzDYmNhxckjolXVoQQIy6DX5WSAFQZIYIKFQlFgjZrU50ASUojMZ4fblcAUBxdCqsALy1PKRpo\
Z0czJ2FKjgYpmQBEZSNbAys5DUpvDh6CVVdDy4M1IiohMwBcKwOEGFwQABIjYW3HhiwIKzQEM0mISmQ7cCOJU2is4PIgUQ44OxA4wrDhSKMq\
KEo0QpJCQZFuiIqwmGKiUJIrMQjgCFFDUggnTuKQKWNAEA8GLHCMLOkIB0oncuZgIfTAYooUkky8CLEASaIqwxzlczSjRgwGE3nwWHqISAyn\
EowiEsADSddDBoZQOAKUigYehQQAreJVgFZCM1JSVBGEZMGCK1UapEiCoUiRpS6qzG00wO5UDVd4PPCba5ULCQw68tBwFoAAvxgbCfBARNAD\
LFgGK8C3CsO5QUSoEFLwVpcgEy1dJ0LSWrZtQYEAACH5BAkKAAAALAAAAAAcABwAAAf/gACCg4SFhoeIhRgziY2HQgeOiUQ1hDcyLoNgFJKC\
JiIEggpSEIwALyALnQBVFzdTAANlZVcAQxEVCqsABCs0ClgTKCUCFVo9jg0pVYIpNDc/VBcqRFtZWrUASAtDhlhgLCUpAFAq2Z4XJAAaK2dr\
W4dHITg4CwrMhg8IHQ52CIlUCISw8iARlzd1IjVCwsBEowciBjRKogDDOEdEQsSgUnAQEg0MasSwwkCSiig7loRBcURQEg0eatQgKekASjwc\
MpQohCRFkYuNDHwhcCVJoipYMDhSosHRjAULWib64STOjUQGGEDVgO8QHSdgMxxq4KEEFQEAZhjo6JEHAAZqUu44EWNIgQB8LzWYqKJAQRIe\
gDsqiPElGRauSWbMQOKCBxK3q1xQ0VCEVZEiSAD85ZGpE5IrDgE8uIwPyd1VAkw1q+yx6y5RSl8nesBWtu1BgQAAIfkECQoAAAAsAAAAABwA\
HAAAB/+AAIKDhIWGh4iFGEWJjYcEX46JDUeEG1sPgwQlkoIYUAuCPD00M4JfGVedAC5DIRoAMzQrWAA1I14CqwBHODg8JggiVwpPLQeORSlV\
or4UJj8/RDYTZUSCAiUxLoUGQxRHGABXMSaEA1wqABoXdCAvh0QxNTUlPNyGSDluWhHqiCYoxPCQCRGXLGrAOEoiwVQiJBdSNEKiAIM4R1SG\
TCFSUFASKhIWLGCgypGKNWHqoJECC0CSAUdEMmjZaMOaDmncILhGKIkABbocmfAgoUGjByaQOGrBwFEKLBrMJbIBh4yMSRqgmsB3CAKZHXAy\
HCpyBUtSABa5sjoAAoAECG9QgngxJAAJvgdF8lbhwQOAEidOYghSMCVEx0MK8j7Ye4+IHCdzdgHIq+sBX2YHnJhxKCnJjIsuBPAo+BfKqiQK\
CPEllCOS5EFIlL5OpHa27UAAIfkECQoAAAAsAAAAABwAHAAAB/+AAIKDhIWGh4iFPBiJjYdXDI6JAlSENUMugx4akoJIVpwAVQQ4AoI1Mgad\
gh5WRAAKOCENAEc3PTyrABo1NQICIVAzPD00Qo4YCg+evR4YFBRFQjcrA4JJWAuGMx4lVAoAV1O0g1QbPgADP0oZYIcmDAsLGjyZhikqZS0T\
x4gz8hLsGXJxYQQEAo6SaDCVCMMFE40e8ECSRJKBI0eKCASQxAQRLBo0WHPE5YwbNS1oVOLoEeQViI6MmEwwgsYrQhIpSiqi4UqKjYUeYAAa\
VMkRRzyKFGGU6IedDjYSKSiSgirRQTLChLGD4JCAGUsrTixU5QCdWivOrNliiKI9iRNNZ3wBY0KKHh1DPJVggRRJrhhOnBgxwIYMGl0AeIw9\
EjgEACMw2JCT5EKxIAxynFwRhCBKjFUSCQHJs0xQjy+ICbXoUuhqJyIlUss2FAgAIfkECQoAAAAsAAAAABwAHAAAB/+AAIKDhIWGh4iFVQKJ\
jYdEDI6JPESECzVVg0RUkoJVHliCLlMxCoJUYAadglcMAwBJFDFFAA0hBEirACYLCwpJMVYNDyw4U44CPA+CSb0SPAsMKUdQIaqwDVguhQpX\
WAOmJhIYhBhTx0UhWyIEhykaWBoGSYgKUCQrCCGJCvHXhy583FhRw1GVBvQSpRAyo1GVJFUyORpw5IqBXINcYCjCsUgKST9QlCkjhss1jR1n\
fHT0BQUEKQUOmCjk4gFESSkGmEixDJELZY14iDjiKAkPJDwa+UDjZkMipEgZIUqyIYGWLDR6EkqSjEcmJTeSDuLxY8QuLi2ybDFUReuAPU5W\
+KTgkkOCCgsc9gF4wEvrISlOnLAgAiePCgFnHKDQBQCIkycADADR4QPAFAd8Gqwy4ESLIAF2dlAQ5KMPlFULpBACgUezIChfGBOiAUJ2oiJX\
bOsmFAgAIfkECQoAAAAsAAAAABwAHAAAB/+AAIKDhIWGh4iFDzyJjYcNEo6JSAaEGgtJgyZEkoIPGgODEgwKggZDJp2CAxoNAA8lDEUAKTE1\
jKopWBoKDwsMMw9TNQuOSUkuglVYWERJWFe6VjGuAFUKJsmESDNFKUgAGAaZgwKxAAILLFDFhjzeRUVViEgSBDghDJPxKY0LISGuOHKBYd4k\
D6USPVj4QJIJKkQakBvEo2JFAZJCiFhBI4eQVIKQWKwoCQcCGj0ufJlRyEXDTkVmzOiViIgblokU0IjU6EUeJy0a/ZjQQshLQ1ucKE2Dy5AC\
MFJaTLhgkNAXJ3m6DAFwwwtOQQpeeAnnA8EEG4Y8MMBlgA2cEylSVORY8OVMhBCDihw5emiFDh1gFITp8+LBCC1jVQE40+YJAAUgOOA94sZN\
qE4mYKiZVyWCA30ArJzB20mClKMtOnylAEVxIR8VXDfiQUW2bUOBAAAh+QQJCgAAACwAAAAAHAAcAAAH/4AAgoOEhYaHiIUuAomNhwpUjokP\
KYQGGkmDKSaSgi4zlYJUGowAMx4NnYIYRZVVWFiVCgsLPKoAAkVFSA8aGhgAJQtHjg9VLp6tM0kNJjwGDAupAC48RciEVQI8PJkCKdiCrxIA\
SRpTVuSGSTxIPAJViElYNTUxJYna7o1HMTEakqo8aMTDg4JGM6aAYSApRYoiAsIBwABhzB4nTiZIkgAFB44hDGYIUgCBjRyMGh1x9GglZCEM\
C4ZckYRBQRFbiTDQAZgohQ0ijkKs0TOiEZQbKwhIJLRBxw4dXaYZwmClx4obP5YCINCGTZYQAIx4CTVyg4xqLLggEGLIA4VpCldAcNDS4AIJ\
BkNQtGAhiBKRgYmMOHDAQoGWM2AAyCiz4haAEW+8TKygBSyWMmUMqOJRpwWyBy0iUBDkIQPfTiZIxBNEA41mQRIIOCYUo8zsRDx43t4tKBAA\
IfkECQoAAAAsAAAAABwAHAAAB/+AAIKDhIWGh4iGSYmMh0gzjYkuPIQYRQ+DPA2RgwKUgilFSIICV5ucAEhIn6ECqVgarqhJPDyLRUUKAFRY\
VI1HMZAALgJIAg8KGDwKGlinAEkKLoU1Tnt1BABVAtOEKb4PBhIMR4c+cU5OaymILiYlCwtHmIcxQU4fjAYMDFjdiApQSGBU5QgGRjOmEFgQ\
CUMKZf8AKLgBAgiZNvkaURkSo8aUI+wAYJDSYcyONloibexIoYQwQS6oEPgxpOGMXPQOPdjCMFESCgcZHdFiYUROQ0dChCgRkRCFOg4cRMCC\
iIcGAjhCUDgq6AiHDhWyxShAhJACKFweJJHAAgoFQ1dfrAwQlKRMhAwpfnCZMkXEihqCHmAwUIXRkAgRoLiQgsIHABsrVDRl1OPMDQAPZIzA\
AcAEjRVzOT2gI+XTjREMBF0RUZMThhyyAGyYYGCQhtaoCJVQMjk3ISQafAtHFAgAIfkECQoAAAAsAAAAABwAHAAAB/+AAIKDhIWGh4iGD4mM\
h1UCjYkNXlWDSQKVgo+Rgkl3HZkCSEmdMwqcgnNOWoI8SDwAD0VFSKgAP05ONgACPLApKUUujAsesABIek46CkmuAjNFp4IPPIuEQ3p2dDgA\
JBEmhdAuLikDGljDhTY6OjtZM4guAlRYWFSZhmB9cF3Xhxg0aBjw75ABNVYaGcDACEkDA+EaVUmSJJ8gF2AmgDgRBkWkGQwWlJBA5ViSG3Pq\
OHiTIFIDDwtESkhBqAqRKTgoROJRJAUmRlA8MHoggSEjA16yQKiFiEqMGFgSXaETQcsEKoiSYIlRI0YJdYRMuIkgxYcLCSs0gEVyxcq8K1Nh\
hpQwxCDEgEE3WrQggsPHFCpQcGCNlYKIRUNXyrTA4aIHAigArOAYUrDRhgk0yF1YQQBAChwhGqB6IEbJNCMIpggaAOYKKgwXjAJggSAiAANH\
bBW6kgMsAN+6q7jWTfxQIAA7AAAAAAAAAAAA" alt="loading..." />';

	window.pickGold.alertBox = function (message, level, confirm, args)
	{
		if (!window.pickGold['hsj://box/alert'])
		{
			window.pickGold['hsj://box/alert'] = $('\
				<table cellpadding="1" cellspacing="1" border="0" style="width: 650px; height: 150px; margin: auto auto auto auto;">\
				<tr><td style="text-align: center; height: 50px;" id="td-hsj-box-alert">&nbsp;</td></tr>\
				</table>');
			if (!window.pickGold['hsj://box/alert'].dialog)
			{
				window.pickGold['hsj://box/alert'] = null;
				window.pickGold['hsj://load/resource']($);
				alert(message);
				if (!confirm)
					confirm();
				return;
			};

			window.pickGold['hsj://box/alert'].dialog(
				{
					autoOpen: false,
					resizable: false,
					modal: true,
					title: '&#x63D0;&#x793A;'// 提示
				}).data('button-default', function ()
				{
					window.pickGold['hsj://box/alert'].dialog('close');
				});
		}
		window.pickGold['hsj://box/alert'].data('button-confirm', confirm);
		window.pickGold['hsj://box/alert'].data('button-arguments', args);
		if (!confirm)// 纯提示
		{
			confirm = {};
			confirm[window.pickGold.toGB2312('&#x786E;&#x5B9A;')] = window.pickGold['hsj://box/alert'].data('button-default'); // 确定
		}
		else if (typeof arguments.callee == typeof confirm)// 传入方法
		{
			confirm = {};
			confirm[window.pickGold.toGB2312('&#x786E;&#x5B9A;')] = function ()// 确定
			{
				window['hsj://box/alert'].data('button-default').call(this);
				window['hsj://box/alert'].data('button-confirm').call(this, window.pickGold['hsj://box/alert'].data('button-arguments'), window.pickGold['hsj://box/alert']);
			};
			confirm[window.pickGold.toGB2312('&#x53D6;&#x6D88;')] = window.pickGold['hsj://box/alert'].data('button-default'); // 取消
		}
		else// 传入按钮
		{
			confirm = {};
			for (var k in window.pickGold['hsj://box/alert'].data('button-confirm'))
			{
				confirm[k] = function ()
				{
					window.pickGold['hsj://box/alert'].data('button-default').call(this);
					var optionButtons = window.pickGold['hsj://box/alert'].dialog('option', 'buttons');
					var inputButtons = window.pickGold['hsj://box/alert'].data('button-confirm');
					for (var kk in optionButtons)
					{
						if (optionButtons[kk] == arguments.callee && !!inputButtons[kk])
						{
							inputButtons[kk].call(this, window.pickGold['hsj://box/alert'].data('button-arguments'), window.pickGold['hsj://box/alert']);
							return;
						}
					}
				};
			}
		}
		window.pickGold['hsj://box/alert'].dialog('option', 'buttons', confirm);
		window.pickGold['hsj://box/alert'].dialog('open');
		if (!message)
			$('#td-hsj-box-alert').html('&#x9519;&#x8BEF;&#xFF01;'); // 错误！
		else
			$('#td-hsj-box-alert').html(message);
		return window.pickGold['hsj://box/alert'];
	};
	if (!$ || !$.alertBox)
		extend.alertBox = window.pickGold.alertBox;

	///
	/// e = 要在其中显示内容的Html元素。
	///
	window.pickGold.ajaxStartBox = function (ajax, message, e)
	{
		if (!!e)
		{
			ajax['hsj://box/ajax/html'] = $(e).html();
			$(e).html('\
			<table cellpadding="1" cellspacing="1" border="0" style="width: 98%; height: 150px; margin: auto auto auto auto;">\
			<tr><td style="text-align: center; height: 50px;">' + window.pickGold['hsj://html/loading'] + '</td></tr>\
			<tr><td style="text-align: center; height: 50px;">Loading...</td></tr>\
			<tr><td style="text-align: center; height: 50px;">' + (!message ? '&nbsp;' : message) + '</td></tr>\
			</table>');
			ajax['hsj://box/ajax/e'] = e;
			return;
		}

		if (!window.pickGold['hsj://box/ajax'])
		{
			window.pickGold['hsj://box/ajax'] = $('\
				<table cellpadding="0" cellspacing="0" border="0" style="width: 240px;margin: auto auto auto auto;">\
				<tr><td style="text-align: center;width:235px;"><div>' + window.pickGold['hsj://html/loading'] + '</div></td></tr>\
				<tr><td style="text-align: center;"></td></tr>\
				<tr><td style="text-align: center;" id="td-hsj-box-ajax">&nbsp;</td></tr>\
				</table>');
			if (!window.pickGold['hsj://box/ajax'].dialog)
			{
				window.pickGold['hsj://box/ajax'] = null;
				window.pickGold['hsj://load/resource']($);
				return ajax;
			}

			window.pickGold['hsj://box/ajax'].dialog(
				{
					autoOpen: false,
					closeOnEscape: false,
					resizable: false,
					modal: true,
					title: '&#x6B63;&#x5728;&#x63D0;&#x4EA4;&#x5E76;&#x52A0;&#x8F7D;&#x6570;&#x636E;....'// 正在提交并加载数据....
				}
			);
		}
		window.pickGold['hsj://box/ajax'].dialog('open');
		if (!message)
			$('#td-hsj-box-ajax').html('&nbsp;');
		else
			$('#td-hsj-box-ajax').html(message);
		ajax['hsj://box/ajax/e'] = 1;
		return ajax;
	};
	if (!$ || !$.ajaxStartBox)
		extend.ajaxStartBox = window.pickGold.ajaxStartBox;

	window.pickGold.ajaxSuccessBox = function (ajax, request, message)
	{
		if (ajax['hsj://box/ajax/e'] == 1)
		{
			window.pickGold['hsj://box/ajax'].dialog('close');
			if (!!message)
			{
				if (!this.alertBox)
					window.pickGold.alertBox(message);
				else
					this.alertBox(message);
			}
			return;
		}

		if (!ajax['hsj://box/ajax/e'] || (!ajax['hsj://box/ajax/html'] && ajax['hsj://box/ajax/html'] != ''))
			return;

		if (!message)
			$(ajax['hsj://box/ajax/e']).html(ajax['hsj://box/ajax/html']);
		else
			$(ajax['hsj://box/ajax/e']).html(message);
		return ajax;
	};
	if (!$ || !$.ajaxSuccessBox)
		extend.ajaxSuccessBox = window.pickGold.ajaxSuccessBox;

	///
	/// ajax = Ajax 参数对象，JSON
	/// request = HttpXMLRequest
	/// message = 要显示的消息
	/// contentError = 是否是内容错误，如JSON字符串解析不了；
	///
	window.pickGold.ajaxErrorBox = function (ajax, request, message, contentError)
	{
		if (!this.ajaxSuccessBox)
			window.pickGold.ajaxSuccessBox(ajax, request);
		else
			this.ajaxSuccessBox(ajax, request);
		if (!message)
		{
			if (!!request && !!request.message && !request.status)
				contentError = true;
			if (!window.pickGold['E'])
				window.pickGold['E'] = { 'UNKNOW_SERVER_ERROR': 544, 'KNOW_SERVER_ERROR': 555 };
			if (!window.pickGold['E']['UNKNOW_SERVER_ERROR'])
				window.pickGold['E']['UNKNOW_SERVER_ERROR'] = 544;
			if (!window.pickGold['E']['KNOW_SERVER_ERROR'])
				window.pickGold['E']['KNOW_SERVER_ERROR'] = 555;
			if (contentError)
			{
				message = '&#x670D;&#x52A1;&#x5668;&#x8FD4;&#x56DE;&#x6570;&#x636E;&#x683C;&#x5F0F;&#x9519;&#x8BEF;&#xFF0C;&#x8BF7;&#x8054;&#x7CFB;&#x7BA1;&#x7406;&#x5458;&#x3002;'; // 服务器返回数据格式错误，请联系管理员。
			}
			else if (!request)
			{
				message = '&#x8BF7;&#x6C42;&#x670D;&#x52A1;&#x5668;&#x9519;&#x8BEF;&#xFF0C;&#x8BF7;&#x7A0D;&#x5019;&#x91CD;&#x8BD5;&#x3002;'; // 请求服务器错误，请稍候重试。
			}
			else if (request.status == window.pickGold['E']['KNOW_SERVER_ERROR'])
			{
				if (!request.responseXML || !request.responseXML.documentElement)
					message = request.responseText;
				else if (!!request.responseXML.documentElement.textContent)
					message = request.responseXML.documentElement.textContent;
				else if (!!request.responseXML.documentElement.text)
					message = request.responseXML.documentElement.text;
				else
					message = request.responseText;
			}
			else if (request.status == window.pickGold['E']['UNKNOW_SERVER_ERROR'])
			{
				if (!request.responseXML || !request.responseXML.documentElement)
					message = request.responseText;
				else if (!!request.responseXML.documentElement.textContent)
					message = request.responseXML.documentElement.textContent;
				else if (!!request.responseXML.documentElement.text)
					message = request.responseXML.documentElement.text;
				else
					message = request.responseText;
				message = '&#x53D1;&#x751F;&#x4E00;&#x4E2A;&#x53EF;&#x6355;&#x83B7;&#x7684;&#x5F02;&#x5E38;&#xFF1A;' + message; // 发生一个可捕获的异常：
			}
			else if (request.status == 500)
			{
				message = '&#x8BF7;&#x6C42;&#x670D;&#x52A1;&#x5668;&#x9519;&#x8BEF;&#xFF0C;&#x8BF7;&#x7A0D;&#x5019;&#x91CD;&#x8BD5;&#x3002;'; // 请求服务器错误，请稍候重试。
			}
			else
			{
				message = '&#x8BF7;&#x6C42;&#x670D;&#x52A1;&#x5668;&#x9519;&#x8BEF;&#xFF0C;&#x8BF7;&#x7A0D;&#x5019;&#x91CD;&#x8BD5;&#x3002;'; // 请求服务器错误，请稍候重试。
			}
		}
		if (!this.alertBox)
			window.pickGold.alertBox(message, 1);
		else
			this.alertBox(message, 1);
		return ajax;
	};
	if (!$ || !$.ajaxErrorBox)
		extend.ajaxErrorBox = window.pickGold.ajaxErrorBox;

	window.pickGold.classNameVerify = function (value, name, data, ajax)
	{
		if (!this.className)// 以下是使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
			return this;

		var cs = this.className.split(/[\r\n\t\s ]+/img);
		for (var i in cs)
		{
			var c = cs[i];
			if (!!c && typeof (window[c]) == typeof (arguments.callee))
				window[c].call(this, value, name, data, ajax);
		}
		return this;
	};
	extend.classNameVerify = window.pickGold.classNameVerify;

	window.pickGold.setFormValue = function (value)// window.pickGold.setFormValue.call(this, value)
	{
		var v = this;
		try
		{
			v = this.editor; // 如果元素是编辑器，或存在特殊赋值方式的，则视为表单内容。
		}
		catch (x)
		{
			v = null;
		}
		if (typeof (this.setData) == typeof (arguments.callee))// 元素本身存在特殊赋值方式
			v = function (v) { this.setData(v); };
		else if (typeof (this.setValue) == typeof (arguments.callee))// 元素本身存在特殊赋值方式
			v = function (v) { this.setValue(v); };
		else if (typeof (this.setContent) == typeof (arguments.callee))// 元素本身存在特殊赋值方式
			v = function (v) { this.setContent(v); };
		else if (!v)// 元素本身没有特殊赋值方式也不是明确标识的编辑器
			v = null;
		else if (typeof (v.setData) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊赋值方式
			v = function (v) { this.editor.setData(v); };
		else if (typeof (v.setValue) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊赋值方式
			v = function (v) { this.editor.setValue(v); };
		else if (typeof (v.setContent) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊赋值方式
			v = function (v) { this.editor.setContent(v); };
		else// 元素本身没有特殊赋值方式也不是明确标识的编辑器
			v = null;
		if (!!v && typeof (v) == typeof (arguments.callee))
		{
			v = v.call(this, value);
			v = this.onchange;
			if (!!v && typeof (v) == typeof (arguments.callee))
				v.call(this, value);
			return;
		}

		v = $.lower(this.tagName); //$.lower($(this).attr('tagName'))
		if (v == 'select' && !!$(this).attr('multiple'))//if (v == 'select' && !!$(this).attr('multiple'))//
		{
			$(this).children().each(function ()
			{
				if ($.isArray(value))//!!value && (value instanceof Array || value.constructor == Array || Object.prototype.toString.call(value) === '[object Array]')
				{
					for (var i = 0; i < value.length; i++)
					{
						if ($(this).val() == value[i])
						{
							$(this).attr('selected', 'selected');
							v = this.parent.onchange;
						}
					}
					if (!!v && typeof (v) == typeof (arguments.callee))
						v.call(this, value);
					return;
				}

				if ($(this).val() == value)
				{
					$(this).attr('selected', 'selected');
					v = this.parent.onchange;
					if (!!v && typeof (v) == typeof (arguments.callee))
						v.call(this, value);
				}
			});
			return;
		}

		v = $.lower($(this).attr('type'));
		if (v == 'checkbox' || v == 'radio')
		{
			if ($.isArray(value))//!!value && (value instanceof Array || value.constructor == Array || Object.prototype.toString.call(value) === '[object Array]')
			{
				for (var i = 0; i < value.length; i++)
				{
					if ($(this).val() == value[i])
					{
						$(this).attr('checked', 'checked');
						v = this.onchange;
						if (!!v && typeof (v) == typeof (arguments.callee))
							v.call(this, value);
					}
				}
				return;
			}

			if ($(this).val() == value)
			{
				$(this).attr('checked', true);
				v = this.onchange;
				if (!!v && typeof (v) == typeof (arguments.callee))
					v.call(this, value);
			}
			return;
		}

		$(this).val(value);
		v = this.onchange;
		if (!!v && typeof (v) == typeof (arguments.callee))
			v.call(this, value);
	};
	extend.__innerSetFormValue = window.pickGold.setFormValue;

	window.pickGold.setForm = function (data, selector)
	{
		$(selector).each(function ()
		{
			window.pickGold.setFormValue.call(this, data);
		});
		return data;
	};
	extend.setForm = window.pickGold.setForm;

	window.pickGold.fillForm = function (data, selector, format)
	{
		var e = { clear: {}, data: data, selector: selector, format: format, name: null, key: null, value: null };
		if (!e.data || !selector)
			return e.data;

		if (!e.format)
			e.format = '{0}';
		if (typeof (e.data) == typeof (e.format))
			e.data = window.pickGold.eval.call(e.data);
		$(e.selector).each(function ()
		{
			if (typeof (e.data[this]) != typeof (e.undefined))
			{
				e.value = e.data[this];
			}
			else if (typeof (e.data[selector]) != typeof (e.undefined))
			{
				e.value = e.data[selector];
			}
			else if (typeof (e.data['']) != typeof (e.undefined))
			{
				e.value = e.data[''];
			}
			else
			{
				e.name = $(this).attr('name');
				if (!e.name)
					e.name = $(this).attr('id');
				if (!e.name)
					return;

				for (var k in e.data)
				{
					if (e.format.replace('{0}', k) == e.name)
					{
						e.key = k;
						break;
					}
				}
				if (e.key == null)
					return;

				e.value = e.data[e.key];
			}
			window.pickGold.setFormValue.call(this, e.value);
		});
		return e.data;
	};
	extend.fillForm = window.pickGold.fillForm;

	window.pickGold.getForm = function (value, selector, verify)
	{
		$(selector).each(function ()
		{
			var v = this;
			try
			{
				v = this.editor; // 如果元素是编辑器，或存在特殊取值方式的，则视为表单内容。
			}
			catch (x)
			{
				v = null;
			}
			if (typeof (this.getData) == typeof (arguments.callee))// 元素本身存在特殊取值方式
				v = function () { return this.getData(); };
			else if (typeof (this.getValue) == typeof (arguments.callee))// 元素本身存在特殊取值方式
				v = function () { return this.getValue(); };
			else if (typeof (this.getContent) == typeof (arguments.callee))// 元素本身存在特殊取值方式
				v = function () { return this.getContent(); };
			else if (!v)// 元素本身没有特殊取值方式也不是明确标识的编辑器
				v = null;
			else if (typeof (v.getData) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊取值方式
				v = function () { return this.editor.getData(); };
			else if (typeof (v.getValue) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊取值方式
				v = function () { return this.editor.getValue(); };
			else if (typeof (v.getContent) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊取值方式
				v = function () { return this.editor.getContent(); };
			else// 元素本身没有特殊取值方式也不是明确标识的编辑器
				v = null;
			if (!!v && typeof (v) == typeof (arguments.callee))
			{
				v = v.call(this);
				if (!value)
					value = v;
				else if ($.isArray(value))
					value.push(v);
				else
					value = [value, v];
				if (!!verify)
					window.pickGold.classNameVerify.call(this, value); // 使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
				return;
			}

			v = $.lower(this.tagName);
			if (v == 'select' && !!$(this).attr('multiple'))
			{
				$(this).children().each(function ()
				{
					if (!$(this).attr('selected'))
						return;

					v = $(this).val();
					if (!value)
						value = v;
					else if ($.isArray(value))
						value.push(v);
					else
						value = [value, v];
				});
				if (!!verify)
					window.pickGold.classNameVerify.call(this, value); // 使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
				return;
			}

			v = $.lower($(this).attr('type'));
			if ((v == 'checkbox' || v == 'radio') && !$(this).attr('checked'))
				return;

			v = $(this).val();
			if (typeof (v) == typeof ($.undefined))
				return;

			if (!value)
				value = v;
			else if ($.isArray(value))
				value.push(v);
			else
				value = [value, v];
			if (!!verify)
				window.pickGold.classNameVerify.call(this, value); // 使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
		});
		return value;
	};
	extend.getForm = window.pickGold.getForm;

	window.pickGold.addAjaxData = function (ajax, selector)
	{
		if (!ajax)
			ajax = {};
		if (!ajax.data)
			ajax.data = {};
		if (!selector)
			return ajax;

		if (!ajax['class-name-verify'])
			ajax['class-name-verify'] = window.pickGold.classNameVerify;
		$(selector).each(function ()
		{
			var t = $(this).attr('name');
			if (!t)
				t = $(this).attr('id');
			if (!t)
				return;

			var v = this;
			try
			{
				v = this.editor; // 如果元素是编辑器，或存在特殊取值方式的，则视为表单内容。
			}
			catch (x)
			{
				v = null;
			}
			if (typeof (this.getData) == typeof (arguments.callee))// 元素本身存在特殊取值方式
				v = function () { return this.getData(); };
			else if (typeof (this.getValue) == typeof (arguments.callee))// 元素本身存在特殊取值方式
				v = function () { return this.getValue(); };
			else if (typeof (this.getContent) == typeof (arguments.callee))// 元素本身存在特殊取值方式
				v = function () { return this.getContent(); };
			else if (!v)// 元素本身没有特殊取值方式也不是明确标识的编辑器
				v = null;
			else if (typeof (v.getData) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊取值方式
				v = function () { return this.editor.getData(); };
			else if (typeof (v.getValue) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊取值方式
				v = function () { return this.editor.getValue(); };
			else if (typeof (v.getContent) == typeof (arguments.callee))// 元素本身明确标识是编辑器，且编辑器存在特殊取值方式
				v = function () { return this.editor.getContent(); };
			else// 元素本身没有特殊取值方式也不是明确标识的编辑器
				v = null;
			if (!!v && typeof (v) == typeof (arguments.callee))
			{
				v = v.call(this);
				if (!ajax.data[t])
					ajax.data[t] = v;
				else if ($.isArray(ajax.data[t]))
					ajax.data[t].push(v);
				else
					ajax.data[t] = [ajax.data[t], v];
				ajax['class-name-verify'].call(this, ajax.data[t], t, ajax.data, ajax); // 使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
				return;
			}

			v = $.lower(this.tagName);
			if (v == 'select' && !!$(this).attr('multiple'))
			{
				$(this).children().each(function ()
				{
					if (!$(this).attr('selected'))
						return;

					v = $(this).val();
					if (!ajax.data[t])
						ajax.data[t] = v;
					else if ($.isArray(ajax.data[t]))
						ajax.data[t].push(v);
					else
						ajax.data[t] = [ajax.data[t], v];
				});
				ajax['class-name-verify'].call(this, ajax.data[t], t, ajax.data, ajax); // 使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
				return;
			}

			v = $.lower($(this).attr('type'));
			if ((v == 'checkbox' || v == 'radio') && !$(this).attr('checked'))
				return;

			v = $(this).val();
			if (typeof (v) == typeof ($.undefined))
				return;

			if (!ajax.data[t])
				ajax.data[t] = v;
			else if ($.isArray(ajax.data[t]))
				ajax.data[t].push(v);
			else
				ajax.data[t] = [ajax.data[t], v];
			ajax['class-name-verify'].call(this, ajax.data[t], t, ajax.data, ajax); // 使用类（样式）名作为函数来验证：control.vaild(value, name, data, ajax);
		});
		if (!ajax.url)
		{
			if (!!ajax.href)
				ajax.url = ajax.href;
			else if (!!ajax.uri)
				ajax.url = ajax.uri;
		}
		ajax.url = $.getAjaxUrl(ajax.url, null, ajax.url);
		return ajax;
	};
	extend.addAjaxData = window.pickGold.addAjaxData;

	window.pickGold.startAjax = function (ajax, cache)
	{
		if (!!ajax.data && typeof (ajax.data) !== typeof ("string"))
		{
			var ds = {};
			for (var key in ajax.data)
			{
				if (!ajax.data[key] || ajax.data[key] == '')// || parseFloat(ajax.data[key]) == 0.00
					continue;

				if (!$.isArray(ajax.data[key]))
				{
					ds[key] = ajax.data[key];
					continue;
				}

				if (ajax.data[key].length == 0)
					continue;

				if (ajax.data[key].length == 1)
				{
					ds[key] = ajax.data[key][0];
					continue;
				}

				/*
				var vs = [];
				for (var i = 0; i < ajax.data[key].length; i++)
				{
				for (var ii = 0; ii < vs.length; ii++)
				{
				if (ajax.data[key][i] == vs[ii])
				{
				ajax.data[key][i] = null;
				break;
				}
				}
				if (!!ajax.data[key][i])
				vs.push(ajax.data[key][i]);
				}
				ds[key] = vs;
				*/
				ds[key] = ajax.data[key];
			}
			ajax.data = ds;
		}
		if (!ajax.success)
			ajax.success = function (result, xhr)
			{
				$.ajaxSuccessBox(ajax);
			};
		if (!ajax.error)
			ajax.error = function (xhr)
			{
				$.ajaxErrorBox(ajax, xhr);
			};
		if (!ajax.beforeSend)
			ajax.beforeSend = function (xhr)
			{
				$.ajaxStartBox(ajax);
			};
		if (!ajax.url)
		{
			if (!!ajax.href)
				ajax.url = ajax.href;
			else if (!!ajax.uri)
				ajax.url = ajax.uri;
		}
		ajax.url = window.pickGold.getAjaxUrl(ajax.url);
		if (!ajax.type)
			ajax.type = 'POST';
		if (!ajax.dataType)
			ajax.dataType = 'text'; //xml html script json jsonp(x.xxx?callback=xxx) text
		//if (typeof ajax.cache == typeof ajax.undefined)
		//	ajax.cache = false;
		if (arguments.length > 1)
		{
			if (typeof arguments[1] == typeof ajax.success)
			{
				ajax['complete-200'] = arguments[1];
				ajax.success = function (result, xhr)
				{
					$.ajaxSuccessBox(ajax);
					ajax['complete-200'].apply(ajax, arguments);
				};
			}
			else
			{
				ajax.cache = arguments[1];
			}
		}
		if (arguments.length > 2)
		{
			if (typeof arguments[2] != typeof ajax.success)
			{
				ajax.cache = arguments[2];
			}
			else if (!ajax['complete-200'])
			{
				ajax['complete-200'] = arguments[2];
				ajax.success = function (result, xhr)
				{
					$.ajaxSuccessBox(ajax);
					ajax['complete-200'].apply(ajax, arguments);
				};
			}
			else
			{
				ajax['complete-500'] = arguments[2];
				ajax.error = function (xhr)
				{
					$.ajaxErrorBox(ajax, xhr);
					ajax['complete-500'].apply(ajax, arguments);
				};
			}
		}
		if (arguments.length > 3)
		{
			if (typeof arguments[3] != typeof ajax.success)
			{
				ajax.cache = arguments[3];
			}
			else if (!ajax['complete-200'])
			{
				ajax['complete-200'] = arguments[3];
				ajax.success = function (result, xhr)
				{
					$.ajaxSuccessBox(ajax);
					ajax['complete-200'].apply(ajax, arguments);
				};
			}
			else
			{
				ajax['complete-500'] = arguments[3];
				ajax.error = function (xhr)
				{
					$.ajaxErrorBox(ajax, xhr);
					ajax['complete-500'].apply(ajax, arguments);
				};
			}
		}
		$.ajax(ajax);
		return ajax;
	};
	extend.startAjax = window.pickGold.startAjax;

	window.pickGold.getAjaxUrl = function (url, location, value)
	{
		if (!location || location == '')
			location = window.location.href;
		location = location.replace(/#.*$/img, '');
		if (!url || url == '')// 如果没有指定地址。
		{
			if (arguments.length > 2)
				return value;

			return location;
		}

		var r = /[^\(\)\/]+\([^\)]*\)$/img;
		if (!!r.test(url) || url.indexOf('.') == 0 || url.indexOf('$') == 0)// 如果仅指定了调用函数
		{
			//url = url.substring(1, url.length);
			//if (url.indexOf('=') == 0)
			//	url = url.substring(1, url.length);
			//url = "%24=" + url;
			//if (location.indexOf('?') > 0)
			//	return location + '&' + url;

			//return location + '?' + url;
			var e = '';
			var q = '';
			r = /(\?.*)$/img;
			if (r.exec(location))// 提取查询【?xx=xxx】
			{
				q = RegExp.$1;
				location = location.substring(0, location.lastIndexOf(q)); //location.replace(r, '')
			}
			r = /[^\/]+(\.[^\?\/]+)$/img;
			if (r.exec(location))// 提取扩展名【.do】
			{
				e = RegExp.$1;
				location = location.substring(0, location.lastIndexOf(e)); //location.replace(r, '')
			}
			r = /[^\(\)\/]+\([\r\n\t\s ]*\)$/img;
			if (!!r.test(url))// 将空括号处理为点前缀
			{
				if (url.indexOf('.') != 0 && url.indexOf('$') != 0)
					url = '.' + url;
				url = url.substring(0, url.length - 2);
			}
			r = /\/([\.\$]+.*)$/img;
			if (r.exec(location))// 如果存在 Ajax 调用方式【/.get-user/p0/p1...】
				return location.substring(0, location.lastIndexOf(RegExp.$1)) + url + e + q;

			r = /\/([^\/]+\([^\)]*\))$/img;
			if (r.exec(location))// 如果存在 Ajax 调用方式【/get-user(p0,p1...)】
				return location.substring(0, location.lastIndexOf(RegExp.$1)) + url + e + q;

			r = /\/$/img;
			if (r.exec(location))// 如果以 / 结尾
				return location + url + e + q;

			return location + '/' + url + e + q;
		}

		if (url.indexOf('?') == 0 || url.indexOf('&') == 0)// 如果仅指定了地址查询参数
		{
			url = url.substring(1, url.length);
			if (location.indexOf('?') > 0)
				return location + '&' + url;

			return location + '?' + url;
		}

		return url;
	};
	extend.getAjaxUrl = window.pickGold.getAjaxUrl;

	if (!!window.FormData && !!window.Blob && !!window.ArrayBuffer)
	{
		function BlobAjax(options)
		{
			this.xhr = null;
			this.options = options;
			this.url = this.options.url || window.location.href;
			this.type = this.options.type || 'GET';
			this.dataType = this.options.dataType || 'text';
			this.data = this.options.data || null;
			this.async = this.options.async || true;
			this.send = function (headers, completeCallback)
			{
				this.xhr = new XMLHttpRequest();
				this.result = {};
				this.headers = headers;
				this.callback = completeCallback;
				this.xhr.ajax = this;
				this.xhr.headers = this.headers;
				this.xhr.callback = this.callback;
				this.xhr.dataType = this.dataType;
				this.xhr.result = this.result;
				this.xhr.addEventListener('load', function ()
				{
					if (this.status >= 200 && this.status < 300 || this.status === 304)
						this.result[this.dataType] = this.response;
					else
						this.result.text = this.statusText;
					this.callback(this.status, this.statusText, this.result, this.getAllResponseHeaders());
				});

				this.xhr.open(this.type, this.url, this.async);
				this.xhr.responseType = this.dataType;
				for (var key in headers)
				{
					if (headers.hasOwnProperty(key))
						this.xhr.setRequestHeader(key, headers[key]);
				}
				this.xhr.send(this.data);
			};
			this.abort = function ()
			{
				if (this.xhr)
					this.xhr.abort();
			};
		}
		$.ajaxTransport("+*", function (options, originalOptions, jqXHR)
		{
			if (!!options.dataType && (options.dataType == 'blob' || options.dataType == 'arraybuffer'))
				return new BlobAjax(options);

			if (!!options.data && (options.data instanceof Blob || options.data instanceof ArrayBuffer))
				return new BlobAjax(options);
		});
	}

	extend['#'] = function (elementId)
	{
		return $('#' + elementId);
	};

	extend['.'] = function (className)
	{
		return $('.' + elementId);
	};

	window.pickGold.ime = function (element)
	{
		var e = element;
		if (!!this.tagName)//if (!!$(this).attr('tagName'))//
			e = this;
		if (!!e && !!e.target)
			e = e.target;
		if (!!e && !!e.srcElement)
			e = e.srcElement;
		if (!!e)
		{
			if ($.lower(e.tagName) == 'textarea' || $.lower($(e).attr('type')) == 'text')//if ($.lower($(e).attr('tagName')) == 'textarea' || $.lower($(e).attr('type')) == 'input')//
				$(e).focus();
			else
				$(e).prev().focus();
		}
		e = window['QQWebIME'];
		if (!!e)
		{
			e.toggle();
			return;
		}

		e = document.createElement('script');
		e.async = true;
		e.src = '//ime.qq.com/fcgi-bin/getjs';
		e.setAttribute('ime-cfg', 'lt=2');
		element = document.getElementsByTagName('head')[0];
		element.insertBefore(e, element.firstChild)
	};
	extend.ime = window.pickGold.ime;

	$.extend(extend);

	extend.fn = {};

	extend.fn.ime = function ()
	{
		this.click($.ime);
		return this;
	};

	extend.fn.addAjaxData = function (ajax)
	{
		if (!ajax)
			return $.addAjaxData(ajax, this);

		$.addAjaxData(ajax, this);
		return this;
	};

	extend.fn.setForm = function (data)
	{
		$.setForm(data, this);
		return this;
	};

	extend.fn.getForm = function (data)
	{
		return $.getForm(data, this);
	};

	extend.fn.vl = function (data)
	{
		if (!arguments.length)
			return $.getForm(data, this);

		$.setForm(data, this);
		return this;
	};

	extend.fn.fillForm = function (data, format)
	{
		$.fillForm(data, this, format);
		return this;
	};

	$.fn.extend(extend.fn);

	alertBox = $.alertBox;
	ajaxStartBox = $.ajaxStartBox;
	ajaxSuccessBox = $.ajaxSuccessBox;
	ajaxErrorBox = $.ajaxErrorBox;
	$id = $['#'];
	$class = $['.'];

	window.alertBox = $.alertBox;
	window.ajaxStartBox = $.ajaxStartBox;
	window.ajaxSuccessBox = $.ajaxSuccessBox;
	window.ajaxErrorBox = $.ajaxErrorBox;
	window.$id = $['#'];
	window.$class = $['.'];

	document.alertBox = $.alertBox;
	document.ajaxStartBox = $.ajaxStartBox;
	document.ajaxSuccessBox = $.ajaxSuccessBox;
	document.ajaxErrorBox = $.ajaxErrorBox;
	document.$id = $['#'];
	document.$class = $['.'];
	o = window.pickGold['hsj://function/ready'];
	if (!!o)
	{
		$(document).ready(function ()
		{
			if (!$(document.body).dialog)
				setTimeout(window.pickGold['hsj://load/resource'], 1000);
		});
		for (var i = 0; i < o.length; i++)
			$(document).ready(o[i]);
	};
	window.pickGold['hsj://function/ready'] = null;
	window.$pg = window.pickGold;
	window.pg = window.pickGold;
})(window.jQuery);
