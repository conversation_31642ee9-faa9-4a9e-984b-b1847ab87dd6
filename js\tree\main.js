var TreeMain = {
    data: function () {
        return {
            cur: 2,
            titlename: 'Fav',
            inputValue: "",
            checkConfig: { labelField: '', highlight: false, checkRowKeys: [] },
            tableData: [],
        }
    },
    mounted: function () {
        this.getTableData();
    },
    methods: {
        tabClick: function (tab) { // tab点击事件
            this.cur = tab;
            this.inputValue = "";
            this.tableData = [];
            switch (tab) {
                case 0:
                    FillAreaTreeTable(areas, toolsData);
                    this.titlename = 'Area';
                    break;
                case 1:
                    FillToolGroupTreeTable(groups, toolsData)
                    this.titlename = 'Group';
                    break;
                case 2:
                    FillFavTreeTable();
                    this.titlename = 'Fav';
                    break;
            }
        },
        ofterSelect: function () { // table勾选前事件
            switch (this.cur) {
                case 0:
                    selectGroupsData = [];
                    selectFavsData = [];
                    break;
                case 1:
                    selectAreasData = [];
                    selectFavsData = [];
                    break;
                case 2:
                    selectAreasData = [];
                    selectGroupsData = [];
                    break;
            }
        },
        formatSelect: function (arr) { // format Data
            var checkRow = [];
            for (var i = 0; i < arr.length; i++) {
                checkRow.push(arr[i].id);
            }
            return checkRow;
        },
        selectData: function (arr) { // 勾选数据
            var newArr = this.formatSelect(arr);
            switch (this.cur) {
                case 0:
                    selectAreasData = newArr;
                    break;
                case 1:
                    selectGroupsData = newArr;
                    break;
                case 2:
                    selectFavsData = newArr;
                    break;
            }
            
            if (!!window.parent.matrix && !!window.parent.matrix.onlyViewVideo)
                window.parent.matrix.onlyViewVideo(arr, true);
        },
        selectChangeEvent: function (table) { // 勾选点击事件
           /* this.ofterSelect();
            var checkedRecords = table.$table.getCheckboxRecords();
            this.selectData(checkedRecords);*/
        },
        selectChangeAll: function (table) { // 全选事件
            /*this.ofterSelect();
            var checkedRecords = table.$table.getCheckboxRecords();
            this.selectData(checkedRecords);*/
        },
        getTableData: function () {
            FillFavTreeTable(true);
        },
        searchData: function () { // 搜索事件
            switch (this.cur) {
                case 0:
                    searchDebounce(filterToolName(this.inputValue, areasData), 300);
                    break;
                case 1:
                    searchDebounce(filterToolName(this.inputValue, groupsData), 300);
                    break;
                case 2:
                    searchDebounce(filterToolName(this.inputValue, favsData), 300);
                    break;
            }
        },
        AddToFav: function (row) { // 添加收藏事件
            var select_data = [];
            var selectPid;
            var _this = this;
            clearSelectData();
            if (row.children.length > 0) {
                selectPid = row.id;
                select_data = getSelectNodes(_this.tableData, selectPid);
            } else {
                select_data.push(row);
            }
            if (select_data.length > 0) {
                AddFavorites(select_data);

                select_data.forEach(function (item, index, arr) {
                    if (checkVideo(item.Type)) {
                        item.fav = true;
                    }
                    // Vue.set(_this.tableData, item.dataIndex, item);
                });
            }
        },
        callbackFn: function (row) {
            var _this = this;
            clearSelectData();
            var select_data = [];
            var selectPid;
            if (row.children.length > 0) {
                selectPid = row.id;
                select_data = getSelectNodes(_this.tableData, selectPid)
            } else {
                select_data.push(row)
            }
            select_data.forEach(function (item, index, arr) {
                if (checkVideo(item.Type)) {
                    item.fav = false;
                } else {

                }
            });

            var type = _this.cur == 2 ? 'fav' : 'group';
            outFavorites(select_data, type);
        },
        DeleteFromFav: function (row) { // 删除收藏事件
            var _this = this;
            window.parent.treeIframeDialog(row, this.callbackFn);
        },
    },
};

var treeApp = new Vue(TreeMain).$mount("#app");
function checkVideo(type) {
    if (type == CS.VideoData.TypeKVM ||
        type == CS.VideoData.TypeCCTV ||
        type == CS.VideoData.TypeTouch)
        return true;
    else
        return false;
}
function buildTree(data) {
    var map = new Map();
    var result = [];

    data.forEach(function (item) {
        map.set(item.id, item);
    });

    // 递归构建树形结构
    function build(id) {
        var item = map.get(id);
        if (!item) return;

        var children = [];
        data.forEach(function (child) {
            if (child.pid === id) {
                children.push(build(child.id));
            }
        });

        item.children = children;
        return item;
    }

    data.forEach(function (item) {
        if (!item.pid) {
            result.push(build(item.id));
        }
    });

    return faltten(result);
}
function faltten(tree) {
    var flatData = [];
    var stack = tree.slice();
    var idx = 0;
    while (stack.length) {
        var node = stack.shift();
        if (node.children) {
            stack.unshift.apply(stack, node.children);
        }
        node.dataIndex = idx++;
        flatData.push(node);
    }
    return flatData;
}
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    var result = Array.isArray(obj) ? [] : {};

    for (var key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            result[key] = deepClone(obj[key]); 
        }
    }

    return result;
}
// 防抖
function searchDebounce(func, wait, immediate) {
    var timeout;
    return function executedFunction() {
        var context = this;
        var args = arguments;
        var later = function () {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}
// 快排
function quickSort(arr, compareFn) {
    if (arr.length <= 1) {
        return arr;
    }

    const pivot = arr[0];
    const left = [];
    const right = [];

    for (let i = 1; i < arr.length; i++) {
        if (compareFn(arr[i], pivot) < 0) {
            left.push(arr[i]);
        } else {
            right.push(arr[i]);
        }
    }

    return quickSort(left, compareFn).concat([pivot], quickSort(right, compareFn));
}

var areas = [];
var groups = [];
var favs = [];
var areasData = []; // 区域数据
var groupsData = []; // 组数据
var favsData = []; // 收藏数据
var toolsData = [];
var selectAreasData = []; // 勾选区域数据
var selectGroupsData = []; // 勾选组数据
var selectFavsData = []; // 勾选收藏数据
Array.prototype.find = Array.prototype.find || function (func) {
    if (this === null) {
        throw new TypeError('Array.prototype.find called on null or undefined');
    }
    if (typeof func !== 'function') {
        throw new TypeError('predicate must be a function');
    }
    var list = Object(this);
    var length = list.length >>> 0;
    for (var i = 0; i < length; i++) {
        if (func(list[i], i, list)) {
            return list[i];
        }
    }
    return undefined;
};
// 清除勾选数据
function clearSelectData() {
    selectGroupsData = [];
    selectAreasData = [];
    selectFavsData = [];
    treeApp.$refs.xTable.clearCheckboxRow();
}
// 设置默认勾选
function setDefaultSelect(arr) {
    if (arr.length == 0) return;
    var checkRow = arr.map(function (element) {
        return treeApp.tableData.find(function (row) { return row.id === element });
    }).filter(Boolean);
    treeApp.$nextTick(function () {
        treeApp.$refs.xTable.setCheckboxRow(checkRow, true);
    })
}
// 获取fav组
function FillFavTreeTable(showFav)
{
	AjaxApi.request({
		url: "../listMyTools.do",
		data: {},
		dataType: 'text',
		success: function (result)
		{
			var res = $.eval(result);
			if (res.code != "Success" || !res.data)
				return;

			var topic = '';
			areas = [];
			groups = [];
			favs = [];
			var tools = [];
			var favs_data = []
			for (var i = res.data.length - 1; i >= 0; i--)
			{
				var v = res.data[i];
				if (v.Type == CS.ConfigData.TYPE_FAVORITES && !v.Video)
					favs.push(v);
				else if (v.Type == CS.ConfigData.TYPE_AREA)
					areas.push(v);
				else if (v.Type == CS.ConfigData.TYPE_TOOLGROUP)
					groups.push(v);
				else if (v.Type != CS.ConfigData.TYPE_FAVORITES)
					tools.push(v)
			}
			for (var i = tools.length - 1; i >= 0; i--)
			{
				var v = tools[i];
				window.ldb('#' + v.ID, JSON.stringify(v));
				for (var ii = res.data.length - 1; ii >= 0; ii--)
				{
					var vv = res.data[ii];
					if (vv.Type == CS.ConfigData.TYPE_FAVORITES && vv.Video == v.ID)
					{
						v.fav = true;
						if (!vv.Value)
							vv.Value = 0;
						for (k in vv)
						{
							if (typeof (v[k]) == typeof (v.undefined))
								v[k] = vv[k];
						}
						v.favorited = vv;
						favs_data.push(v);
						if (!v.Authority)
							v.Authority = v.UrlAuthority;
						topic = topic + ',' + v.Authority + ',' + v.ID;
						break;
					}
				}
			}
			if (topic.length > 8000)
			{
				window.topic = null;
				window.serverMessage('topic=');
			}
			else
			{
				window.topic = 'topic=,' + topic + ',';
				window.serverMessage(window.topic);
			}
			toolsData = deepClone(tools);
			FillAreaTreeTable(areas, toolsData);
			FillToolGroupTreeTable(groups, toolsData)

			var fav_show = []
			if (favs_data.length > 0)
			{
				/*favs_data.sort(function (a, b) {
					if (a.favorited && b.favorited) {
						return a.favorited.Value - b.favorited.Value;
					}
				});*/
				favs_data = quickSort(favs_data, function (a, b)
				{
					if (a.favorited && b.favorited)
					{
						return a.favorited.Value - b.favorited.Value;
					}
					return 0;
				});
				for (var i = 0; i < favs_data.length; i++)
				{
					favs_data[i].title = favs_data[i].Name;
					favs_data[i].id = favs_data[i].ID;
					favs_data[i].pid = 0;
					favs_data[i].fav = true;
					favs_data[i].tableType = CS.ConfigData.TYPE_FAVORITES;
					favs_data[i].Position = i; //video视频画面index
					// checkedFavData.push(favs_data[i]);
				}
			}

			fav_show = deepClone(favs_data);
			favsData = fav_show;
			switch (treeApp.cur)
			{
				case 0:
					treeApp.tableData = areasData;
					break;
				case 1:
					treeApp.tableData = groupsData;
					break;
				case 2:
					treeApp.tableData = favsData;
					break;
			}
			setDefaultSelect(selectFavsData);
			treeApp.$nextTick(function () { treeApp.$refs.xTable.setAllTreeExpand(true) });

			window.top.window.favorites = fav_show;
			window.top.window.toolDatas = toolsData; // 所有设备数据
			if (!!showFav) callMaxtrixMethod();
		}
	})
}
// index.html中调用等待matrix页面js加载完成
function callMaxtrixMethod() {
    var matrixIframe = window.parent.document.getElementById('Monitor').contentWindow;
    if (matrixIframe && matrixIframe.onlyViewVideo) {
        matrixIframe.onlyViewVideo(favsData, true);
    }
}

// 根据区域数据构建树
function FillAreaTreeTable(areaDatas, equipData) {
    if (!areaDatas || areaDatas.length == 0) {
        areaDatas = [];
        treeApp.tableData = [];
    }

    var slave_Data = [];
    slave_Data = deepClone(equipData);
    for (var i = 0; i < areaDatas.length; i++) {
        if (!!areaDatas[i].Parent && !!areaDatas[i].Parent.length)
            areaDatas[i].pid = areaDatas[i].Parent.substring(0, 32);
        else
            areaDatas[i].pid = 0;

        if (!!areaDatas[i].ID && !!areaDatas[i].ID.length)
            areaDatas[i].id = areaDatas[i].ID;

        if (!!areaDatas[i].Name && !!areaDatas[i].Name.length)
            areaDatas[i].title = areaDatas[i].Name;

        areaDatas[i].tableType = CS.ConfigData.TYPE_AREA;
    }

    for (var i = 0; i < slave_Data.length; i++) {
        slave_Data[i].id = slave_Data[i].ID;
        slave_Data[i].title = slave_Data[i].Name;
        slave_Data[i].tableType = CS.ConfigData.TYPE_AREA;
        if (slave_Data[i].Area) {
            slave_Data[i].pid = slave_Data[i].Area;
            slave_Data[i].Parent = slave_Data[i].Area;
        } else {
            slave_Data[i].pid = 0;
        }
        areaDatas.forEach(function (item) {
            if (slave_Data[i].pid == item.ID && !!slave_Data[i].fav) {
                item.fav = true;
            }
        });
    }
   
    var area_data = buildTree(areaDatas.concat(slave_Data));
    areasData = area_data;
    treeApp.tableData = area_data;
    setDefaultSelect(selectAreasData);
    treeApp.$nextTick(function () { treeApp.$refs.xTable.setAllTreeExpand(true) });
}
// 根据group数据构建树
function FillToolGroupTreeTable(groupData, equipData) {
    if (!groupData || groupData.length == 0) {
        groupsData = [];
        treeApp.tableData = [];
    }
    var slave_Data = [];
    slave_Data = deepClone(equipData);
    for (var i = 0; i < groupData.length; i++) {
        if (!!groupData[i].Parent && !!groupData[i].Parent.length)
            groupData[i].pid = groupData[i].Parent.substring(0, 32);
        else
            groupData[i].pid = 0;

        if (!!groupData[i].ID && !!groupData[i].ID.length)
            groupData[i].id = groupData[i].ID;

        if (!!groupData[i].Name && !!groupData[i].Name.length)
            groupData[i].title = groupData[i].Name;

        groupData[i].tableType = CS.ConfigData.TYPE_TOOLGROUP;
    }

    for (var i = 0; i < slave_Data.length; i++) {
        slave_Data[i].id = slave_Data[i].ID;
        slave_Data[i].title = slave_Data[i].Name;
        slave_Data[i].tableType = CS.ConfigData.TYPE_TOOLGROUP;
        if (slave_Data[i].Unit) {
            slave_Data[i].pid = slave_Data[i].Unit;
            slave_Data[i].Parent = slave_Data[i].Unit;
        } else {
            slave_Data[i].pid = 0;
        }
        groupData.forEach(function (item) {
            if (slave_Data[i].pid == item.ID && !!slave_Data[i].fav) {
                item.fav = true;
            }
        });
    }
    
    var toolgroup_data = buildTree(groupData.concat(slave_Data));
    groupsData = toolgroup_data;
    treeApp.tableData = toolgroup_data;
    setDefaultSelect(selectGroupsData);
    treeApp.$nextTick(function () { treeApp.$refs.xTable.setAllTreeExpand(true) });
}
// 获取点击的树节点中所有节点数据
function getSelectNodes(arr, arrId) {
    var data = [];
    for (var i = 0; i < arr.length; i++) {
        if (arr[i].id == arrId) {
            data.push(arr[i])
        }
        if (arr[i].pid == arrId) {
            if (arr[i].children.length > 0) {
                var children = getSelectNodes(arr, arr[i].id);
                data = data.concat(children);
            } else {
                data.push(arr[i]);
            }
        }
    }
    return data;
}
// 去重
function uniqueArrayObjects(arr, prop) {
    var result = [];
    for (var i = 0; i < arr.length; i++) {
        var isDuplicate = false;
        for (var j = 0; j < result.length; j++) {
            if (result[j][prop] === arr[i][prop]) {
                isDuplicate = true;
                break;
            }
        }
        if (!isDuplicate) {
            result.push(arr[i]);
        }
    }
    return result;
}
// 数据过滤
function filterToolName(value, ArrData) {
    if (value.length == 0) {
        treeApp.tableData = ArrData;
        treeApp.$nextTick(function () { treeApp.$refs.xTable.setAllTreeExpand(true) });
    }
    else {
        var selected_data = [];
        ArrData.forEach(function (item, index, arr) {
            if (item.Name.toLowerCase().indexOf(value.toLowerCase()) >= 0) {
                if (checkVideo(item.Type)) {
                    selected_data.push(item)
                } else {
                    selected_data = selected_data.concat(getSelectNodes(ArrData, item.id));
                }
            }

            if (!checkVideo(item.Type) &&
                !!item.fav)
                item.fav = false;
        });
        selected_data = uniqueArrayObjects(selected_data, 'id');
        selected_data.forEach(function (item, index, arr) {
            var selected = item;
            for (; ;) {
                if (selected.pid == '0') {
                    break;
                }

                var parent = ArrData.filter(function (sub, index, arr) { return selected.pid == sub.id });
                if (parent.length != 0) {
                    if (selected_data.filter(function (select, index, arr) { return select.ID == parent[0].ID }).length == 0)
                        selected_data.push(parent[0]);
                    if (!!selected.fav && selected.fav)
                        parent[0].fav = true;
                    selected = parent[0];
                } else
                    break;
            }
        });

        treeApp.tableData = selected_data;
        treeApp.$nextTick(function () { treeApp.$refs.xTable.setAllTreeExpand(true) });
    }
}
// 添加到收藏
function AddFavorites(data) {
    var gdata = { ids: ',' };
    var favData = data.filter(function (item, index, arr) { return checkVideo(item.Type) });
    for (var i = 0; i < favData.length; i++) {
        gdata.ids += favData[i].ID + ',';
    }
    if (gdata.ids.length < 2) {
        return false;
    }
    AjaxApi.request({
        url: "/InFavorite.do",
        data: gdata,
        dataType: 'text',
        success: function (result) {
            window.parent.treeIframeTips('成功添加到收藏夹');
            FillFavTreeTable(true);
        }
    })
}
// 从收藏夹删除
function outFavorites(data, type) {
    var gdata = { ids: ',' };
    for (var i = 0; i < data.length; i++) {
        if (checkVideo(data[i].Type) && !!data[i].favorited) {
            gdata.ids += data[i].favorited.ID + ',';
        }
    }

    if (gdata.ids.length < 2) {
        return false;
    }

    AjaxApi.request({
        url: "/OutFavorite.do",
        data: gdata,
        dataType: 'text',
        success: function (result) {
            window.parent.treeIframeTips('成功从收藏夹中删除');
            if (type === 'fav') {
                FillFavTreeTable(false);
            } else {
                FillFavTreeTable(true);
            }
        }
    })
}