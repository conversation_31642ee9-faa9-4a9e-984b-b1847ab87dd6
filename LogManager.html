﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title>系统日志管理页面</title>
	<link href="lib/layui/css/layui.css" rel="stylesheet" />
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript">
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate', 'upload', 'excel'], function () {
			$ = layui.$
				, laydate = layui.laydate
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, element = layui.element
				, excel = layui.excel
				, upload = layui.upload
				, laypage = layui.laypage;
			tableId = 'logList';
			tableExId = 'logListEx';
			limitPageCount = 12;
			var days = 30;
			var gData;
			pageCount = 1;
			table.render({
				elem: '#' + tableId
				, toolbar: '#toolbar'
				, id: tableId
				, title: 'log数据表'
				, defaultToolbar: []
				, limit: limitPageCount
				, cols: [columnsName()]
				, data: []
			});
			table.render({
				elem: '#' + tableExId
				, id: tableExId
				, title: 'log数据表'
				, defaultToolbar: []
				, limit: limitPageCount
				, cols: [columnsName()]
				, data: []
			});
			laydate.render({
				elem: '#startTime',
				format: "yyyy-MM-dd",
				type: 'date',
				range: true,
				done: function (value, date, endDate) {
					$('#startTime').val(value);
				}
			});
			laypageRender();
			form.on('submit(selectBtnVideo)', function (obj) {
				laypageRender();
				return false;
			})
			table.on('toolbar(logList)', function (obj) {
				var checkStatus = table.checkStatus(obj.config.id);
				switch (obj.event) {
					//自定义头工具栏右侧图标 - 提示
					case 'ExportData':
						{
							exportExcelData();
							break;
						}
				}
			})
			function exportExcelData() {
				gData.model = 'export';
				$.ajax({
					url: '../Log/List.do',
					method: 'get',
					cache: false,
					data: gData,
					dataType: 'text',
					success: function (result) {
						var res = JSON.parse(result);
						formateData(res);
						//加载table数据
						table.reload(tableExId, {
							data: res.data,
							limit: res.data.length,
							page: false,
							done: function (res, curr, count) {
								exportDIYStyleExcel(tableExId, excel, '日志管理数据.xlsx');
							}
						});
					}
				})
			}
			function laypageRender(data, first) {
				if (!data) {
					data = form.val('logForm');
					data.limitCount = limitPageCount;
					data.pageCount = 0;
					data.limitCount = limitPageCount;
					data.pageCount = 0;
					var addTime = 24 * 60 * 60 * 1000 - 1;
					if (data.startTime.length == 0) {
						var dataTime = new Date();
						var endTime = new Date();
						var startTime = dataTime.setDate(dataTime.getDate() - days);
						data.startTime = startTime;
						data.endTime = endTime.getTime();
					}
					else {
						var startIndex = 10;
						var time = data.startTime;
						data.startTime = parseDate(time.substring(0, startIndex)).toUTC().getTime();
						data.endTime = parseDate(time.substring(startIndex + 3)).toUTC().getTime();
					}
					if (data.startTime > data.endTime) {
						layer.alert(lang('请选择有效的时间段！！！'), { btn: [lang('确定')], title: lang('提示') });
						return false;
					}
					data.endTime = data.endTime + addTime;
				}
				gData = data;
				var ajax = {};
				ajax.url = '../Log/List.do';
				ajax.data = data;
				ajax.dataType = 'text';
				ajax.cache = false;
				ajax.success = function (result) {
					var res = $.eval(result);
					formateData(res);
					laypage.render({
						elem: 'footer'
						, count: res.count
						, theme: '#FF5722'
						, curr: data.pageCount + 1
						, limit: limitPageCount
						, prev: lang('上一页')
						, next: lang('下一页')
						, layout: ['count', 'prev', 'page', 'next', 'skip']
						, jump: function (obj, first) {
							if (first) {
								table.reload(tableId, {
									data: res.data,
									page: false
								});
							}
							else {
								data.pageCount = obj.curr - 1;
								laypageRender(data);
							}
							laypageEN();//翻页翻译成英文方法
						}
					});
				}
				ajax.error = onAjaxError;
				$.ajax(ajax);
			}
			function formateData(res) {
				for (var i = 0; i < res.data.length; i++) {
					res.data[i].xuhao = i + 1;
				}
			}
			function columnsName() {
				var columnsArray = [];
				var columnsNames = [
					{ title: "序号", field: "xuhao", width: 65 }
					, { title: "ID", field: "ID", hide: true }
					, { title: "名称", field: "Name" }
					, { title: "类型", field: "Type" }
					, { title: "Data", field: "Data" }
					, { title: "Data1", field: "Data1" }
					, { title: "Data2", field: "Data2" }
					, { title: "Data3", field: "Data3" }
					, { title: "Data4", field: "Data4" }
					, { title: "Data5", field: "Data5" }
					, { title: "Data6", field: "Data6" }
					, { title: "Data7", field: "Data7" }
					, { title: "Data8", field: "Data8" }
					, { title: "Data9", field: "Data9" }
					, { title: "创建时间", field: "Time", templet: templet, width: 160 }
					, { title: "创建者", field: "User" }
				];
				for (var i = 0; i < columnsNames.length; i++) {
					var o = {
						title: columnsNames[i].title,
						field: columnsNames[i].field,
						align: 'center',
						event: 'singleclick',
						templet: columnsNames[i].templet,
						width: columnsNames[i].width,
						hide: columnsNames[i].hide ? columnsNames[i].hide : false
					};
					columnsArray.push(o);
				}
				var columnsLast = {
					fixed: 'right',
					title: "操作",
					toolbar: "#barDemo",
					align: 'center',
					hide: !window.allow.StationBC
				}
				return columnsArray;
			}
		});

	</script>
</head>
<body>
	<form class="layui-form" id="logForm" lay-filter="logForm">

		<div class="layui-form-item">
			<fieldset class="layui-elem-field">
				<legend><span>条件筛选</span></legend>
				<div class="layui-field-box" style="text-align:center;">
					<div class="layui-inline">
						<label class="layui-form-label">警报时间:</label>
					</div>
					<div class="layui-inline" style="width:200px;">
						<input placeholder="-" readonly="readonly" class="layui-input" id="startTime" name="startTime" />
					</div>
					<div class="layui-inline">
						<button class="layui-btn" type="submit" lay-submit="" lay-filter="selectBtnVideo">查询</button>
					</div>
					<div class="layui-inline">
						<input type="button" id="clear" class="layui-btn" value="重置" />
					</div>
				</div>
			</fieldset>
		</div>
		<div class="layui-form-item">
			<fieldset class="layui-elem-field">
				<legend><span>数据</span></legend>
				<div class="layui-field-box" style="width:98.5vw;">
					<table id="logList" lay-filter="logList" class="layui-table"></table>
				</div>
			</fieldset>
		</div>
		<div class="layui-form-item" style="display:none;">
			<table id="logListEx" lay-filter="logListEx" class="layui-table"></table>
		</div>
		<div id="footer" style="text-align:center;"></div>
	</form>
	<script type="text/html" id="toolbar">
		<div class="layui-btn-container" style="float:left;">
			<div class="layui-inline">
				<input type="button" class="layui-btn layui-btn-sm" lay-event="ExportData" value="导出">
			</div>
		</div>
	</script>
</body>
</html>