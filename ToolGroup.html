﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>机台组页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<link rel="stylesheet" href="lib/layui/treetable/treetable.css" />
	<style>
		#addToolGroup .layui-inline {
			margin-right: 0px;
		}

		#addToolGroup .edit {
			width: 200px;
		}

		.borderStyle {
			border: 1px solid red;
		}

		.ew-tree-table .ew-tree-table-tool {
			height: 50px;
		}

		.ew-tree-table-cell-content:hover {
			color: #666 !important;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/excel.js"></script>
	<script src="js/request.js"></script>
	<script type="text/javascript">
		var groupTreeTable;
		layui.config({ base: 'lib/layui/' });
		layui.extend({ treeTable: './treeTable-lay' });
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel', 'upload', 'dropdown', 'treeTable'], function ()
		{
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, upload = layui.upload
				, excel = layui.excel
				, treetable = layui.treeTable;
			lang.call(this, $);
			//创建Table的列名
			RenderTreeTable([]);

			FillDataTable();
			//头工具栏事件
			treetable.on('toolbar(toolgrouplist)', function (obj)
			{
				switch (obj.event)
				{
					case 'isAdd':
						index = layer.open({
							title: lang('增加机台组')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addToolGroup')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function ()
							{
								//setRoleSelect();
								$($('p')[0]).hide();
							}
							, btn1: function (index)
							{
								var data = form.val("form-addToolGroup");
								if (data.toolgroupname.trim().length == 0)
								{
									layer.alert(lang('机台组名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								AjaxApi.request({
									url: "/ToolGroup/AddToolGroup.do",
									data: data,
									dataType: 'text',
									success: function (result)
									{
										var res = $.eval(result);
										if (res.code == "Success")
										{
											FillDataTable();
											layer.alert(lang('保存成功,是否继续?'), {
												title: lang('增加机台组')
												, btn: [lang('继续'), lang('取消')]
												, btnAlign: 'c'
												, btn1: function (subIndex)
												{
													$('#addToolGroup')[0].reset();
													layer.close(subIndex);
													FillDataTable();
												}
												, btn2: function (subIndex)
												{
													$('#addToolGroup')[0].reset();
													layer.close(subIndex);
													layer.close(index);
													FillDataTable();
												}
											});
										}
										else if (res.msg == "Exist")
										{
											layer.alert(lang('机台组已存在！'), { btn: [lang('确定')], title: lang("增加机台组"), });
										}
										else
										{
											layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加机台组"), });
										}
									},
									error: function (r)
									{
										layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加机台组"), });
									}
								})
							}
							, end: function ()
							{
								$('#addToolGroup')[0].reset();
								$('#addToolGroup').css('display', 'none');
							}
						});
						break;
					case 'Unfold':
						{
							$('#FoldBtn').css('display', 'block');
							$('#UnfoldBtn').css('display', 'none');
							groupTreeTable.expandAll();
							break;
						}
					case 'Fold':
						{
							$('#FoldBtn').css('display', 'none');
							$('#UnfoldBtn').css('display', 'block');
							groupTreeTable.foldAll();
							break;
						}
				};
			});

			//监听行工具事件
			treetable.on('tool(toolgrouplist)', function (obj)
			{
				var data = obj.data;
				if (obj.event === 'del')
				{
					Delete(data);
				}
				else if (obj.event == 'add')
				{
					AddSubToolGroup(data);
				}
				else if (obj.event === 'edit')
				{
					if (data.Role == '')
					{
						layer.alert(lang('此用户禁止修改！'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}

					$($('p')[0]).show();
					var oldrole = data.Role;
					var des = data.Description;
					var index1 = layer.open({
						title: lang('修改机台组')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#addToolGroup')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function ()
						{
							form.val('form-addToolGroup', {
								"toolgroupname": data.Name,
								"description": data.Note
							});

							$('#addToolGroup input[name=toolgroupname]').attr('disabled', true);
							$('#addToolGroup input[name=toolgroupname]').addClass('layui-disabled');
						}
						, btn1: function (value, index)
						{
							var data1 = form.val("form-addToolGroup");
							if (data1.toolgroupname.length == 0)
							{
								layer.alert(lang('机台组不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}

							if (data1.description == data.Note)
							{
								return false;
							}

							data1["ID"] = data.ID;
							AjaxApi.request({
								url: "/ToolGroup/EditToolGroup.do",
								data: data1,
								dataType: 'text',
								success: function (result)
								{
									var res = $.eval(result);
									if (res.code == "Success")
									{
										layer.msg(lang('修改成功'), { icon: 1 });
										layer.close(index1);
										FillDataTable();
									}
									else
									{
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
							})
						}
						, end: function ()
						{
							$('#addToolGroup')[0].reset();
							$('#addToolGroup').css('display', 'none');
							$('#addToolGroup input[name=toolgroupname]').attr('disabled', false);
							$('#addToolGroup input[name=toolgroupname]').removeClass('layui-disabled');
						}
					});
				}
				else if (obj.event == 'singleclick')
				{
					var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
					if (checkCell.length > 0)
					{
						checkCell.click();
					}
				}
			});

			form.on('submit(toolgroupSearch)', function (data)
			{
				var data = form.val('form-select');
				groupTreeTable.filterData(data.toolgroupname);
				return false;
			});
			$('#resetBtn').on("click", function ()
			{
				document.getElementById('form-select').reset();
				FillDataTable();
			})
		});

		function Delete(data)
		{
			var ids = ',';
			var names = ',';
			ids += data.ID;
			names += data.Name;

			if (data.children && data.children.length !== 0)
			{
				for (var i = 0; i < data.children.length; i++)
				{
					if (i == data.children.length - 1)
					{
						ids += "," + data.children[i].ID;
						names += "," + data.children[i].Name;
					} else
					{
						ids += "," + data.children[i].ID + ",";
						names += "," + data.children[i].Name + ",";
					}
				}
			}
			layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
			{
				AjaxApi.request({
					url: "/ToolGroup/DeleteToolGroup.do",
					data: { ID: ids, names: names },
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.code == "Success")
						{
							layer.msg(lang('删除成功'), { icon: 1 });
							FillDataTable();
						}
						else
						{
							layer.msg(lang('删除失败'), { icon: 2 });
						}
					},
					error: function (r)
					{
						layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
					}
				})
				layer.close(index);
			});
		}

		function AddSubToolGroup(data)
		{
			var index1 = layer.open({
				title: lang('增加子机台组')
				, type: 1
				, resize: false
				, id: 'edit'
				, content: $('#addToolGroup')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
				, success: function ()
				{
					form.val('form-addToolGroup', {
						"maintoolgroup": data.Name
					});

					$('#RootDiv').show();
				}
				, btn1: function (value, index)
				{
					var data1 = form.val("form-addToolGroup");
					if (data1.toolgroupname.length == 0)
					{
						layer.alert(lang('机台组不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
						return false;
					}

					data1['parent'] = data.ID;
					if (!data.Path)
						data1['root'] = data.ID;
					else
						data1['root'] = data.Path;

					AjaxApi.request({
						url: "/ToolGroup/AddSubToolGroup.do",
						data: data1,
						dataType: 'text',
						success: function (result)
						{
							var res = $.eval(result);
							if (res.code == "Success")
							{
								layer.msg(lang('添加成功'), { icon: 1 });
								layer.close(index1);
								FillDataTable();
							}
							else if (res.code == 'Exist')
							{
								layer.alert(lang('已存在') + data.Name, { btn: [lang('确定')], title: lang('提示') });
								$('#addToolGroup')[0].reset();
								$('#addToolGroup').css('display', 'none');
								$('#RootDiv').hide();
							}
							else
							{
								layer.alert(lang('添加失败！'), { btn: [lang('确定')], title: lang('提示') });
								$('#addToolGroup')[0].reset();
								$('#addToolGroup').css('display', 'none');
								$('#RootDiv').hide();
							}
						}
					})
				}
				, end: function ()
				{
					$('#addToolGroup')[0].reset();
					$('#addToolGroup').css('display', 'none');
					$('#RootDiv').hide();
				}
			});
		}

		var username = '';
		function FillDataTable()
		{
			if (window.top)
			{
				window.top.FillFavTreeTable();
			}

			AjaxApi.request({
				url: '../ListConfig.do?Type=' + window.ConfigData.TYPE_TOOLGROUP + '&Flags=0',
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					username = top.login.Name;
					for (var i = 0; i < res.data.length; i++)
					{
						$.parseUrl(res.data[i].Data, res.data[i]);
						$.parseUrl(res.data[i].Data9, res.data[i]);

						if (!!res.data[i].Parent && !!res.data[i].Parent.length)
							res.data[i].Parent = res.data[i].Parent.substring(0, 32);

						res.data[i].xuhao = i + 1;
					}

					groupTreeTable.refresh(res.data);
					groupTreeTable.expandAll('#toolgrouplist');
				}
			})
		}

		function show()
		{
			if ($('#pwd').val().length == 0)
				$($('p')[0]).css("visibility", "visible");
			else
				$($('p')[0]).css("visibility", "hidden");
		}

		var treeNodeClose = false;

		function foldBtnStatus()
		{
			if (treeNodeClose)
			{
				$('#FoldBtn').css('display', 'none');
				$('#UnfoldBtn').css('display', 'block');
			} else
			{
				$('#FoldBtn').css('display', 'block');
				$('#UnfoldBtn').css('display', 'none');
			}
		}
		function RenderTreeTable(data)
		{
			if (!!treetable)
			{
				groupTreeTable = treetable.render(
					{
						elem: '#toolgrouplist',
						height: 'full-145',
						toolbar: '#toolgrouptoolbar',
						defaultToolbar: [],
						data: data,
						sort: true,
						tree: {
							iconIndex: 1,
							idName: 'ID',
							pidName: 'Parent',
							isPidData: true,
						},
						cols: [
							{ title: lang("序号"), type: "numbers", width: 80 }
							, { title: lang("机台组名称"), field: "Name", align: "center" }
							, { title: lang('更新者'), field: 'EditUser', align: "center" }
							, { title: lang("更新时间"), field: "Edit", templet: templet, align: "center" }
							, { title: lang("描述"), field: "Note", align: "center" }
							, { fixed: 'right', width: 270, title: lang("操作"), align: "center", toolbar: "#operation", hide: !hasButtonPermission('ToolGroupOptionSet') }
						],
						done: function (res, curr, count)
						{
							foldBtnStatus();
						}
					}
				);

				if (!hasButtonPermission('ToolGroupOptionSet'))
					$('#divBC').hide();
			}
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>机台组设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">机台组名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="toolgroupname" autocomplete="off" placeholder="请输入机台组名称"
							   oninput="cleanSpelChar(this)">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="toolgroupSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button id="resetBtn" type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="toolgrouplist" lay-filter="toolgrouptable"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="userListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="toolgrouptoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="Unfold" id="UnfoldBtn" style="display: none;">{{lang('全部展开')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="Fold" id="FoldBtn">{{lang('全部折叠')}}</button>
					</div>
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="operation">
			{{#  if(!d.Parent){ }}
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventAdd" lay-event="add">{{lang('增加子机台组')}}</a>
			{{# } }}
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改机台组')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
		</script>
		<script type="text/html" id="checkedOne">
			<input type="checkbox" name="like1[write]" lay-skin="primary">
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="addToolGroup" class="layui-form" lay-filter="form-addToolGroup" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item" id="RootDiv" style="display:none;">
			<div class="layui-inline">
				<label class="layui-form-label">根区域名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input layui-disabled" name="maintoolgroup" autocomplete="off" placeholder="请输入区域名称"
					   oninput="cleanSpelChar(this)" disabled="disabled">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">机台组名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="toolgroupname" autocomplete="off" placeholder="请输入机台组名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">描述</label>
			</div>
			<div class="layui-inline edit">
				<textarea type="text" class="layui-textarea" style="height: 200px; width:200px; white-space: pre-wrap;overflow:auto;" name="description" autocomplete="off" placeholder="请输入少于200字的描述"
						  oninput="limitTextAreaCount(this)"></textarea>
			</div>
		</div>
	</form>

</body>
</html>