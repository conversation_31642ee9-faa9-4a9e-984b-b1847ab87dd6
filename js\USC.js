﻿/* for USC 合肥新汇成微电子股份有限公司 */
/*<!--.css 以下是有效的 CSS 代码，切勿移除 **************************
.god, .god-a-t, .god-a-v, .god-a-vm, .god-a-vl, .god-a-vh, .god-a-s, .god-v-ra, .god-v-rw, .god-v-rs, .god-v-sm, .god-v-a, .god-a-m, .god-e-mi, .usc { display: none !important; }
/*--><!--.js 以下是 JS 代码 ***************************************** */
if (!!window.godCSS)
	window.CS.css = window.godCSS('.god, .god-a-t, .god-a-v, .god-a-vm, .god-a-vl, .god-a-vh, .god-a-s, .god-v-ra, .god-v-rw, .god-v-rs, .god-v-sm, .god-v-a, .god-a-m, .god-e-mi, .usc');
window.CS.menus =
	[
		{
			'key': 'ToolManagement', 'text': '机台管理', 'child':
				[
					{ 'key': 'AreaSetup', 'text': '区域设置', 'child': [{ 'key': 'AreaOptionSet', 'text': '增删改' }] },
					{ 'key': 'ToolGroupSetup', 'text': '机台组设置', 'child': [{ 'key': 'ToolGroupOptionSet', 'text': '增删改' }] },
					{ 'key': 'ToolSetup', 'text': '机台设置', 'child': [{ 'key': 'ToolOptionSet', 'text': '增删改' }] }
				]
		},
		{
			'key': 'LogManagement', 'text': '日志管理', 'child':
				[
					{ 'key': 'ToolLog', 'text': '机台日志', 'child': [] },
					{ 'key': 'SystemLog', 'text': '系统日志', 'child': [] }
				]
		},
		{
			'key': 'SystemSetup', 'text': '系统管理', 'child':
				[
					{ 'key': 'UserSetup', 'text': '用户管理', 'child': [{ 'key': 'UserOptionSet', 'text': '增删改' }] },
					{ 'key': 'RoleSetup', 'text': '角色管理', 'child': [{ 'key': 'RoleOptionSetup', 'text': '增删改' }] },
					{ 'key': 'AuthSetup', 'text': '机台权限', 'child': [{ 'key': 'AuthOptionSet', 'text': '增删改' }] },
					{ 'key': 'NoticeSetup', 'text': '公告设置', 'child': [] }
				]
		},
	];
window.CS.title = '远程控制系统(RCS)';
window.CS.company = '合肥新汇成微电子(USC)';
window.CS.localRecording = 1;
window.CS.onready = function ()
{
	if (window.webName == 'toolbar')
	{
		window.videoUri.toLower();
		if (window.videoUri.href.indexOf('extra=cof-ilb(zkw)') > 0 || window.videoUri.href.indexOf('extra=cof-ilb(zkw/8)') > 0)
			window.CS.hasPilot = 1;
	}
};
window.CS.getExtraData = function (url)
{
	if (!url || !url.indexOf)
	{
		if (!arguments.length)
			return [{ 'text': 'COF-ILB(CX)', 'value': 'cof-ilb' }, { 'text': 'COF-ILB(ZKW)', 'value': 'cof-ilb(zkw)' }, { 'text': 'COF-ILB(ZKW/7)', 'value': 'cof-ilb(zkw/7)' }, { 'text': 'COF-ILB(ZKW/8)', 'value': 'cof-ilb(zkw/8)' }];

		return;
	}

	var u = url.toLowerCase();
	i = u.indexOf('extra=cof-ilb(zkw/7)'); // ILB // IO-LED Only
	if (i > 0)
	{
		//1.on = FE 05 00 00 FF 00 98 35, off = FE 05 00 00 00 00 D9 C5, 500ms = FE 10 00 03 00 02 04 00 04 00 05 01 6F, 1s = FE 10 00 03 00 02 04 00 04 00 0A 41 6B
		//2.on = FE 05 00 01 FF 00 C9 F5, off = FE 05 00 01 00 00 88 05, 500ms = FE 10 00 08 00 02 04 00 04 00 05 40 DC, 1s = FE 10 00 08 00 02 04 00 04 00 0A 00 D8
		//3.on = FE 05 00 02 FF 00 39 F5, off = FE 05 00 02 00 00 78 05, 500ms = FE 10 00 0D 00 02 04 00 04 00 05 80 E3, 1s = FE 10 00 0D 00 02 04 00 04 00 0A C0 E7
		//4.on = FE 05 00 03 FF 00 68 35, off = FE 05 00 03 00 00 29 C5, 500ms = FE 10 00 12 00 02 04 00 04 00 05 C1 AF, 1s = FE 10 00 12 00 02 04 00 04 00 0A 81 AB
		//read-do = FE 01 00 00 00 04 29 C6 = FE 01 01 xx ** **
		//read-di = FE 02 00 00 00 04 6D C6 = FE 02 01 xx ** **
		//read-all = FE 04 03 E8 00 14 64 7A = FE 04 28 00 00 00 2D Fx 00 00 00 0x 00 00 00 4A 4D 34 35 79 38 30 62 63 4B 5A 39 56 55 64 4E 00 04 04 00 00 00 00 00 00 00 00 00 ** ** 
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'cof-ilb(zkw/7)';
			this.html = null;
			this.src = 'part/cof-ilb.html';
			this.protocol = 'udp';
			this['e'] = '#p-end-0';
			this['1'] = { o: 1, data: 'FE10000300020400040005016F', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['2'] = { o: 2, data: 'FE1000080002040004000540DC', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['3'] = { o: 3, data: 'FE10000D0002040004000580E3', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['4'] = { o: 4, data: 'FE10001200020400040005C1AF', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this.hw = 'ZKW-4DO-4DI-WL';
		};
		var cb = function (data, extra)
		{
			if (!data)
				return;

			if (data == '0')
				return null;

			var i = data.lastIndexOf('=');
			if (i > 0)
			{
				data = data.substring(i + 1, data.length);
			}
			else
			{
				i = data.lastIndexOf('?');
				if (i > 0)
				{
					data = data.substring(i + 1, data.length);
				}
				else
				{
					i = data.lastIndexOf('/');
					if (i > 0)
						data = data.substring(i + 1, data.length);
				}
			}
			data = data.toUpperCase();
			if (data.length == 8 && data.substring(0, 6) == 'FE1000')//操作成功
			{
				if (!!window.Data)
					window.Data('Status', lang('操作成功'));
				return data;
			}

			if (data.substring(0, 6) == 'FE0101')//读取输出状态//FE 01 01 xx 00 00//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				if (!!extra && !!extra['OS'])
					extra['OS'] = v;
				return v;
			}

			if (data.substring(0, 6) == 'FE0201')//读取输入状态//FE 02 01 xx 00 00//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				for (i = 1; i <= 8; i++)
				{
					var t = v[i - 1] > 0 ? '1' : '0';
					if ($('#img-extra' + i).data('src') != t)
					{
						$('#img-extra' + i).data('src', t);
						$('#img-extra' + i).attr('src', 'imgs/toolbar/' + this[i + '']['img' + t]);
					}
				}
				if (!!extra && !!extra['IS'])
					extra['IS'] = v;
				return v;
			}
		};
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				if (o[i].indexOf(':') > 0)
					window.CS.extraMore = o[i];
				else
					window.CS.extraMore = o[i] + ':10000';
				f.prototype.ExtraMore = window.CS.extraMore;
				break;
			}
		}
		if (!!window.onMessageUrl)
			window.CS.onExtraMURLB = window.onMessageUrl;
		window.onMessageUrl = function (uri, msg, ws)
		{
			if (!uri)
			{
				if (!!window.CS.onExtraMURLB)
					window.CS.onExtraMURLB.apply(this, arguments);
				return;
			}

			if (uri.protocol == 'schedule' && !!uri.host && !!uri.path)
			{
				var t = uri.path;
				var i = t.lastIndexOf('/');
				t = t.substring(i, t.length);
				if (t.length >= 135)//FE 04 28 00 00 00 2D Fx 00 00 00 0x 00 00 00 4A 4D 34 35 79 38 30 62 63 4B 5A 39 56 55 64 4E 00 04 04 00 00 00 00 00 00 00 00 00 ** **
				{
					uri.di = parseInt(t[35], 16);
					uri.do = parseInt(t[23], 16);
					if (window.videoUrl.indexOf(uri.domain) > 0)
					{
						uri.lamp = [uri.di & 1, uri.di & 2, uri.di & 4, uri.d0 & 8];
						for (i = 1; i <= 4; i++)
						{
							uri.light = uri.lamp[i - 1] > 0 ? '1' : '0';
							if ($('#img-extra' + i).data('src') != uri.light)
							{
								$('#img-extra' + i).data('src', uri.light);
								$('#img-extra' + i).attr('src', 'imgs/toolbar/' + window.CS.extraData[i + '']['img' + uri.light]);
							}
						}
					}
				}
			}

			if (!!window.CS.onExtraMURLB)
				window.CS.onExtraMURLB.apply(this, arguments);
		};
		setTimeout(function ()
		{
			$.get('../cache?Schedule://' + window.CS.extraMore, function (result)
			{
				window.onMessageUrl(new Uri('Schedule://' + window.CS.extraMore + '/' + result), null, null);
			});
		}, 1000);
		f.prototype.cb = cb;
		f.prototype.onmessage = cb;
		f.prototype.onMessage = cb;
		return new f();
	}
	i = u.indexOf('extra=cof-ilb(zkw/8)'); // ILB // IO-Button Only
	if (i > 0)
	{
		//read-do = FE 01 00 00 00 04 29 C6 = FE 01 01 xx ** **
		//read-di = FE 02 00 00 00 04 6D C6 = FE 02 01 xx ** **
		//read-all = FE 04 03 E8 00 14 64 7A = FE 04 28 00 00 00 2D Fx 00 00 00 0x 00 00 00 4A 4D 34 35 79 38 30 62 63 4B 5A 39 56 55 64 4E 00 04 04 00 00 00 00 00 00 00 00 00 ** ** 
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'cof-ilb(zkw/8)';
			this.html = null;
			this.src = null;
			this.protocol = 'udp';
			this['e'] = '#p-end-0';
			this.hw = 'ZKW-4DO-4DI-WL';
		};
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				if (o[i].indexOf(':') > 0)
					window.CS.extraMore = o[i];
				else
					window.CS.extraMore = o[i] + ':10000';
				f.prototype.ExtraMore = window.CS.extraMore;
				break;
			}
		}
		if (!!window.onMessageUrl)
			window.CS.onExtraMURLB = window.onMessageUrl;
		window.onMessageUrl = function (uri, msg, ws)
		{
			if (!uri)
			{
				if (!!window.CS.onExtraMURLB)
					window.CS.onExtraMURLB.apply(this, arguments);
				return;
			}

			if (uri.protocol == 'schedule' && !!uri.host && !!uri.path)
			{
				var t = uri.path;
				var i = t.lastIndexOf('/');
				t = t.substring(i, t.length);
				if (t.length >= 135)//FE 04 28 00 00 00 2D Fx 00 00 00 0x 00 00 00 4A 4D 34 35 79 38 30 62 63 4B 5A 39 56 55 64 4E 00 04 04 00 00 00 00 00 00 00 00 00 ** **
				{
					uri.di = parseInt(t[35], 16);
					uri.do = parseInt(t[23], 16);
					if (window.videoUrl.indexOf(uri.domain) > 0)
					{
						uri.lamp = [uri.di & 1, uri.di & 2, uri.di & 4, uri.d0 & 8];
						for (i = 1; i <= 4; i++)
						{
							uri.light = uri.lamp[i - 1] > 0 ? '1' : '0';
							if ($('#img-extra' + i).data('src') != uri.light)
							{
								$('#img-extra' + i).data('src', uri.light);
								$('#img-extra' + i).attr('src', 'imgs/toolbar/' + window.CS.extraData[i + '']['img' + uri.light]);
							}
						}
						window.doPilot((uri.do & 8) == 0 ? 0 : 1);
					}
					if (!!window.videoUri)
					{
						t = window.videoUri.getQuery('pilot');
						if (!!t && t.indexOf(uri.domain) >= 0)
							window.doPilot((uri.do & 8) == 0 ? 0 : 1);
					}
				}
			}

			if (!!window.CS.onExtraMURLB)
				window.CS.onExtraMURLB.apply(this, arguments);
		};
		setTimeout(function ()
		{
			$.get('../cache?Schedule://' + window.CS.extraMore, function (result)
			{
				window.onMessageUrl(new Uri('Schedule://' + window.CS.extraMore + '/' + result), null, null);
			});
		}, 1000);
		return new f();
	}
	i = u.indexOf('extra=cof-ilb(zkw)'); // ILB //
	if (i > 0)
	{
		//1.on = FE 05 00 00 FF 00 98 35, off = FE 05 00 00 00 00 D9 C5, 500ms = FE 10 00 03 00 02 04 00 04 00 05 01 6F, 1s = FE 10 00 03 00 02 04 00 04 00 0A 41 6B
		//2.on = FE 05 00 01 FF 00 C9 F5, off = FE 05 00 01 00 00 88 05, 500ms = FE 10 00 08 00 02 04 00 04 00 05 40 DC, 1s = FE 10 00 08 00 02 04 00 04 00 0A 00 D8
		//3.on = FE 05 00 02 FF 00 39 F5, off = FE 05 00 02 00 00 78 05, 500ms = FE 10 00 0D 00 02 04 00 04 00 05 80 E3, 1s = FE 10 00 0D 00 02 04 00 04 00 0A C0 E7
		//4.on = FE 05 00 03 FF 00 68 35, off = FE 05 00 03 00 00 29 C5, 500ms = FE 10 00 12 00 02 04 00 04 00 05 C1 AF, 1s = FE 10 00 12 00 02 04 00 04 00 0A 81 AB
		//read-do = FE 01 00 00 00 04 29 C6 = FE 01 01 xx ** **
		//read-di = FE 02 00 00 00 04 6D C6 = FE 02 01 xx ** **
		//read-all = FE 04 03 E8 00 14 64 7A = FE 04 28 00 00 00 2D Fx 00 00 00 0x 00 00 00 4A 4D 34 35 79 38 30 62 63 4B 5A 39 56 55 64 4E 00 04 04 00 00 00 00 00 00 00 00 00 ** ** 
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'cof-ilb(zkw)';
			this.html = null;
			this.src = 'part/cof-ilb.html';
			this.protocol = 'udp';
			this['e'] = '#p-end-0';
			this['1'] = { o: 1, data: 'FE10000300020400040005016F', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['2'] = { o: 2, data: 'FE1000080002040004000540DC', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['3'] = { o: 3, data: 'FE10000D0002040004000580E3', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['4'] = { o: 4, data: 'FE10001200020400040005C1AF', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this.hw = 'ZKW-4DO-4DI-WL';
		};
		var cb = function (data, extra)
		{
			if (!data)
				return;

			if (data == '0')
				return null;

			var i = data.lastIndexOf('=');
			if (i > 0)
			{
				data = data.substring(i + 1, data.length);
			}
			else
			{
				i = data.lastIndexOf('?');
				if (i > 0)
				{
					data = data.substring(i + 1, data.length);
				}
				else
				{
					i = data.lastIndexOf('/');
					if (i > 0)
						data = data.substring(i + 1, data.length);
				}
			}
			data = data.toUpperCase();
			if (data.length == 8 && data.substring(0, 6) == 'FE1000')//操作成功
			{
				if (!!window.Data)
					window.Data('Status', lang('操作成功'));
				return data;
			}

			if (data.substring(0, 6) == 'FE0101')//读取输出状态//FE 01 01 xx 00 00//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				if (!!extra && !!extra['OS'])
					extra['OS'] = v;
				return v;
			}

			if (data.substring(0, 6) == 'FE0201')//读取输入状态//FE 02 01 xx 00 00//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				for (i = 1; i <= 8; i++)
				{
					var t = v[i - 1] > 0 ? '1' : '0';
					if ($('#img-extra' + i).data('src') != t)
					{
						$('#img-extra' + i).data('src', t);
						$('#img-extra' + i).attr('src', 'imgs/toolbar/' + this[i + '']['img' + t]);
					}
				}
				if (!!extra && !!extra['IS'])
					extra['IS'] = v;
				return v;
			}
		};
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				if (o[i].indexOf(':') > 0)
					window.CS.extraMore = o[i];
				else
					window.CS.extraMore = o[i] + ':10000';
				f.prototype.ExtraMore = window.CS.extraMore;
				break;
			}
		}
		if (!!window.onMessageUrl)
			window.CS.onExtraMURLB = window.onMessageUrl;
		window.onMessageUrl = function (uri, msg, ws)
		{
			if (!uri)
			{
				if (!!window.CS.onExtraMURLB)
					window.CS.onExtraMURLB.apply(this, arguments);
				return;
			}

			if (uri.protocol == 'schedule' && !!uri.host && !!uri.path)
			{
				var t = uri.path;
				var i = t.lastIndexOf('/');
				t = t.substring(i, t.length);
				if (t.length >= 135)//FE 04 28 00 00 00 2D Fx 00 00 00 0x 00 00 00 4A 4D 34 35 79 38 30 62 63 4B 5A 39 56 55 64 4E 00 04 04 00 00 00 00 00 00 00 00 00 ** **
				{
					uri.di = parseInt(t[35], 16);
					uri.do = parseInt(t[23], 16);
					if (window.videoUrl.indexOf(uri.domain) > 0)
					{
						uri.lamp = [uri.di & 1, uri.di & 2, uri.di & 4, uri.d0 & 8];
						for (i = 1; i <= 4; i++)
						{
							uri.light = uri.lamp[i - 1] > 0 ? '1' : '0';
							if ($('#img-extra' + i).data('src') != uri.light)
							{
								$('#img-extra' + i).data('src', uri.light);
								$('#img-extra' + i).attr('src', 'imgs/toolbar/' + window.CS.extraData[i + '']['img' + uri.light]);
							}
						}
						window.doPilot((uri.do & 8) == 0 ? 0 : 1);
					}
					if (!!window.videoUri)
					{
						t = window.videoUri.getQuery('pilot');
						if (!!t && t.indexOf(uri.domain) >= 0)
							window.doPilot((uri.do & 8) == 0 ? 0 : 1);
					}
				}
			}

			if (!!window.CS.onExtraMURLB)
				window.CS.onExtraMURLB.apply(this, arguments);
		};
		setTimeout(function ()
		{
			$.get('../cache?Schedule://' + window.CS.extraMore, function (result)
			{
				window.onMessageUrl(new Uri('Schedule://' + window.CS.extraMore + '/' + result), null, null);
			});
		}, 1000);
		f.prototype.cb = cb;
		f.prototype.onmessage = cb;
		f.prototype.onMessage = cb;
		return new f();
	}
	i = u.indexOf('extra=cof-ilb'); // ILB //extra=cof-ilb(cx)
	if (i > 0)
	{
		var o = $.parseUrl(url.substring(i, url.length));
		var f = function ()
		{
			this.Extra = 'cof-ilb';
			this.html = null;
			this.src = 'part/cof-ilb.html';
			this.collect = true;
			this['e'] = '#p-end-0';
			this['0'] = '55AA770101F40102030405060708009122/55AA660203E80000000000000000AA55';// 点动时间 / 开关量主动上传 //
			this['1'] = { o: 1, data: 'CCDD330100010001366C', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['2'] = { o: 2, data: 'CCDD3301000200023870', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['3'] = { o: 3, data: 'CCDD3301000400043C78', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['4'] = { o: 4, data: 'CCDD3301000800084488', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['5'] = { o: 5, data: 'CCDD33010010001054A8', img0: 'lamp-g0.png', img1: 'lamp-g1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['6'] = { o: 6, data: 'CCDD33010020002074E8', img0: 'lamp-r0.png', img1: 'lamp-r1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['7'] = { o: 7, data: 'CCDD330100400040B468', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['8'] = { o: 8, data: 'CCDD3301008000803468', img0: 'lamp-y0.png', img1: 'lamp-y1.png', prompt: '确定要按下 {text} 按钮吗？' };
			this['Q0'] = '55AA770101F40102030405060708009122';//点动时间 100 + (1,2,3,4,5,6,7,8) ms //
			this['A0'] = '4F4B21';
			this['Q1'] = '55AA660203E80000000000000000AA55';//开关量主动上传 0x03E8 = 1000ms // 55AA660200640000000000000000AA55(0x0064 = 100ms) //
			this['A1'] = '4F4B21';
			this['OQ'] = { data: 'CCDDB00100000DBE7C', setInterval: 200 };//读取输出状态//2+8 = 0x82 = AABBB00100820D40//
			this['OS'] = [0, 0, 0, 0, 0, 0, 0, 0];
			this['IQ'] = { data: 'CCDDC00100000DCE9C', setInterval: 200 };//读取输入状态//1+2+3+6 = 0x27 = EEFFC00100270DF5EA//
			this['IS'] = [0, 0, 0, 0, 0, 0, 0, 0];
			this.hw = 'CX-5X0XE';
		};
		var cb = function (data, extra)
		{
			if (!data)
				return;

			if (data == '0')
			{
				return
				[
					'55AA770101F40102030405060708009122',//点动时间 100 + (1,2,3,4,5,6,7,8) ms //
					'55AA660203E80000000000000000AA55',//开关量主动上传 0x03E8 = 1000ms // 55AA660200640000000000000000AA55(0x0064 = 100ms) //
					null
				];
			}

			var i = data.lastIndexOf('=');
			if (i > 0)
			{
				data = data.substring(i + 1, data.length);
			}
			else
			{
				i = data.lastIndexOf('?');
				if (i > 0)
				{
					data = data.substring(i + 1, data.length);
				}
				else
				{
					i = data.lastIndexOf('/');
					if (i > 0)
						data = data.substring(i + 1, data.length);
				}
			}
			data = data.toUpperCase();
			if (data == '4F4B21')//操作成功
			{
				if (!!window.Data)
					window.Data('Status', lang('操作成功'));
				return data;
			}

			if (data.substring(0, 8) == 'AABBB001')//读取输出状态//2+8 = 0x82 = AA-BB-B0-01-00-82-0D-40//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				if (!!extra && !!extra['OS'])
					extra['OS'] = v;
				return v;
			}

			if (data.substring(0, 8) == 'EEFFC001')//读取输入状态//1+2+3+6 = 0x27 = EE-FF-C0-01-00-27-0D-F5-EA//
			{
				var i = parseInt(data.substring(10, 12), 16);
				var v = [i & 1, i & 2, i & 4, i & 8, i & 16, i & 32, i & 64, i & 128];
				for (i = 1; i <= 8; i++)
				{
					var t = v[i - 1] > 0 ? '1' : '0';
					if ($('#img-extra' + i).data('src') != t)
					{
						$('#img-extra' + i).data('src', t);
						$('#img-extra' + i).attr('src', 'imgs/toolbar/' + this[i + '']['img' + t]);
					}
				}
				if (!!extra && !!extra['IS'])
					extra['IS'] = v;
				return v;
			}
		};
		for (var i in o)
		{
			if (!!i.length && i.toLowerCase() == 'extramore')
			{
				if (o[i].indexOf(':') > 0)
					f.prototype.ExtraMore = o[i];
				else
					f.prototype.ExtraMore = o[i] + ':50000';
				break;
			}
		}
		f.prototype.cb = cb;
		f.prototype.onmessage = cb;
		f.prototype.onMessage = cb;
		return new f();
	}
};
/*-->*/
