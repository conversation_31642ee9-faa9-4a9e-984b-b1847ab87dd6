﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title></title>
	<link href="lib/layui/css/layui.css" rel="stylesheet" />
	<link href="css/common.css" rel="stylesheet" />
	<style type="text/css">
		.tb {
			width: 50px;
			height: 50px;
			cursor: pointer;
		}

		.key {
			margin: 4px;
			font-size: 14px;
			font-weight: bold;
		}

			.key td {
				height: 32px;
				padding: 2px;
			}

			.key .kl, .key .kl td {
				line-height: 4px;
				height: 4px;
			}

			.key .kp, .key .kp td {
				line-height: 1px;
				height: 1px;
			}

			.key td a {
				width: 100%;
				height: 100%;
				margin: 2px;
				display: inline-block;
				white-space: nowrap;
				overflow: hidden;
				border: solid 1px blue;
				line-height: 32px;
				text-align: center;
				vertical-align: middle;
				font-style: normal;
			}

			.key td .kc {
				border: dashed 1px blue;
			}

			.key td .kd {
				border: dotted 1px blue;
			}

			.key td a:hover {
				background-color: dodgerblue;
				color: red;
			}

			.key td a:active {
				background-color: darkblue;
				color: white;
			}
	</style>
	<script type="text/javascript">
		if (window.top != window.self)
			top.location.href = window.location.href;
	</script>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" id="document.ready">
		$(document).ready(function ()
		{
			if (!!window.Data)
			{
				if (location.search.indexOf('?dev=') >= 0 || location.search.indexOf('&dev=') >= 0 || window.Data('Dev', '?') == 'Dev')
					$('#devTool').show();
			}
		});
	</script>
	<script type="text/javascript" id="layui.use">
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate'], function ()
		{
			$ = layui.$
				, laydate = layui.laydate
				, transfer = layui.transfer
				, form = layui.form
				, table = layui.table
				, layer = layui.layer;
			window.tbar = document.getElementById('div-tool');
			window.player = document.getElementById('img-player');
			window.player.io = $(window.player).offset();
			window.tbar.doMouseMove = function (e)
			{
				if (!window.tmove)
					return;

				if (!e)
					e = window.event;
				window.tbar.pos = { top: window.tbar.iy + (e.clientY - window.tbar.ey), left: window.tbar.ix + (e.clientX - window.tbar.ex) };
				if (window.tbar.pos.left < window.player.io.left)
					window.tbar.pos.left = window.player.io.left;
				if (window.tbar.pos.top < window.player.io.top)
					window.tbar.pos.top = window.player.io.top;
				if (window.tbar.pos.left + window.tbar.clientWidth > window.player.io.left + window.player.clientWidth)
					window.tbar.pos.left = window.player.io.left + window.player.clientWidth - window.tbar.clientWidth;
				if (window.tbar.pos.top + window.tbar.clientHeight > window.player.io.top + window.player.clientHeight)
					window.tbar.pos.top = window.player.io.top + window.player.clientHeight - window.tbar.clientHeight;
				$(window.tbar).css(window.tbar.pos);
			};
			$(window.tbar).mousedown(function (e)
			{
				if (!e)
					e = window.event;
				window.tmove = true;
				window.player.io = $(window.tbar).offset();
				window.tbar.ex = e.clientX;
				window.tbar.ey = e.clientY;
				window.tbar.ix = window.player.io.left;
				window.tbar.iy = window.player.io.top;
				window.player.io = $(window.player).offset();
			}).mousemove(window.tbar.doMouseMove).mouseup(function ()
			{
				window.tmove = false;
			}).mouseenter(function ()
			{
				//window.tmove = false;
			}).mouseleave(function (e)
			{
				if (!e)
					e = window.event;
				if (e.clientX < window.player.io.left || e.clientY < window.player.io.top || e.clientX > window.player.io.left + window.player.clientWidth || e.clientY > window.player.io.top + window.player.clientHeight)
					window.tmove = false;
				//console.log('e.clientX = ' + e.clientX +
				//	', e.clientY = ' + e.clientY +
				//	', playerOX = ' + window.player.io.left +
				//	', playerOY = ' + window.player.io.top +
				//	', playerOR = ' + (window.player.io.left + window.player.clientWidth) +
				//	', playerOB = ' + (window.player.io.top + window.player.clientHeight))
				//window.tmove = false;
			});

			$(window.player).mouseenter(function ()
			{
				//window.tmove = false;
			}).mousedown(function (e)
			{
				return false;
			}).mousemove(function (e)
			{
				if (!!window.tmove)
					window.tbar.doMouseMove.call(window.tbar, e, this);

			}).mouseup(function (e)
			{
				window.tmove = false;
			}).mouseleave(function (e)
			{
				if (!e)
					e = window.event;
				if (e.clientX < window.player.io.left || e.clientY < window.player.io.top || e.clientX > window.player.io.left + window.player.clientWidth || e.clientY > window.player.io.top + window.player.clientHeight)
					window.tmove = false;
			}).keydown(function (e)
			{

			}).keyup(function (e)
			{

			});

			$('.tb').mousedown(function ()
			{
				return false;
			});

			$('#img-record').click(function ()
			{
				window.tmove = false;
				if (!window.RecordState)
					window.RecordState = 1;
				else
					window.RecordState = 0;
				window.video.RecordState = window.RecordState;
				$('#img-record').attr('src', '/rcs.core.job,rcs.core/web/imgs/Record' + window.RecordState + '.png');
				$('#img-record').attr('title', !window.RecordState ? '已停止录制' : '屏幕录制中……');
			});

			$('#img-full').click(function ()
			{
				window.tmove = false;
				if (!window.FullScreen)
				{
					var de = document.documentElement;
					window.FullScreen = 1;
					$('#td-info').hide();
					$('#td-split').hide();
					if (!!de.requestFullscreen)
						de.requestFullscreen();
					else if (!!de.msRequestFullscreen)
						de.msRequestFullscreen();
					else if (!!de.mozRequestFullscreen)
						de.mozRequestFullscreen();
					else if (!!de.webkitRequestFullscreen)
						de.webkitRequestFullscreen();
				}
				else
				{
					var de = document;
					window.FullScreen = 0;
					$('#td-info').show();
					$('#td-split').show();
					if (!!de.exitFullScreen)
						de.exitFullscreen();
					else if (!!de.msExitFullscreen)
						de.msExitFullscreen();
					else if (!!de.mozExitFullscreen)
						de.mozExitFullscreen();
					else if (!!de.webkitExitFullscreen)
						de.webkitExitFullscreen();
				}
				window.video.FullScreen = window.FullScreen;
				$('#img-full').attr('src', '/rcs.core.job,rcs.core/web/imgs/FullScreen' + (!window.FullScreen ? 1 : 0) + '.png');
				$('#img-full').attr('title', !window.FullScreen ? '非全屏模式' : '全屏操作中……');
			});

			window.keys = { 'LChars': "`1234567890-=qwertyuiop[]\\asdfghjkl;'zxcvbnm,./", 'HChars': '~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:"ZXCVBNM<>?', names: [] }
			for (var i = 0; i < 47; i++)
				$('.kp').append('<td><div style="width: 16px;"><br/></div></td>');
			$('.key td a').each(function ()
			{
				var t = $(this).text();
				var i = window.keys.LChars.indexOf(t);
				if (i >= 0 && !$(this).hasClass('kc') && !$(this).hasClass('kd') && this.name != 'Windows' && this.name != 'Menu')
				{
					this.Low = t;
					this.High = window.keys.HChars.substring(i, i + 1);
					this.Case = this.Low + this.High;
				}
				this.keys = window.keys;
				this.keys[this.name] = this;
				this.keys.names.push(this.name);
				$(this).attr('href', '#' + this.name);
			}).click(function ()
			{
				var t = this.name;
				if (t == 'CapsLock')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'lightgreen');
						$(this).data('background-color', $(this).css('background-color'));
					}
					else
					{
						$(this).css('background-color', 'inherit');
					}
				}
				else if (t == 'NumLock')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'lightgreen');
						$(this).data('background-color', $(this).css('background-color'));
					}
					else
					{
						$(this).css('background-color', 'inherit');
					}
				}
				else if (t == 'ScrollLock')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'lightgreen');
						$(this).data('background-color', $(this).css('background-color'));
					}
					else
					{
						$(this).css('background-color', 'inherit');
					}
				}
				else if (t == 'Fn' || t == 'Windows' || t == 'LShift' || t == 'RShift' || t == 'LCtrl' || t == 'RCtrl' || t == 'LAlt' || t == 'RAlt')
				{
					if ($(this).css('background-color') != $(this).data('background-color'))
					{
						$(this).css('background-color', 'deepskyblue');
						$(this).data('background-color', $(this).css('background-color'));
						$(this).data('shortcut', 1);
						$(this).data(t, 1);
						this.Shortcut = 1;
						this.keys[t + 'State'] = 1;
					}
					else
					{
						$(this).css('background-color', 'inherit');
						$(this).data('shortcut', 0);
						$(this).data(t, 0);
						this.Shortcut = 0;
						this.keys[t + 'State'] = 0;
					}
					if (!this.keys['LShiftState'] && !this.keys['RShiftState'])
					{
						for (var i = this.keys.names.length - 1; i >= 0; i--)
						{
							t = this.keys.names[i];
							var e = this.keys[t];
							if (!!e.Case)
								$(e).text(e.Low);
						}
						this.keys['ShiftSate'] = 0;
					}
					else
					{
						for (var i = this.keys.names.length - 1; i >= 0; i--)
						{
							t = this.keys.names[i];
							var e = this.keys[t];
							if (!!e.Case)
								$(e).text(e.High);
						}
						this.keys['ShiftSate'] = 1;
					}
				}
				else
				{
					for (var i = this.keys.names.length - 1; i >= 0; i--)
					{
						t = this.keys.names[i];
						var e = this.keys[t];
						if (!!e.Case)
							$(e).text(e.Low);
						if (!!e.Shortcut)
						{
							$(e).css('background-color', 'inherit');
							$(e).data('shortcut', 0);
							$(e).data(t, 0);
							e.Shortcut = 0;
							this.keys[t + 'State'] = 0;
						}
					}
					this.keys['ShiftSate'] = 0;
				}
				return false;
			});

		});
	</script>
	<script type="text/javascript">
		window.FullScreen = 0;
		window.RecordState = 0;
		window.WorkState = 0;
		window.video = { RecordState: window.RecordState, WorkState: window.WorkState };
		$.parseUrl(window.location.search, window.video);
		window.videoID = window.video.ID;
		window.videoUrl = window.video.Url;
		window.videoDeviceReceived = function (device)
		{
			window.videoDevice = device;
			if ($('#img-pilot').data('interlock_state') != window.videoDevice['interlock_state'])
			{
				var i = window.videoDevice['interlock_state'] == '远端' ? 0 : 1;
				$('#img-pilot').data('interlock_state', window.videoDevice['interlock_state']);
				$('#img-pilot').attr('title', window.videoDevice['interlock_state'])
				$('#img-pilot').attr('src', '/rcs.core.job,rcs.core/web/imgs/Pilot' + i + '.png');
			}
		};
	</script>
</head>
<body style="background-color: gray;">
	<table cellspacing="0" cellpadding="0" border="0" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; right: 0px; bottom: 0px;">
		<tr>
			<td id="td-info" width="300">
				<iframe src="Side.html" style="width: 100%; height: 100%; left: 0px; top: 0px; bottom: 0px;"></iframe>
			</td>
			<td id="td-split" width="10" style="background-color: skyblue; cursor: pointer;" onclick="$('#td-info').css('width', $('#td-info').width() > 50 ? '50px' : '300px')">
				<br />
			</td>
			<td>
				<div id="div-tool" style="background-color: skyblue; position: absolute; width: 970px; height: 326px; text-align: left;" onselectstart="return false;" oncontextmenu="return false;">
					<img id="img-full" src="/rcs.core.job,rcs.core/web/imgs/FullScreen0.png" class="tb" onmousedown="return false;" />
					<img id="img-ctrl" src="/rcs.core.job,rcs.core/web/imgs/Work00.png" class="tb" onmousedown="return false;" />
					<img id="img-record" src="/rcs.core.job,rcs.core/web/imgs/Record0.png" class="tb" onmousedown="return false;" />
					<img id="img-pilot" src="/rcs.core.job,rcs.core/web/imgs/Pilot0.png" class="tb" onmousedown="return false;" />
					<table id="keypad" cellpadding="0" cellspacing="0" border="0" class="key" onselectstart="return false;" onmousedown="return false;">
						<tr class="kp"></tr>
						<tr>
							<td colspan="2"><a name="Esc">Esc</a></td>
							<td><br /></td>
							<td colspan="2"><a name="F1">F1</a></td>
							<td colspan="2"><a name="F2">F2</a></td>
							<td colspan="2"><a name="F3">F3</a></td>
							<td colspan="2"><a name="F4">F4</a></td>
							<td colspan="2"><br /></td>
							<td colspan="2"><a name="F5">F5</a></td>
							<td colspan="2"><a name="F6">F6</a></td>
							<td colspan="2"><a name="F7">F7</a></td>
							<td colspan="2"><a name="F8">F8</a></td>
							<td colspan="2"><br /></td>
							<td colspan="2"><a name="F9">F9</a></td>
							<td colspan="2"><a name="F10">F10</a></td>
							<td colspan="2"><a name="F11">F11</a></td>
							<td colspan="2"><a name="F12">F12</a></td>
							<td><br /></td>
							<td colspan="2"><a name="PrintScreen" class="kc" style="font-family: Wingdings; font-size: 20px;" title="Print Screen">v</a></td>
							<td colspan="2"><a name="ScrollLock" class="kc" style="font-family: Wingdings; font-size: 20px;" title="Scroll Lock">5</a></td>
							<td colspan="2"><a name="PauseBreak" class="kc" style="font-family: Wingdings; font-size: 20px;" title="Pause / Break">I</a></td>
							<td colspan="9"><br /></td>
						</tr>
						<tr class="kp"></tr>
						<tr>
							<td colspan="2"><a name="Tilde">`</a></td>
							<td colspan="2"><a name="D1">1</a></td>
							<td colspan="2"><a name="D2">2</a></td>
							<td colspan="2"><a name="D3">3</a></td>
							<td colspan="2"><a name="D4">4</a></td>
							<td colspan="2"><a name="D5">5</a></td>
							<td colspan="2"><a name="D6">6</a></td>
							<td colspan="2"><a name="D7">7</a></td>
							<td colspan="2"><a name="D8">8</a></td>
							<td colspan="2"><a name="D9">9</a></td>
							<td colspan="2"><a name="D0">0</a></td>
							<td colspan="2"><a name="Minus">-</a></td>
							<td colspan="2"><a name="Plus">=</a></td>
							<td colspan="5"><a name="Backspace">Backspace</a></td>
							<td><br /></td>
							<td colspan="2"><a name="Insert" title="Inster" class="kc">Ins</a></td>
							<td colspan="2"><a name="Home" title="Honm" class="kc" style="font-size: 12px;">Home</a></td>
							<td colspan="2"><a name="PageUp" title="Page Up" class="kc" style="font-size: 12px;">PgUp</a></td>
							<td><br /></td>
							<td colspan="2"><a name="NumLock" class="kd" title="Number Lock" style="font-size: 12px;">NmLk</a></td>
							<td colspan="2"><a name="Division" class="kd">/</a></td>
							<td colspan="2"><a name="Asterisk" class="kd">*</a></td>
							<td colspan="2"><a name="Negative" class="kd">-</a></td>
						</tr>
						<tr>
							<td colspan="3"><a name="Tab">Tab</a></td>
							<td colspan="2"><a name="Q">a</a></td>
							<td colspan="2"><a name="W">w</a></td>
							<td colspan="2"><a name="E">e</a></td>
							<td colspan="2"><a name="R">r</a></td>
							<td colspan="2"><a name="T">t</a></td>
							<td colspan="2"><a name="Y">y</a></td>
							<td colspan="2"><a name="U">u</a></td>
							<td colspan="2"><a name="I">i</a></td>
							<td colspan="2"><a name="O">o</a></td>
							<td colspan="2"><a name="P">p</a></td>
							<td colspan="2"><a name="LSB">[</a></td>
							<td colspan="2"><a name="RSB">]</a></td>
							<td colspan="4"><a name="Backslash">\</a></td>
							<td><br /></td>
							<td colspan="2"><a name="Delete" class="kc" title="Delete">Del</a></td>
							<td colspan="2"><a name="End" class="kc" title="End">End</a></td>
							<td colspan="2"><a name="PageDown" class="kc" title="Page Down" style="font-size: 12px;">PgDn</a></td>
							<td><br /></td>
							<td colspan="2"><a name="N7" class="kd">7</a></td>
							<td colspan="2"><a name="N8" class="kd">8</a></td>
							<td colspan="2"><a name="N9" class="kd">9</a></td>
							<td colspan="2" rowspan="2"><a name="Positive" class="kd">+</a></td>
						</tr>
						<tr>
							<td colspan="4"><a name="CapsLock">CapsLock</a></td>
							<td colspan="2"><a name="A">a</a></td>
							<td colspan="2"><a name="S">s</a></td>
							<td colspan="2"><a name="D">d</a></td>
							<td colspan="2"><a name="F">f</a></td>
							<td colspan="2"><a name="G">g</a></td>
							<td colspan="2"><a name="H">h</a></td>
							<td colspan="2"><a name="J">j</a></td>
							<td colspan="2"><a name="K">k</a></td>
							<td colspan="2"><a name="L">l</a></td>
							<td colspan="2"><a name="Colon">;</a></td>
							<td colspan="2"><a name="Quotation">'</a></td>
							<td colspan="5"><a name="Enter">Enter</a></td>
							<td colspan="8"><br /></td>
							<td colspan="2"><a name="N4" class="kd">4</a></td>
							<td colspan="2"><a name="N5" class="kd">5</a></td>
							<td colspan="2"><a name="N6" class="kd">6</a></td>
						</tr>
						<tr>
							<td colspan="5"><a name="LShift">Shift</a></td>
							<td colspan="2"><a name="Z">z</a></td>
							<td colspan="2"><a name="X">x</a></td>
							<td colspan="2"><a name="C">c</a></td>
							<td colspan="2"><a name="V">v</a></td>
							<td colspan="2"><a name="B">b</a></td>
							<td colspan="2"><a name="N">n</a></td>
							<td colspan="2"><a name="M">m</a></td>
							<td colspan="2"><a name="Comma">,</a></td>
							<td colspan="2"><a name="Period">.</a></td>
							<td colspan="2"><a name="Question">/</a></td>
							<td colspan="6"><a name="RShift">Shift</a></td>
							<td colspan="3"><br /></td>
							<td colspan="2"><a name="Up" class="kc" style="font-family: Webdings; font-size: 20px;" title="Up">5</a></td>
							<td colspan="3"><br /></td>
							<td colspan="2"><a name="N1" class="kd">1</a></td>
							<td colspan="2"><a name="N2" class="kd">2</a></td>
							<td colspan="2"><a name="N3" class="kd">3</a></td>
							<td colspan="2" rowspan="2"><a name="NewLine" class="kd">Enter</a></td>
						</tr>
						<tr>
							<td colspan="2"><a name="Fn">Fn</a></td>
							<td colspan="4"><a name="LCtrl">Ctrl</a></td>
							<td colspan="2"><a name="Windows" style="font-family: Wingdings; font-size: 20px;" title="Windows">z</a></td>
							<td colspan="2"><a name="LAlt">Alt</a></td>
							<td colspan="14"><a name="Space">Space</a></td>
							<td colspan="2"><a name="RAlt">Alt</a></td>
							<td colspan="2"><a name="Menu" style="font-family: Wingdings; font-size: 20px;" title="Menu">2</a></td>
							<td colspan="3"><a name="RCtrl">Ctrl</a></td>
							<td><br /></td>
							<td colspan="2"><a name="Left" class="kc" style="font-family: Webdings; font-size: 20px;" title="Left">3</a></td>
							<td colspan="2"><a name="Down" class="kc" style="font-family: Webdings; font-size: 20px;" title="Down">6</a></td>
							<td colspan="2"><a name="Right" class="kc" style="font-family: Webdings; font-size: 20px;" title="Right">4</a></td>
							<td><br /></td>
							<td colspan="4"><a name="N0" class="kd">0</a></td>
							<td colspan="2"><a name="Point" class="kd">.</a></td>
						</tr>
						<tr class="kp"></tr>
					</table>
				</div>
				<img id="img-player" src="/frame/**************/" style="left: 0px; height: 0px; width: 100%; height: 100%; right: 0px; bottom: 0px;" onclick="javascript: $(this).data('pause', !$(this).data('pause'));" oncontextmenu="return false;" onkeydown="return false;" />
			</td>
		</tr>
	</table>
	<!--
	<script type="text/javascript">
		setInterval(function () {
			document.getElementById('img-player').src = '/frame/' + window.video.UrlAuthority + '/' + Date.now() + '.jpg';
		}, 100);
	</script>
	-->
	<!--
	<canvas id="cvs-player" style="position: absolute; left: 0px; height: 0px; width: 100%; height: 100%; right: 0px; bottom: 0px;"></canvas>
	-->
	<script type="text/javascript">
		var cvs = document.getElementById('cvs-player');
		var c2d = !cvs || !cvs.getContext ? null : cvs.getContext('2D');
		var img = document.getElementById('img-player');//new Image();
		img.src = '/frame/' + window.video.UrlAuthority + '/' + Date.now() + '.jpg'
		//img.onload = function () {
		//	if (!c2d)
		//		c2d = cvs.getContext('2D');
		//	c2d.drawImage(img, 0, 0, cvs.clientWidth, cvs.clientHeight);
		//};
		function openWebSocket()
		{
			var ws = new WebSocket('ws://' + window.location.host + '/frame/' + window.video.UrlAuthority + '/');
			ws.binaryType = 'blob';//'arraybuffer';//
			ws.onopen = function (e)
			{

			};
			ws.onmessage = function (e)
			{
				if (!!$(img).data('pause'))
					return;

				//var src = '';
				//var u8s = new Uint8Array(e.data);
				//for (var i = 0; i < u8s.length / 8192; i++)
				//	src += String.fromCharCode.apply(null, u8s.slice(i * 8192, i * 8192 + 8192));
				//src = window.btoa(src);
				//img.src = 'data:image/jpg;base64,' + src;
				var src = img.src;
				img.src = URL.createObjectURL(e.data);
				if (/^blob:/img.test(src))
					URL.revokeObjectURL(src);
			};
			ws.onclose = function (e)
			{
				openWebSocket();
			};
			ws.onerror = function (e)
			{

			};
			//window.btoa();
		}
		setTimeout(openWebSocket, 1000);
	</script>
	<div id="devTool" style="color: red; position: absolute; display: none; right: 0; bottom: 0px; z-index: 999;">
		<span onclick="javascript: this.innerHTML = navigator.userAgent;"></span>
		|
		<a href="javascript: void(0);" onclick="javascript: top.window.Data('Dev', 'Dev');">Developer Tool</a>
		|
		<a href="javascript: window.location.reload();">Reload</a>
	</div>
</body>
</html>