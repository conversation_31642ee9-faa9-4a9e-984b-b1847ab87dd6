﻿// 请求接口 ajax requestapi
(function ($) {
    'use strict';

    if (!Array.prototype.findIndex) {
        Array.prototype.findIndex = function (predicate) {
            var list = Object(this);
            var length = list.length >>> 0;
            var thisArg = arguments[1];
            var index = -1;

            for (var i = 0; i < length; i++) {
                if (predicate.call(thisArg, list[i], i, list)) {
                    index = i;
                    break;
                }
            }
            return index;
        };
    }

    var AjaxApi = {
        defaults: {
            type: 'GET',
            url: '',
            data: {},
            cache: false,
            dataType: 'text',
            contentType: 'text/plain; charset=utf-8',
            success: function () { },
            error: function (jqXHR, textStatus, errorThrown) {
                if (jqXHR.status == 401) {
                    layer.alert(lang('您没有此项操作的权限，请与系统管理员联系！'), { btn: [lang('确定'), lang('取消')], title: lang('提示') });
                } else {
                    layer.alert(jqXHR.responseText + lang(":请与系统管理员联系！！！"), { btn: [lang('确定'), lang('取消')], title: lang('提示') });
                } 
            }
        },

        request: function (options) {
            var dataType = options.dataType || AjaxApi.defaults.dataType;
            var url = options.url;
            var type = options.type || AjaxApi.defaults.type;
            var data = options.data || AjaxApi.defaults.data;

            // 验证options中的url
            if (typeof url !== 'string' || url === '') {
                throw new Error('URL is required for AjaxApi.request and must be a string.');
            }

            var settings = {
                type: type,
                url: url,
                data: data,
                cache: AjaxApi.defaults.cache,
                dataType: dataType,
                contentType: 'application/' + dataType + '; charset=utf-8',
                success: options.success || AjaxApi.defaults.success,
                error: options.error || AjaxApi.defaults.error
            };
            // 发送请求
            $.ajax(settings);
        }
    };

    window.AjaxApi = AjaxApi;

})(jQuery);


function getRoleUserGroup(array, type)
{
    return array.filter(function (item)
    {
        if (type == 'userGroup')
        {
            return item.Data.indexOf('IsUserGroup') != -1;
        } else if (type == 'role')
        {
            return item.Data.indexOf('IsUserGroup') == -1;
        } else
        {
            return true;
        }
    });
}

function hasButtonPermission(type)
{
    if (window.login.Permission)
    {
        var premissions = window.login.Permission.split(',');
        var idx = premissions.findIndex(function (item) { return item == type });
        if (idx == -1)
            return false;
        return true;
    }
    return true;
}