﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>上传图片</title>
	<!-- 样式 -->
	<link rel="stylesheet" href="lib/layui/css/layui.css" />
	<link rel="stylesheet" href="lib/cropper.css" />
	<link rel="stylesheet" href="css/common.css" />
	<style type="text/css">
		.allbox {
			width: 100%;
			display: flex;
			margin-bottom: 20px;
		}

		/* 装上传图片 */
		.box1 {
			width: 500px;
			height: 500px;
			background: rgba(40, 40, 40, 0.3);
		}
		/* 被裁剪展示图片容器 */
		.box2 {
			width: 100px;
			height: 100px;
			margin-left: 100px;
		}

		.radio {
			color: black;
			font-weight: bold;
			padding: 0px 10px;
			border: 1px solid black;
		}

		.select-cv {
		}

		.cv-title {
			width: 10%;
			text-align: center;
			color: blue;
			font-weight: bold;
			font-size: 24px;
		}
	</style>
	<!-- js  -->
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/cropper.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript">
		window.CVP = {
			CHANNEL: { title: '通道', parameter: [] },
			THRESHOLD: { title: '简单二极化', parameter: [] },
			ADAPTIVE: { title: '高级二极化', parameter: [] },
			EQUALIZE: { title: '均衡', parameter: [] },
			REVERSAL: { title: '反转', parameter: [] },
			BLUR: { title: '均值滤波', parameter: [] },
			MEDIAN: { title: '中值滤波', parameter: [] },
			GAUSSIAN: { title: '高斯滤波', parameter: [] },
			MORPHOLOGY: { title: '形状处理', parameter: [] },
			SOBLE: { title: '简单边界查找', parameter: [] },
			SCHARR: { title: '普通边界查找', parameter: [] },
			LAPLACIAN: { title: '高级边界查找', parameter: [] },
			CANNY: { title: '复杂边界查找', parameter: [] },
			'': {}
		};
	</script>
	<script type="text/javascript">
		function cvMove(s, d)
		{
			var me = $(s)[0];
			var ss = $('#cvs').find('*[name=cv]');
			if (!d)
			{
				for (var i = ss.length - 1; i > 0; i--)
				{
					if (ss[i] == me)
					{
						$(ss[i - 1]).before(me);
						return;
					}
				}
				return;
			}

			for (var i = ss.length - 2; i >= 0; i--)
			{
				if (ss[i] == me)
				{
					$(ss[i + 1]).after(me);
					return;
				}
			}
			return;
		}
	</script>
</head>
<body style="background-color: gray;">
	<form action="#">
		<div class="allbox">
			<!-- 展示图片 -->
			<div class="box1">
				<img id="img1">
			</div>

			<div class="box2">

			</div>
		</div>
		<input id="input1" type="file" />
		<button id="button1">截取模板</button>
		<input type="button" value="处理" onclick="javascript: cv.call(this);" />

		<div class="layui-form">
			<div class="layui-panel" style="padding: 16px;">
				<select name="cv" class="select-cv" lay-filter="cv"><option value="">——请选择——</option></select>
			</div>
			<div id="cvs"></div>
			<div class="layui-panel" style="padding: 16px;"></div>
		</div>

		<script type="text/html" id="CHANNEL">
			<table cellpadding="0" cellspacing="0" border="0" name="CHANNEL" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th>通道：</th>
					<td>
						<input type="radio" name="CHANNEL-channel" value="gray" title="黑白通道(默认)" checked="checked" />
						<div lay-radio="">
							<span class="radio" style="background-color: gray;">黑白通道(默认)</span>
						</div>
						<input type="radio" name="CHANNEL-channel" value="r" title="红色通道" />
						<div lay-radio="">
							<span class="radio" style="background-color: red;">红色通道</span>
						</div>
						<input type="radio" name="CHANNEL-channel" value="g" title="绿色通道" />
						<div lay-radio="">
							<span class="radio" style="background-color: lime;">绿色通道</span>
						</div>
						<input type="radio" name="CHANNEL-channel" value="b" title="蓝色通道" />
						<div lay-radio="">
							<span class="radio" style="background-color: blue;">蓝色通道</span>
						</div>
						<input type="radio" name="CHANNEL-channel" value="rb" title="粉色通道" />
						<div lay-radio="">
							<span class="radio" style="background-color: fuchsia;">粉色通道</span>
						</div>
						<input type="radio" name="CHANNEL-channel" value="rg" title="黄色通道" />
						<div lay-radio="">
							<span class="radio" style="background-color: yellow;">黄色通道</span>
						</div>
						<input type="radio" name="CHANNEL-channel" value="gb" title="青色通道" />
						<div lay-radio="">
							<span class="radio" style="background-color: aqua;">青色通道</span>
						</div>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="THRESHOLD">
			<table cellpadding="0" cellspacing="0" border="0" name="THRESHOLD" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">阈值：</th>
					<td style="width: 90%;">
						<div name="THRESHOLD-thresh" class="THRESHOLD-thresh" id="THRESHOLD-thresh{{0}}"></div>
					</td>
				</tr>
				<tr>
					<th>方式：</th>
					<td>
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="0" title="1(默认)" checked="checked" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="1" title="2" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="2" title="3" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="3" title="4" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="4" title="5" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="7" title="6" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="8" title="7" />
						<input type="radio" name="THRESHOLD-type" class="THRESHOLD-type" value="16" title="8" />
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="ADAPTIVE">
			<table cellpadding="0" cellspacing="0" border="0" name="ADAPTIVE" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">阈值：</th>
					<td style="width: 90%;" colspan="3">
						<div name="ADAPTIVE-thresh" id="ADAPTIVE-max{{0}}"></div>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">高斯：</th>
					<td style="width: 40%;">
						<input type="radio" name="ADAPTIVE-method" class="ADAPTIVE-method" value="0" title="均值" />
						<input type="radio" name="ADAPTIVE-method" class="ADAPTIVE-method" value="1" title="高斯(默认)" checked="checked" />
					</td>
					<th style="width: 10%;">方式：</th>
					<td style="width: 40%;">
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="0" title="1(默认)" checked="checked" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="1" title="2" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="2" title="3" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="3" title="4" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="4" title="5" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="7" title="6" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="8" title="7" />
						<input type="radio" name="ADAPTIVE-type" class="ADAPTIVE-type" value="16" title="8" />
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="EQUALIZE">
			<table cellpadding="0" cellspacing="0" border="0" name="ADAPTIVE" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="REVERSAL">
			<table cellpadding="0" cellspacing="0" border="0" name="ADAPTIVE" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="BLUR">
			<table cellpadding="0" cellspacing="0" border="0" name="BLUR" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">大小：</th>
					<td style="width: 90%;">
						<input type="radio" name="BLUR-type" class="BLUR-size" value="1" title="1" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="2" title="2" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="3" title="3" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="4" title="4(默认)" checked="checked" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="5" title="5" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="6" title="6" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="7" title="7" />
						<input type="radio" name="BLUR-type" class="BLUR-size" value="8" title="8" />
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="MEDIAN">
			<table cellpadding="0" cellspacing="0" border="0" name="MEDIAN" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">大小：</th>
					<td style="width: 90%;">
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="1" title="1" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="2" title="2" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="3" title="3" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="4" title="4(默认)" checked="checked" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="5" title="5" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="6" title="6" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="7" title="7" />
						<input type="radio" name="MEDIAN-type" class="MEDIAN-size" value="8" title="8" />
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="GAUSSIAN">
			<table cellpadding="0" cellspacing="0" border="0" name="GAUSSIAN" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">大小：</th>
					<td style="width: 90%;">
						<input type="radio" name="type" class="GAUSSIAN-size" value="1" title="1" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="2" title="2" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="3" title="3" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="4" title="4(默认)" checked="checked" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="5" title="5" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="6" title="6" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="7" title="7" />
						<input type="radio" name="type" class="GAUSSIAN-size" value="8" title="8" />
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">Sigma X：</th>
					<td style="width: 90%;">
						<div name="thresh" id="GAUSSIAN-x{{0}}"></div>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">Sigma Y：</th>
					<td style="width: 90%;">
						<div name="thresh" id="GAUSSIAN-y{{0}}"></div>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">边框：</th>
					<td style="width: 90%;">
						<input type="radio" name="type" class="GAUSSIAN-border" value="0" title="0" />
						<input type="radio" name="type" class="GAUSSIAN-border" value="1" title="1" />
						<input type="radio" name="type" class="GAUSSIAN-border" value="2" title="2" />
						<input type="radio" name="type" class="GAUSSIAN-border" value="3" title="3" />
						<input type="radio" name="type" class="GAUSSIAN-border" value="4" title="4(默认)" checked="checked" />
						<input type="radio" name="type" class="GAUSSIAN-border" value="5" title="5" />
						<input type="radio" name="type" class="GAUSSIAN-border" value="16" title="6" />
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="MORPHOLOGY">
			<table cellpadding="0" cellspacing="0" border="0" name="MORPHOLOGY" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">方法：</th>
					<td style="width: 90%;">
						<input type="radio" name="type" class="MORPHOLOGY-type" value="0" title="腐蚀" checked="checked" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="1" title="膨胀" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="2" title="开" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="3" title="闭" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="4" title="梯度" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="5" title="礼帽" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="6" title="黑帽" />
						<input type="radio" name="type" class="MORPHOLOGY-type" value="7" title="直击" />
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">迭代次数：</th>
					<td style="width: 90%;">
						<div name="thresh" id="MORPHOLOGY-iterations{{0}}"></div>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="SOBLE">
			<table cellpadding="0" cellspacing="0" border="0" name="SOBLE" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">大小：</th>
					<td style="width: 90%;">
						<div name="size" id="SOBLE-size{{0}}"></div>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="SCHARR">
			<table cellpadding="0" cellspacing="0" border="0" name="ADAPTIVE" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="LAPLACIAN">
			<table cellpadding="0" cellspacing="0" border="0" name="LAPLACIAN" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">大小：</th>
					<td style="width: 90%;">
						<div name="size" id="LAPLACIAN-size{{0}}"></div>
					</td>
				</tr>
			</table>
		</script>
		<script type="text/html" id="CANNY">
			<table cellpadding="0" cellspacing="0" border="0" name="CANNY" style="width: 100%;">
				<tr>
					<th class="cv-title">{{1}}</th>
					<td>
						<a class="layui-icon layui-icon-delete" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: $('#cv-{{0}}').remove();"></a>
						&nbsp;
						<a class="layui-icon layui-icon-up" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 0);"></a>
						&nbsp;
						<a class="layui-icon layui-icon-down" style="font-size: 30px; color: #1E9FFF;" onclick="javascript: cvMove('#cv-{{0}}', 1);"></a>
					</td>
				</tr>
				<tr>
					<th style="width: 10%;">大小：</th>
					<td style="width: 90%;">
						<div name="size" id="CANNY-thresh{{0}}"></div>
					</td>
				</tr>
			</table>
		</script>

		<script type="text/javascript">
			function changeCV(e)
			{
				var t = $('#' + e.value).html();
				if (!e.text)
					e.text = $(this).text();
				if (!window.cvIndex)
					window.cvIndex = 1;
				else
					window.cvIndex++;
				t = t.replace(/\{\{0\}\}/img, window.cvIndex);
				t = t.replace(/\{\{no\}\}/img, window.cvIndex);
				t = t.replace(/\{\{1\}\}/img, e.text);
				t = t.replace(/\{\{text\}\}/img, e.text);
				t = t.replace(/\{\{title\}\}/img, e.text);
				t = '<div class="layui-panel " style="padding: 16px;" id="cv-' + window.cvIndex + '" name="cv">' + t + '</div>';
				$('#cvs').append(t);//$(this).parents('.layui-form').append(t);
				slider.render({
					elem: '#THRESHOLD-thresh' + window.cvIndex,
					min: 1,
					max: 255,
					value: [64, 255], // 初始值
					range: true, // 范围选择
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				slider.render({
					elem: '#ADAPTIVE-max' + window.cvIndex,
					min: 1,
					max: 255,
					value: 255, // 初始值
					change: function (vals)
					{
						layer.msg('开始值：' + vals);
					}
				});
				slider.render({
					elem: '#GAUSSIAN-x' + window.cvIndex,
					min: 0,
					max: 100,
					value: 0, // 初始值
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				slider.render({
					elem: '#GAUSSIAN-y' + window.cvIndex,
					min: 0,
					max: 100,
					value: 0, // 初始值
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				slider.render({
					elem: '#MORPHOLOGY-iterations' + window.cvIndex,
					min: 1,
					max: 10,
					value: 3, // 初始值
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				slider.render({
					elem: '#SOBLE-size' + window.cvIndex,
					min: 1,
					max: 10,
					value: 3, // 初始值
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				slider.render({
					elem: '#LAPLACIAN-size' + window.cvIndex,
					min: 1,
					max: 10,
					value: 3, // 初始值
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				slider.render({
					elem: '#CANNY-thresh' + window.cvIndex,
					min: 1,
					max: 255,
					value: [50, 80], // 初始值
					range: true, // 范围选择
					change: function (vals)
					{
						layer.msg('开始值：' + vals[0] + '、结尾值：' + vals[1]);
					}
				});
				form.render();
			}
		</script>

		<script type="text/javascript">

			layui.use(function ()
			{
				slider = layui.slider;
				layer = layui.layer;
				form = layui.form;

				for (var i in window.CVP)
				{
					if (!!window.CVP[i].title)
						$('.select-cv').append('<option value="' + i + '">' + window.CVP[i].title + '</option>');
				}
				form.render();

				form.on('select(cv)', changeCV);

			});
		</script>

		<script type="text/javascript">

			let img1 = document.getElementById('img1')
			let input1 = document.getElementById('input1')
			let button1 = document.getElementById('button1')

			// 上传的图片储存一下
			let fileImg;
			// 声明一个变量
			let cropper = new Cropper(img1, {
				//aspectRatio: 1 / 1,  // 裁切比例
				viewMode: 1, // 0 无限制 1 裁剪框不超过图片 2具体个1差不多没看出 ,3拉伸图片
				autoCropArea: 0.5, //裁切框大小 默认0.8(原图的80%)
				// 跟多配置选项  https://www.npmjs.com/package/cropperjs
			})

			//上传图片时
			input1.onchange = (event) =>
			{

				fileImg = event.target.files[0]

				// cropper.replace(url,[hasSameSize]) 设置图片 hasSameSize type:Boolean,Default: false
				cropper.replace(URL.createObjectURL(fileImg), false)

			}

			// 保存时
			button1.onclick = () =>
			{
				let base64Img = cropper.getCroppedCanvas().toDataURL()
				document.getElementById('patter').src = base64Img;
				console.log(base64Img)
				cropper.getCroppedCanvas().toBlob((blob) =>
				{
					// 得到 blob 图片
					console.log('blob', blob)
					// 得到 file 文件
					console.log('file', new window.File([blob], fileImg.name, { type: fileImg.type }))
				})
			}

		</script>

		<script type="text/javascript">
			layer.open({
				type: 1, // page 层类型
				area: ['500px', '300px'],
				title: '匹配模板',
				shade: 0, // 遮罩透明度；0=不使用
				shadeClose: false, // 点击遮罩区域，是否关闭弹层
				maxmin: false, // 是否允许全屏与最小化
				anim: 0, // 0-6 的动画形式，-1 不开启
				content: '<div style="padding: 32px;">一个普通的页面层，传入了自定义的 HTML</div><img id="patter" src="#"/>'
			});

			function cv()
			{
				var reader = new FileReader();
				reader.readAsDataURL(fileImg);
				reader.onload = function (e)
				{
					var ajax = {
						data: {}
					};
					ajax.data['file'] = e.target.result;

					ajax.success = function (result)
					{
						cropper.replace(URL.createObjectURL(result), false);
						alert(typeof result);
					};
					ajax.error = function (request)
					{
						alert(request.responseText);
					};
					ajax.dataType = 'blob';
					ajax.method = 'POST';
					ajax.cache = false;
					ajax.url = '/cv.do';
					$.ajax(ajax);
				}
			}
		</script>
	</form>
</body>
</html>