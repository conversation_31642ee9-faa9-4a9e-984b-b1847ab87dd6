function init() {
    // 如果window.goSamples存在，则执行goSamples函数
    if (window.goSamples) goSamples();

    myDiagramConfig.ChangedSelection = ChangedSelection; // 绑定节点选中事件
    myDiagram = MAKE(go.Diagram, "myDiagramDiv", myDiagramConfig); // 生成 gojs实例画布
    myDiagram.allowMultipleSelection = true; // 允许多选

    myDiagram.nodeTemplate = nodeTemplateConfig; // 节点模板
    // myDiagram.nodeTemplate = groupTemplateConfig; // 组节点模板

    var myToolTip = MAKE(go.HTMLInfo, {
        show: showToolTip,
        hide: hideToolTip,
    });
    if (!isEdit) {
        myDiagram.allowMove = false; // 禁止移动
        myDiagram.nodeTemplate.doubleClick = doubleClickNode; // 绑定双击事件
        myDiagram.nodeTemplate.toolTip = myToolTip;
    }

    // 右键菜单
    var contextMenuStyle = {
        font: "10pt sans-serif",
        margin: 8,
        cursor: "pointer",
    };

    // 生成右键菜单数据
    function generateContextMenuData() {
        if (!!isEdit) {
            return MAKE(
                "ContextMenu",
                MAKE(
                    "ContextMenuButton",
                    MAKE(go.TextBlock, "删除", contextMenuStyle),
                    {
                        click: deleteNode,
                    }
                )
            );
        } else {
            /*return MAKE(
                "ContextMenu",
                MAKE(
                    "ContextMenuButton",
                    MAKE(go.TextBlock, "暂无数据", contextMenuStyle),
                    {
                        // click: viewNode,
                    }
                )
            );*/
        }
    }
    // myDiagram.nodeTemplate.contextMenu = generateContextMenuData()

    // 右键菜单的异步加载
    myDiagram.addDiagramListener("ObjectContextClicked", function (e) {
        if (!!isEdit) return;
        var node = e.subject.part;
        if (node instanceof go.Node) {
            getMachineDevice(node.data.id);
        }
        e.handled = true;
    });
    // 获取机台关联设备列表
    function getMachineDevice(id) {
        AjaxApi.request({
            url: "/Equipment/GetEquipment.do",
            data: { machine: id },
            dataType: 'text',
            success: function (result) {
                var res = $.eval(result);
                generateContextMenu(res.data);
            }
        })
    }
    // 生成右键菜单
    function generateContextMenu(items) {
        var menu = document.getElementById("contextMenu");
        if (items.length > 0) {
            menu.innerHTML = "";
            items.forEach(function (item) {
                var menuItem = document.createElement("div");
                menuItem.className = "menu-item";
                menuItem.textContent = item.Name;
                menuItem.onclick = function () {
                    console.log("菜单项 " + item.Name + " 被点击");
                    menu.style.display = "none"; // 隐藏菜单
                };
                menu.appendChild(menuItem);
            });
        } else {
            menu.innerHTML = '<div class="menu-item">暂无数据</div>';
        }
        
        var maxX = window.innerWidth - menu.offsetWidth;
        var maxY = window.innerHeight - menu.offsetHeight;
        if (lastX > maxX) {
            menu.style.left = maxX + "px";
        } else {
            menu.style.left = lastX + "px";
        }

        if (lastY > maxY) {
            menu.style.top = maxY - 40 + "px";
        } else {
            menu.style.top = lastY - 40 + "px";
        }
        menu.style.display = "block";
        isContextMenuActive = true;

        menu.onclick = function (e) {
            e.stopPropagation();
        };

        // 点击其它地方隐藏菜单
        document.addEventListener('click', function hideMenu() {
            menu.style.display = "none";
            isContextMenuActive = false; 
            document.removeEventListener('click', hideMenu);
        });
        setTimeout(function () { isContextMenuActive = false; }, 200);
    }

    // 设置视图虚拟化
    myDiagram.scrollMode = go.Diagram.InfiniteScroll;

    // 左侧组件
    if (!!isEdit) {
        var palette = MAKE(go.Palette, "myPaletteDiv", {
            nodeTemplate: paletteNodeTemplate,
            // groupTemplateMap: myDiagram.groupTemplateMap, // 设置组模板
            // nodeTemplateMap: myDiagram.nodeTemplateMap, // 设置节点模板
            // autoScale: go.Diagram.Uniform, // 自动缩放 适合窗口
        });
        palette.model.nodeDataArray = myPaletteNode;
        myPaletteNode.forEach(function (node) {
            if (node.figure === "Rectangle") {
                node.zOrder = 0; // 设置矩形是最小层级
            } else {
                node.zOrder = 1;
            }
        });
    }
    // 缩略图
    myOverview = MAKE(go.Overview, "myOverviewDiv", { observed: myDiagram });

    // 未保存时提示
    myDiagram.addDiagramListener("Modified", function (e) {
        var button = document.getElementById("SaveButton");
        if (button) button.disabled = !myDiagram.isModified;
        var idx = document.title.indexOf("*");
        if (myDiagram.isModified) {
            if (idx < 0) document.title += "*";
        } else {
            if (idx >= 0) document.title = document.title.substring(0, idx);
        }
    });

    // 粘贴时处理
    myDiagram.addDiagramListener("ClipboardPasted", function (e) {
        if (!isEdit) return;
        // 获取剪贴板数据
        var copiedParts = e.diagram.selection.toArray();
        copiedParts.forEach(function (part) {
            if (part instanceof go.Node) {
                var data = part.data;

                var newData = {};
                var keysArr = {
                    id: "",
                    text: "",
                    // strokeColor: "#000",
                    key: generateUniqueKey(e.diagram.model),
                    // fill: "#fff",
                    // textWidth: "",
                    // textAlign: "",
                    data: {}
                };

                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        if (key in keysArr) {
                            newData[key] = keysArr[key];
                        } else {
                            newData[key] = data[key];
                        }
                    }
                }
                // 删除原始的复制节点，以避免重复
                e.diagram.model.removeNodeData(data);
                // 添加新的节点数据
                e.diagram.model.addNodeData(newData);
            }
        });
        // 阻止默认的粘贴行为
        e.handled = true;
    });
}
// 鼠标悬停节点时显示提示框
function showToolTip(obj, diagram, tool) {
    if (isContextMenuActive) return; // 防止右键菜单时触发
    var dataObj = obj.data;
    //console.log(dataObj, diagram, tool);
    //   var toolTipDIV = document.getElementById("tooltipInfo");
    //   var pt = diagram.lastInput.viewPoint;
    //   toolTipDIV.style.left = pt.x + 10 + "px";
    //   toolTipDIV.style.top = pt.y + 10 + "px";
    //   //   document.getElementById("toolTipParagraph").textContent =
    //   //     "Tooltip for: " + 1111;
    //   toolTipDIV.style.display = "block";
    var tooltips = document.getElementById("tooltipInfo");
    tooltips.style.display = "block";
    var maxX = window.innerWidth - tooltips.offsetWidth;
    var maxY = window.innerHeight - tooltips.offsetHeight;
    if (lastX > maxX) {
        tooltips.style.left = maxX + "px";
    } else {
        tooltips.style.left = lastX + "px";
    }

    if (lastY > maxY) {
        tooltips.style.top = maxY -  40 + "px";
    } else {
        tooltips.style.top = lastY - 40 + "px";
    }
}

function hideToolTip() {
    // $("#tooltipInfo").html("");
    setTimeout(function () {
        $("#tooltipInfo").hide();
    }, 300);
}

// 获取所有节点数组
function getAllNode() {
    return myDiagram.model.nodeDataArray;
}

// 判断是否存在节点
function isExistNode(nodeId) {
    /*return myDiagram.model.nodeDataArray.some(function (item) {
        return item.key == nodeId;
    });*/
    var arr = [];
    console.log(myDiagram.model.nodeDataArray);
    myDiagram.model.nodeDataArray.forEach(function (item) {
        item.id == nodeId ? arr.push(item) : "";
    });
    return arr.length;
}

// 自定义生成唯一key的函数
function generateUniqueKey(model) {
    var key;
    do {
        key = "node" + Math.floor(Math.random() * 1000000);
    } while (model.findNodeDataForKey(key) !== null);
    return key;
}
// 设置节点是否可拖动
function setViewMode(isViewMode) {
    myDiagram.nodes.each(function (node) {
    //console.log(node)
        node.draggable = !isViewMode;  // 根据模式设置拖动能力
    });
}
