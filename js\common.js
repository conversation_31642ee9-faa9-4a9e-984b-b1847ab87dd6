﻿/*!
 * common.js v25.1.17
 *
 * Copyright 2028 Cowin
 * Author: <PERSON>
 * Editor: <PERSON>
 *
 * Date: 2024-01-17
 */

win = window;
widnow = window;
if (!!window.require)
{
	try
	{
		eval('(const { type } = require("../lib/layui/excel");)');
	}
	catch (x) { }
}

if (!window.CS || !window.CS.Settings || !window.CS.Variables)
{
	setInterval(function ()
	{
		window.location.reload();
		if (window.Data)
			window.Data('Reload', window.location.href);
		console.log('Reload', window.location.href);
	}, 100);
}
window.CLIENT_TYPE = { Unknow: 0, Browser: 1, Edge: 2, CefIE: 3 };

if (!!window.top.window.external &&
	!!window.top.window.external.Cowin)//在 Chrome 或 IE 客户端
{
	if (!window.top.window.clientType)
	{
		window.clientType = window.CLIENT_TYPE.CefIE;
		window.clientCefIE = window.clientType;
		try
		{
			window.shell = window.top.window.external;
			window.Data = window.shell.Data;
			if (!window.shell.Cowin || !window.Data('Cowin', ''))
			{
				window.shell = {};
				window.clientIE = window.clientType;
			}
			else if (!!window.Data('Browser', 'WebView'))
			{
				window.clientType = window.CLIENT_TYPE.Edge;
				window.clientEdge = window.clientType;
			}
			else if (!!window.Data('Browser', 'InternetExplorer'))
			{
				window.clientIE = window.clientType;
			}
			else
			{
				window.clientCef = window.clientType;
				window.clientCEF = window.clientType;
			}
		}
		catch (x)
		{
			window.shell = {};
			window.clientIE = window.clientType;
		}
	}
	else
	{
		if (!!window.top.window.clientBrowser)
			window.clientBrowser = window.top.window.clientType;
		if (!!window.top.window.clientEdge)
			window.clientEdge = window.top.window.clientType;
		if (!!window.top.window.clientCefIE)
			window.clientCefIE = window.top.window.clientType;
		if (!!window.top.window.clientCef)
			window.clientCef = window.top.window.clientType;
		if (!!window.top.window.clientCEF)
			window.clientCED = window.top.window.clientType;
		if (!!window.top.window.clientIE)
			window.clientIE = window.top.window.clientType;
		window.clientType = window.top.window.clientType;
		window.shell = window.top.window.shell;
	}
}
else if (!!window.top.window.chrome &&
	!!window.top.window.chrome.webview &&
	!!window.top.window.chrome.webview.hostObjects &&
	!!window.top.window.chrome.webview.hostObjects.sync &&
	!!window.top.window.chrome.webview.hostObjects.sync.external &&
	!!window.top.window.chrome.webview.hostObjects.sync.external.Cowin)//在 Edge 客户端
{
	window.clientType = window.CLIENT_TYPE.Edge;
	window.clientEdge = window.clientType;
	window.external = window.top.window.chrome.webview.hostObjects.sync.external;
	window.shell = window.top.window.external;
}
else//网页
{
	window.clientType = window.CLIENT_TYPE.Browser;
	window.clientBrowser = window.clientType;
}
if (!window.shell)
{
	if (!window.console)
		window.console = {
			log: function () { },
			info: function () { },
			warn: function () { },
			error: function () { },
			debug: function () { },
			disabled: true
		};
}
else
{
	if (!!window.clientIE)
	{
		Object.defineProperty(window.shell, 'Cowin', { get: function () { return window.top.window.external.Cowin; } });
		Object.defineProperty(window.shell, 'Scheme', { get: function () { return window.top.window.external.Scheme; } });
		Object.defineProperty(window.shell, 'Product', { get: function () { return window.top.window.external.Product; } });
		Object.defineProperty(window.shell, 'Version', { get: function () { return window.top.window.external.Version; } });
		Object.defineProperty(window.shell, 'NetPoint', { get: function () { return window.top.window.external.NetPoint; } });
		Object.defineProperty(window.shell, 'WebPoint', { get: function () { return window.top.window.external.WebPoint; } });
		Object.defineProperty(window.shell, 'LocalUrl', { get: function () { return window.top.window.external.LocalUrl; } });
		Object.defineProperty(window.shell, 'ProxyUrl', { get: function () { return window.top.window.external.ProxyUrl; } });
		Object.defineProperty(window.shell, 'Title', { get: function () { return window.top.window.external.Title; } });
		Object.defineProperty(window.shell, 'Text', { get: function () { return window.top.window.external.Text; }, set: function (value) { window.top.window.external.Text = value; } });
		window.shell.Data = function (name, value)
		{
			return window.top.window.external.Data(name, !arguments.length ? '' : value);
		};
		window.shell.Value = function (name, value)
		{
			return window.top.window.external.Value(name, !arguments.length ? 0 : value);
		};
		window.shell.Store = function (name, value)
		{
			return window.top.window.external.Store(name, !arguments.length ? '' : value);
		};
		window.shell.SendData = function (name, value)
		{
			return window.top.window.external.SendData(name, value);
		};
		window.shell.SendPipe = function (name, value)
		{
			return window.top.window.external.SendPipe(name, value);
		};
		window.shell.SendJS = function (name, value)
		{
			return window.top.window.external.SendJS(name, value);
		};
		window.shell.Log = function (content, type)
		{
			return window.top.window.external.Log(content, !type ? 0 : type);
		};
		window.shell.LogInfo = function (content)
		{
			return window.top.window.external.LogInfo(content);
		};
		window.shell.LogWarn = function (content)
		{
			return window.top.window.external.LogWarn(content);
		};
		window.shell.LogError = function (content)
		{
			return window.top.window.external.LogError(content);
		};
		window.shell.LogTrace = function (content)
		{
			return window.top.window.external.LogTrace(content);
		};
		window.shell.Trace = function (content, color)
		{
			return window.top.window.external.Trace(content, !color ? 0 : color);
		};
		window.shell.Prompt = function (name, text, title, options)
		{
			if (!arguments.length)
				return 0;

			if (arguments.length == 1)
				return window.top.window.external.Prompt(null, arguments[0], '', 0);

			return window.top.window.external.Prompt(!name ? null : name, text, !title ? '' : title, !options ? 0 : options);
		};
		window.shell.Developer = function ()
		{
			return window.top.window.external.Developer();
		};
		window.shell.ControlText = function (name, text)
		{
			return window.top.window.external.ControlText(name, text);
		};
	}
	if (!window.clientCef)
		window.Data = function (name, value) { return window.shell.Data(name, !value || typeof (value) == typeof (name) ? value : value.toString()); };
	else
		window.Data = window.shell.Data;
	window.Value = window.shell.Value;
	if (!window.clientCef)
		window.Store = function (name, value) { return window.shell.Store(name, !value || typeof (value) == typeof (name) ? value : value.toString()); };
	else
		window.Store = window.shell.Store;
	if (!window.clientIE)
		window.Log = function (content, type)
		{
			return window.shell.Log(content, !type ? 0 : type);
		};
	window.LogInfo = window.shell.LogInfo;
	window.LogWarn = window.shell.LogWarn;
	window.LogError = window.shell.LogError;
	window.LogDebug = window.shell.LogDebug;
	if (!window.clientIE)
		window.Trace = function (content, color)
		{
			return window.shell.Trace(content, !color ? 0 : color);
		};
	if (!window.clientIE)
		window.Prompt = function (name, text, title, options)
		{
			if (!arguments.length)
				return 0;

			if (arguments.length == 1)
				return window.shell.Prompt(null, arguments[0], '', 0);

			return window.shell.Prompt(!name ? null : name, text, !title ? '' : title, !options ? 0 : options);
		};
	window.localUrl = window.Data('ENV', 'LocalUrl');
	window.slaveUrl = window.Data('ENV', 'SlaveUrl');
	window.clientVer = function ()
	{
		this.text = window.Data('Version', '');
		this.parts = this.text.split('.');
		this.major = parseInt(this.parts[0]);
		this.minor = parseInt(this.parts[1]);
		this.build = parseInt(this.parts[2]);
		this.revision = parseInt(this.parts[3]);
		this.value = parseFloat((Math.floor(this.major / 100) * 1000 + Math.floor(this.minor / 100)) + (Math.floor(this.build / 100) / 1000) + '' + this.parts[3]);
		this.toLocaleString = function ()
		{
			return Math.floor(this.major / 100) + '.' + Math.floor(this.minor / 100) + '.' + Math.floor(this.build / 100);
		};
		this.toString = function ()
		{
			return this.text;
		};
		this.valueOf = function ()
		{
			return this.value;
		};
		this.proto = window.clientVer;
	};
	window.clientVersion = new window.clientVer();
	window.clientVer = window.clientVersion;
	if (!window.console)
		window.console = {
			log: function (s) { window.LogInfo(s); },
			info: function (s) { window.LogInfo(s); },
			warn: function (s) { window.LogWarn(s); },
			error: function (s) { window.LogError(s); },
			debug: function (s) { window.LogDebug(s); },
			disabled: true
		};
}

window.CS.getExtraData = function ()
{
	return null;
};

/*禁用BackSpace键盘 */
function banBackSpace(e)
{
	var ev = e || window.event;//获取event对象   
	var obj = ev.target || ev.srcElement;//获取事件源   
	var t = obj.type || obj.getAttribute('type');//获取事件源类型  
	//获取作为判断条件的事件类型
	var vReadOnly = obj.getAttribute('readonly');
	//处理null值情况
	vReadOnly = (vReadOnly == "") ? false : vReadOnly;
	//当敲Backspace键时，事件源类型为密码或单行、多行文本的，
	//并且readonly属性为true或enabled属性为false的，则退格键失效
	var flag1 = (ev.keyCode == 8 && (t == "password" || t == "text" || t == "textarea")
		&& vReadOnly == "readonly") ? true : false;
	//当敲Backspace键时，事件源类型非密码或单行、多行文本、数字的，则退格键失效
	var flag2 = (ev.keyCode == 8 && t != "password" && t != "text" && t != "textarea" && t != "number")
		? true : false;

	//判断
	if (flag2 || flag1)
		return false;

}
window.onload = function ()
{
	if (!window.shell)
	{
		document.addEventListener('mousemove', function () { window.lastInput = Date.now(); });
		document.addEventListener('keydown', function () { window.lastInput = Date.now(); });
		document.addEventListener('keyup', function () { window.lastInput = Date.now(); });
	}
	else
	{
		document.addEventListener('mousemove', function () { window.lastInput = window.Value('ProcessInput', 1); });
		document.addEventListener('keydown', function () { window.lastInput = window.Value('ProcessInput', 1); });
		document.addEventListener('keyup', function () { window.lastInput = window.Value('ProcessInput', 1); });
	}
	//禁止后退键 作用于Firefox、Opera
	document.onkeypress = banBackSpace;
	//禁止后退键  作用于IE、Chrome
	document.onkeydown = banBackSpace;
};

function godCSS(css)
{
	var vs = window.CS.css;
	if (!!vs)
	{
		if (!!css)
		{
			for (var i = vs.length - 1; i >= 0; i--)
			{
				if (vs[i] == css)
					return i + 1;
			}

			return 0;
		}

		for (var i = vs.length - 1; i >= 0; i--)
			$(vs[i]).remove();
		return vs;
	}

	if (!css)
		return vs;

	vs = css.split(',');
	for (var i = vs.length - 1; i >= 0; i--)
		vs[i] = vs[i].replace(/^\s+|\s+$/img, '');
	window.CS.css = vs;
	return vs;
}

function dpi(v)
{
	var i = window.devicePixelRatio;
	if (!arguments.length)
		v = this;
	if (!i)
		return v;

	return v * i;
}

function lang()
{
	var me = false;
	var s = arguments[0];
	if (this.substring == ''.substring)
	{
		s = this + '';
		me = true;
	}
	if (!s)
		return s;

	if (!window.langs)
	{
		if (me && arguments.length == 0)
			return s;

		if (!me && arguments.length == 1)
			return s;

		var i = s.indexOf('{')
		if (i < 0 || s.indexOf('}', i) < 0)
			return s;

		var ps = [];
		for (var i = arguments.length - 1; i > 0; i--)
			ps.unshift(arguments[i]);
		if (me)
			ps.unshift(arguments[0]);
		for (var i = ps.length - 1; i >= 0; i--)
			s = s.replace(new RegExp('\\{' + i + '\\}', 'img'), ps[i] + '')
		return s;
	}

	if (me || (!!arguments.length && typeof (arguments.callee) != typeof (s)))//s = $
	{
		var v = s.trim();
		v = window.langs[v.toUpperCase()];
		if (!v)
		{
			if (s.length < 2)
				return s;

			var r = /[^《》【】—：；，。！？\x00 -\xFF]+/img;
			if (s.replace(r, '').replace(/[\r\n\t\s ]+/img, '') != '')
				s = s.replace(r, lang);
			v = s.replace(/[^\x00 -\xFF]/img, lang);
		}
		if (me && arguments.length == 0)
			return v;

		if (!me && arguments.length == 1)
			return v;

		var i = v.indexOf('{')
		if (i < 0 || v.indexOf('}', i) < 0)
			return v;

		var ps = [];
		for (var i = arguments.length - 1; i > 0; i--)
			ps.unshift(arguments[i]);
		if (me)
			ps.unshift(arguments[0]);
		for (var i = ps.length - 1; i >= 0; i--)
			v = v.replace(new RegExp('\\{' + i + '\\}', 'img'), ps[i] + '')
		return v;
	}

	var $ = window.$;
	if (!$)
		$ = s;
	$('option').each(function ()
	{
		var a = $(this).text();
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).text(b);
		}
	});
	$('span').each(function ()
	{
		var a = $(this).text();
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).text(b);
		}
	});
	$('input').each(function ()
	{
		var a = $(this).val();
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).val(b);
		}
		var a = $(this).attr('placeholder');
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).attr('placeholder', b);
		}
	});
	$('a').each(function ()
	{
		var a = $(this).text();
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).text(b);
		}
	});
	$('.layui-form-label').each(function ()
	{
		var a = $(this).text();
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).text(b);
		}
	});
	$('li').each(function ()
	{
		var a = $(this).text();
		if (!!a)
		{
			var b = a.lang();
			if (!!b && b != a)
				$(this).text(b);
		}
	});
}
String.prototype.lang = lang;

function parseQuery(data, urls)
{
	var ks = urls;
	if (!ks.shift || !ks.push)
	{
		ks = [];
		for (var i = arguments.length - 1; i > 0; i--)
			ks[i - 1] = arguments[i];
	}
	var l = ks.length;
	if (l <= 0)
		return data;

	if (!data)
		data = {};
	for (var i = 0; i < l; i++)
		$.parseUrl(ks[i], data);
	if (!data.EditUser)
	{
		if (!!data.Editor)
			data.EditUser = data.Editor;
		else if (!!data.Creator)
			data.EditUser = data.Creator;
		else
			return data;
	}
	l = data.EditUser.lastIndexOf(',');
	if (l > 0)
		data.EditUser = data.EditUser.substring(l + 1, data.EditUser.length);
	return data;
}
function parseQueryKey(data, keys)
{
	var ks = keys;
	if (!ks.shift || !ks.push)
	{
		ks = [];
		for (var i = arguments.length - 1; i > 0; i--)
			ks[i - 1] = arguments[i];
	}
	for (var i = ks.length - 1; i >= 0; i--)
		ks[i] = data[ks[i]];
	return parseQuery(data, ks);
}

function Uri(uri)
{
	if (this.__proto__ != Uri.prototype)
		return new Uri(uri);

	if (!arguments.length)
		uri = window.location.href;
	else if (!uri)
		return;
	else if (!!uri.href && !!uri.href.indexOf)
		uri = uri.href;
	else if (!!uri.Href && !!uri.Href.indexOf)
		uri = uri.Href;
	else if (!uri.indexOf)
		return;

	var i = 0;
	this.Href = uri;
	if (/^[a-zA-Z0-9-]+:\/\//.test(uri))
	{
		i = uri.indexOf('://');
		this.Host = uri.substring(i + 3, uri.length);
		this.Scheme = uri.substring(0, i);
		i = this.Host.indexOf('/');
		if (i <= 0)
			return;

		this.Path = this.Host.substring(i, this.Host.length);
		this.Host = this.Host.substring(0, i);
		i = this.Host.indexOf('@');
		if (i > 0)
		{
			this.User = this.Host.substring(0, i);
			this.Host = this.Host.substring(i + 1, this.Host.length);
		}
		i = this.Host.lastIndexOf(':');
		if (i > 0)
		{
			this.Port = this.Host.substring(i + 1, this.Host.length);
			this.Port = parseInt(this.Port);
			if (this.Host.lastIndexOf(':' + this.Port) != i)
				this.Port = 0;
			else
				this.Domain = this.Host.substring(0, i);
		}
		else
		{
			this.Domain = this.Host;
		}
	}
	else
	{
		this.Path = uri;
	}
	i = this.Path.indexOf('#');
	if (i >= 0)
	{
		this.Anchor = this.Path.substring(i + 1, this.Path.length);
		this.Path = this.Path.substring(0, i);

	}
	i = this.Path.indexOf('?');
	if (i >= 0)
	{
		this.Search = this.Path.substring(i + 1, this.Path.length);
		this.Path = this.Path.substring(0, i);
	}
	i = this.Path.lastIndexOf('/');
	if (i >= 0)
		this.File = this.Path.substring(i, this.Path.length);
	Object.defineProperty(this, 'port', {
		get: function ()
		{
			if (!this.href && !!this.Href)
				this.toLower();
			return this.Port;
		}
	});
	Object.defineProperty(this, 'PORT', {
		get: function ()
		{
			if (!this.HREF && !!this.Href)
				this.toUpper();
			return this.Port;
		}
	});
	Object.defineProperty(this, 'query', {
		get: function ()
		{
			if (!this.Query)
			{
				this.Query = {};
				if (!this.Search)
					return {};

				this.Query = $.parseUrl(this.Search, this.Query);
			}
			return this.Query;
		}
	});
	Object.defineProperty(this, 'QUERY', {
		get: function ()
		{
			if (!this.Query)
			{
				this.Query = {};
				if (!this.Search)
					return {};

				this.Query = $.parseUrl(this.Search, this.Query);
			}
			return this.Query;
		}
	});
	Object.defineProperty(this, 'Protocol', { get: function () { return this.Scheme; } });
	Object.defineProperty(this, 'protocol', {
		get: function ()
		{
			if (!this.href && !!this.Href)
				this.toLower();
			return this.scheme;
		}
	});
	Object.defineProperty(this, 'PROTOCOL', {
		get: function ()
		{
			if (!this.HREF && !!this.Href)
				this.toUpper();
			return this.SCHEME;
		}
	});
	Object.defineProperty(this, 'Authority', { get: function () { return !this.Port ? this.Host : this.Host + ';' + this.Port; } });
	Object.defineProperty(this, 'authority', {
		get: function ()
		{
			if (!this.href && !!this.Href)
				this.toLower();
			return !this.Port ? this.host : this.host + ';' + this.Port;
		}
	});
	Object.defineProperty(this, 'AUTHORITY', {
		get: function ()
		{
			if (!this.HREF && !!this.Href)
				this.toUpper();
			return !this.Port ? this.HOST : this.HOST + ';' + this.Port;
		}
	});
}
Uri.prototype.lower = function ()
{
	if (!!this.href || !this.Href)
		return this;

	this.href = this.Href.toLowerCase();
	if (!!this.Scheme)
		this.scheme = this.Scheme.toLowerCase();
	if (!!this.Host)
		this.host = this.Host.toLowerCase();
	if (!!this.User)
		this.user = this.User.toLowerCase();
	if (!!this.Domain)
		this.domain = this.Domain.toLowerCase();
	if (!!this.Path)
		this.path = this.Path.toLowerCase();
	if (!!this.File)
		this.file = this.File.toLowerCase();
	if (!!this.Search)
		this.search = this.Search.toLowerCase();
	if (!!this.Anchor)
		this.anchor = this.Anchor.toLowerCase();
	return this;
};
Uri.prototype.toLower = Uri.prototype.lower;
Uri.prototype.toLowerCase = Uri.prototype.lower;
Uri.prototype.upper = function ()
{
	if (!!this.HREF || !this.Href)
		return this;

	this.HREF = this.Href.toUpperCase();
	if (!!this.Scheme)
		this.SCHEME = this.Scheme.toUpperCase();
	if (!!this.Host)
		this.HOST = this.Host.toUpperCase();
	if (!!this.User)
		this.USER = this.User.toUpperCase();
	if (!!this.Domain)
		this.DOMAIN = this.Domain.toUpperCase();
	if (!!this.Path)
		this.PATH = this.Path.toUpperCase();
	if (!!this.File)
		this.FILE = this.File.toUpperCase();
	if (!!this.Search)
		this.SEARCH = this.Search.toUpperCase();
	if (!!this.Anchor)
		this.ANCHOR = this.Anchor.toUpperCase();
	return this;
};
Uri.prototype.toUpper = Uri.prototype.upper;
Uri.prototype.toUpperCase = Uri.prototype.upper;
Uri.prototype.getQuery = function (key)
{
	if (!this.Query)
	{
		this.Query = {};
		if (!this.Search)
			return {};

		this.Query = $.parseUrl(this.Search, this.Query);
		for (var k in this.Query)
		{
			if (!k || k[0] == '_' || typeof (this.Query[k]) != typeof (''))
				continue;

			if (!this[k])
				this[k] = this.Query[k];
			if (!k.toUpperCase)
				continue;

			var t = k.toUpperCase();
			if (!this[t])
				this[t] = this.Query[k];
			t = k.toLowerCase();
			if (!this[t])
				this[t] = this.Query[k];
		}
	}
	if (!arguments.length)
		return this.Query;

	var v = this.Query[key];
	if (typeof (v) != typeof (window.undefined) || !key.toUpperCase)
		return v;

	key = key.toUpperCase();
	for (var k in this.Query)
	{
		if (!!k && !!k.toUpperCase && k.toUpperCase() == key)
			return this.Query[k];
	}
	return null;
};
Uri.prototype.toString = function () { return this.Href; };
Uri.prototype.isEmpty = function () { return !this.Href; };
if(!window.Url)
	Url = Uri;

window.__get_object_member = function (instance, name)
{
	if (arguments.length == 0)
		return window.undefined;

	if (arguments.length == 1)
	{
		name = arguments[0];
		instance = this;
	}
	if (!instance)
		return window.undefined;

	var v = instance[name];
	if (typeof (v) != typeof (window.undefined))
		return v;

	v = typeof ('');
	if (typeof (name) != v)
		return window.undefined;

	name = name.toLowerCase();
	for (var i in instance)
	{
		if (typeof (i) == v && i.toLowerCase() == name)
			return instance[i];
	}
	return window.undefined;
};
Uri.prototype.prop = window.__get_object_member;
Uri.prototype.memb = window.__get_object_member;
Uri.prototype.member = window.__get_object_member;
window.prop = window.__get_object_member;
window.memb = window.__get_object_member;
window.member = window.__get_object_member;
window.CS.prop = function (name) { return window.__get_object_member(window.CS, name); };
window.CS.memb = function (name) { return window.__get_object_member(window.CS, name); };
window.CS.member = function (name) { return window.__get_object_member(window.CS, name); };
window.CS.Settings.prop = function (name) { return window.__get_object_member(window.CS.Settings, name); };
window.CS.Settings.memb = function (name) { return window.__get_object_member(window.CS.Settings, name); };
window.CS.Settings.member = function (name) { return window.__get_object_member(window.CS.Settings, name); };
window.CS.Variables.prop = function (name) { return window.__get_object_member(window.CS.Variables, name); };
window.CS.Variables.memb = function (name) { return window.__get_object_member(window.CS.Variables, name); };
window.CS.Variables.member = function (name) { return window.__get_object_member(window.CS.Variables, name); };
window.CS.isConfigUser = function (name) { return window.__get_object_member(window.CS.Settings, !name ? window.CS.ConfigData.USER + window.login.Name : window.CS.ConfigData.USER + name); };

var limitByteLengthPwd = 16;//限定存入数据库中的字节长度 最大10
var minLimitByteLength = 4;//限定存入数据库中的字节长度 最小4
//String 扩展获取byte的字节数量 汉子 长度平均2个长度 数字和字母一个长度
String.prototype.getByteLength = function ()
{
	var len = 0;
	for (var i = 0; i < this.length; i++)
	{
		if (this[i].match(/[^x00-xff]/ig) != null) //全角 
			len += 2;
		else
			len += 1;
	};
	return len;
}
String.prototype.getSpaceWhite = function ()
{
	var p = new RegExp(/\s+/g);//空格
	if (p.test(this))
		return true;
	return false;
}
/*获取当前请求地址参数*/
function getQueryVariable(variable)
{
	var query = window.location.search.substring(1);
	var vars = query.split("&");
	for (var i = 0; i < vars.length; i++)
	{
		var pair = vars[i].split("=");
		if (pair[0] === variable) { return pair[1]; }
	}
	return "";
}
function debounce(fn, delay, immediate)
{
	immediate = immediate || false;
	var timer = null;

	return function ()
	{
		var args = arguments;
		var context = this;

		if (immediate && !timer)
		{
			fn.apply(context, args);
		}

		if (timer)
		{
			clearTimeout(timer);
		}

		timer = setTimeout(function ()
		{
			if (!immediate)
			{
				fn.apply(context, args);
			}
			timer = null;
		}, delay);
	};
}
function cleanSpelChar(th)
{
	function Fn(th)
	{
		if (/["'<>|%=;&+]/.test(th.value))//)(
		{
			$(th).val(th.value.replace(/["'<>|%=;&+]+/img, ""));//)(
		}
	}
	debounce(Fn(th), 300)
}
function cleanNoIPADRESS(th)
{
	if (/["'<>|%=;&+]qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM/.test(th.value))//)(
	{
		$(th).val(th.value.replace(/["'<>|%=;&+]qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM/img, ""));//)(
	}
}
function limitTextAreaCount(th)
{
	if (th.value.length >= 200)
	{
		$(th).val(th.value.substring(0, 199));
	}
}
/**
 * 检查是否为特殊字符
 * @param {any} th
 */
function IsCheckSpecialChar(th)
{
	if (/["'<>|%;&+]/.test(th))
		return 1;
	return 0;
}

function cleanSpelCharUser(th)
{
	if (/[$~！!@#￥^&*()（）?_+"'<>|%;&+{}【】“”`:\/.,。，、·=‘’；》《？-]/.test(th.value))//)(
	{
		$(th).val(th.value.replace(/[$~！!@#￥^&*()（）?_+"'<>|%;&+{}【】“”`:\/.,。，、·=‘’；》《？-]+/img, ""));//)(
	}
}

/**
 * 用户管理，检查是否为特殊字符
 * @param {any} th
 */
function IsCheckSpecialCharUser(th)
{
	if (/[$~！!@#￥^&*()（）?_+"'<>|%;&+{}【】“”`:\/.,。，、·=‘’；》《？-]/.test(th))
		return 1;
	return 0;
}

function doLockTool(id, type, time)
{
	var ajax = {};
	ajax.href = 'LockTool.do';
	ajax.type = 'GET'
	ajax.data = {};
	ajax.data['ID'] = id;
	if (!!type)
		ajax.data['Lock'] = type;
	if (!!time)
		ajax.data['Expire'] = time;
	ajax.data['_'] = Date.now();
	ajax.success = function (result)
	{
		if (result.indexOf('Success') > 0)
		{
			layui.layer.msg(lang('操作成功'));
			return;
		}

		try
		{
			var v = $.eval(result);
			layui.layer.msg(lang('操作失败：') + v.msg, { icon: 2 });
		}
		catch (x)
		{
			layui.layer.msg(lang('操作失败：') + x.message + '\r\n' + result, { icon: 2 });
		}
	};
	ajax.beforeSend = function () { };
	$.startAjax(ajax);
}

function onAjaxError(xhr)
{

	if (xhr.status == 401)
		layer.alert(lang('您没有此项操作的权限，请与系统管理员联系！'), { btn: [lang('确定'), lang('取消')], title: lang('提示') });
	else
		layer.alert(xhr.responseText + lang(":请与系统管理员联系！！！"), { btn: [lang('确定'), lang('取消')], title: lang('提示') });
}

function sendPipe(name, value)
{
	if (!window.shell)
		return -1;

	//todo: using WebSocket to send pipe
	window.shell.SendPipe(name, value);
}

window.dbOnWS = function (name, value)
{
	if (!window.wsStorage)
	{
		window.wsStorage = {};
		window.wsStorage.i = 0;
		window.wsStorage.onopen = function () { console.log('window.wsStorage.onopen'); };
		window.wsStorage.onmessage = function (e)
		{
			var t = e.data;
			var i = t.indexOf('/', 6);
			if (i < 6)
				return;

			var k = t.substring(6, i);
			var c = window.wsStorage[k];
			if (!c)
				return;

			window.wsStorage[k] = null;
			delete window.wsStorage[k];
			if (!c.length || !c[0].call)
				return;

			t = t.substring(i + 1, t.length)
			c[0](t);
		};
		window.wsStorage.onerror = function (e) { console.log('window.wsStorage.onerror ' + e.data); };
		window.wsStorage.onclose = function ()
		{
			var ws = new window.WebSocket('ws://localhost:7822/Message.do?ldb');
			ws.onopen = window.wsStorage.onopen
			ws.onmessage = window.wsStorage.onmessage;
			ws.onerror = window.wsStorage.onerror;
			ws.onclose = window.wsStorage.onclose;
			window.wsStorage.ws = ws;
		};
		window.wsStorage.onclose.call(window.wsStorage);
		window.wsStorage.onpromise = function (ok, error)
		{
			var k = 'CB' + window.wsStorage.i++;
			window.wsStorage[k] = [ok, error, window.wsStorage.data, k];
			try
			{
				window.wsStorage.ws.send('ldb://' + k + '/' + window.wsStorage.data);
			}
			catch (x)
			{
				window.wsStorage[k] = null;
				delete window.wsStorage[k];
				error(x);
			}
		};
		window.wsStorage.asyncSend = function (data)
		{
			window.wsStorage.data = data;
			window.wsStorage.promise = new window.Promise(window.wsStorage.onpromise);
			return window.wsStorage.promise;
		};
		window.wsStorage.syncSend = async function (data) { return await window.wsStorage.asyncSend(data); };
		window.wsStorage.keys = function (i) { return window.wsStorage.syncSend('?keys'); };
		window.wsStorage.count = function () { return window.wsStorage.syncSend('?count'); };
		window.wsStorage.remove = function (i) { return window.wsStorage.syncSend('?remove=' + i); };
		window.wsStorage.get = function (k) { return window.wsStorage.syncSend(k); };
		window.wsStorage.set = function (k, v) { return window.wsStorage.syncSend(k + '/' + v); };
	}
	if (arguments.length > 2 && (arguments[2] == name || arguments[2] == 'clear'))
	{
		var ks = [];
		if (!name)
		{
			if (!value)
				return value;

			ks = window.wsStorage.keys();
			for (var i = ks.length - 1; i >= 0; i--)
			{
				name = ks[i];
				name = window.wsStorage.get(key);
				if (name.indexOf(value) == 0)
				{
					window.wsStorage.remove(ks[i]);
					if (!!window.clientIE)
						window.shell.SendData(ks[i], '');
				}
			}
			return value;
		}

		ks = window.wsStorage.keys();
		for (var i = ks.length - 1; i >= 0; i--)
		{
			value = ks[i];
			if (value.indexOf(name) == 0)
			{
				window.wsStorage.remove(value);
				if (!!window.clientIE)
					window.shell.SendData(value, '');
			}
		}
		return value;
	}

	if (arguments.length == 1)
	{
		if (!!window.clientIE && name.substring(0, 3) == '(v)')
			value = window.shell.SendData(name, null);
		else
			value = window.wsStorage.get(name);
	}
	else if (!value && (typeof (value) == typeof (window) || typeof (value) == typeof (window.undefined)))
	{
		window.wsStorage.remove(name);
		if (!!window.clientIE)
			window.shell.SendData(window.wsStorage.key(i), '');
	}
	else
	{
		window.wsStorage.set(name, value);
		if (!!window.clientIE)
			window.shell.SendData(name, value);
	}
	return value;
};
window.dbOnAjax = function (name, value)
{
	if (!window.ajaxStorage)
	{
		window.ajaxStorage = {};
		window.ajaxStorage.syncSend = function (v)
		{
			var a = $.get({ url: 'http://127.0.0.1:7823/.0/ldb://0/' + Date.now(), data: 'ldb://0/' + v, method: 'POST', contentType: 'text/plain', async: 0 });
			var t = a.responseText;
			var i = t.indexOf('/', 6);
			if (i > 6)
				return t.substring(i + 1, t.length);

			return t;
		};
		window.ajaxStorage.asyncSend = function (v) { return $.get({ url: 'http://127.0.0.1:7823/.0/ldb://0/' + Date.now(), data: 'ldb://0/' + v, method: 'POST', contentType: 'text/plain' }); };
		window.ajaxStorage.keys = function (i) { return window.ajaxStorage.syncSend('?keys').split('/'); };
		window.ajaxStorage.count = function () { return parseInt(window.ajaxStorage.syncSend('?count')); };
		window.ajaxStorage.remove = function (i) { window.ajaxStorage.asyncSend('?remove=' + i); };
		window.ajaxStorage.get = function (k) { return window.ajaxStorage.syncSend(k); };
		window.ajaxStorage.set = function (k, v) { window.ajaxStorage.asyncSend(k + '/' + v); };
	}
	if (arguments.length > 2 && (arguments[2] == name || arguments[2] == 'clear'))
	{
		var ks = [];
		if (!name)
		{
			if (!value)
				return value;

			ks = window.ajaxStorage.keys();
			for (var i = ks.length - 1; i >= 0; i--)
			{
				name = ks[i];
				name = window.ajaxStorage.get(key);
				if (name.indexOf(value) == 0)
				{
					window.ajaxStorage.remove(ks[i]);
					if (!!window.clientIE)
						window.shell.SendData(ks[i], '');
				}
			}
			return value;
		}

		ks = window.ajaxStorage.keys();
		for (var i = ks.length - 1; i >= 0; i--)
		{
			value = ks[i];
			if (value.indexOf(name) == 0)
			{
				window.ajaxStorage.remove(value);
				if (!!window.clientIE)
					window.shell.SendData(value, '');
			}
		}
		return value;
	}

	if (arguments.length == 1)
	{
		if (!!window.clientIE && name.substring(0, 3) == '(v)')
			value = window.shell.SendData(name, null);
		else
			value = window.ajaxStorage.get(name);
	}
	else if (!value && (typeof (value) == typeof (window) || typeof (value) == typeof (window.undefined)))
	{
		window.ajaxStorage.remove(name);
		if (!!window.clientIE)
			window.shell.SendData(window.ajaxStorage.key(i), '');
	}
	else
	{
		window.ajaxStorage.set(name, value);
		if (!!window.clientIE)
			window.shell.SendData(name, value);
	}
	return value;
};
window.dbOnShell = function (name, value)
{
	if (!window.shellStorage)
	{
		window.shellStorage = {};
		window.shellStorage.syncSend = function (v)
		{
			var t = window.Data('', 'ldb://0/' + v);
			var i = t.indexOf('/', 6);
			if (i > 6)
				return t.substring(i + 1, t.length);

			return t;
		};
		window.shellStorage.keys = function (i) { return window.shellStorage.syncSend('?keys').split('/'); };
		window.shellStorage.count = function () { return parseInt(window.shellStorage.syncSend('?count')); };
		window.shellStorage.remove = function (i) { return window.shellStorage.syncSend('?remove=' + i); };
		window.shellStorage.get = function (k) { return window.shellStorage.syncSend(k); };
		window.shellStorage.set = function (k, v) { return window.shellStorage.syncSend(k + '/' + v); };
	}
	if (arguments.length > 2 && (arguments[2] == name || arguments[2] == 'clear'))
	{
		var ks = [];
		if (!name)
		{
			if (!value)
				return value;

			ks = window.shellStorage.keys();
			for (var i = ks.length - 1; i >= 0; i--)
			{
				name = ks[i];
				name = window.shellStorage.get(key);
				if (name.indexOf(value) == 0)
				{
					window.shellStorage.remove(ks[i]);
					if (!!window.clientIE)
						window.shell.SendData(ks[i], '');
				}
			}
			return value;
		}

		ks = window.shellStorage.keys();
		for (var i = ks.length - 1; i >= 0; i--)
		{
			value = ks[i];
			if (value.indexOf(name) == 0)
			{
				window.shellStorage.remove(value);
				if (!!window.clientIE)
					window.shell.SendData(value, '');
			}
		}
		return value;
	}

	if (arguments.length == 1)
	{
		if (!!window.clientIE && name.substring(0, 3) == '(v)')
			value = window.shell.SendData(name, null);
		else
			value = window.shellStorage.get(name);
	}
	else if (!value && (typeof (value) == typeof (window) || typeof (value) == typeof (window.undefined)))
	{
		window.shellStorage.remove(name);
		if (!!window.clientIE)
			window.shell.SendData(window.shellStorage.key(i), '');
	}
	else
	{
		window.shellStorage.set(name, value);
		if (!!window.clientIE)
			window.shell.SendData(name, value);
	}
	return value;
};
window.dbOnLocal = function (name, value)
{
	if (!!window.localStorage)
	{
		if (arguments.length > 2 && (arguments[2] == name || arguments[2] == 'clear'))
		{
			if (!name)
			{
				if (!value)
					return value;

				for (var i = window.localStorage.length - 1; i >= 0; i--)
				{
					name = window.localStorage.key(i);
					name = window.localStorage.getItem(name);
					if (name.indexOf(value) == 0)
					{
						window.localStorage.removeItem(window.localStorage.key(i));
						if (!!window.clientIE)
							window.shell.SendData(window.localStorage.key(i), '');
					}
				}
				return value;
			}

			for (var i = window.localStorage.length - 1; i >= 0; i--)
			{
				value = window.localStorage.key(i);
				if (value.indexOf(name) == 0)
				{
					window.localStorage.removeItem(value);
					if (!!window.clientIE)
						window.shell.SendData(value, '');
				}
			}
			return value;
		}

		if (arguments.length == 1)
		{
			if (!!window.clientIE && name.substring(0, 3) == '(v)')
				value = window.shell.SendData(name, null);
			else
				value = window.localStorage.getItem(name);
		}
		else if (!value && (typeof (value) == typeof (window) || typeof (value) == typeof (window.undefined)))
		{
			window.localStorage.removeItem(name);
			if (!!window.clientIE)
				window.shell.SendData(window.localStorage.key(i), '');
		}
		else
		{
			window.localStorage.setItem(name, value);
			if (!!window.clientIE)
				window.shell.SendData(name, value);
		}
	}
	else
	{
		if (arguments.length == 1)
			value = window.Data('LDB://' + name, null);
		else
			window.Data('LDB://' + name, value);
	}
	return value;
};
if (!!window.clientBrowser)// || !!window.clientEdge)
{
	window.ldb = window.dbOnLocal;
	//window.ldb = window.dbOnWS;//has client process
}
else
{
	//window.ldb = window.dbOnLocal;
	//window.ldb = window.dbOnWS;
	if (window.webName == 'toolbar' || window.webName == 'side')
		window.ldb = window.dbOnAjax;
	else
		window.ldb = window.dbOnShell;
}

window.mem = function (name, key, value)
{
	if (!arguments.length)
		return null;

	if (typeof (name) == typeof (0))
		name = 'OpenVideo/' + name;
	else if (!name)
		return null;

	if (arguments.length > 2)
		value = window.Data('', 'mem://' + name + '/' + key + '/' + value);
	else if (arguments.length > 1 && !!key)
		value = window.Data('', 'mem://' + name + '/' + key);
	else
		value = window.Data('', 'mem://' + name + '/');
	return value;
}

window.slaveMessage = function (slave, handler, message, sync)
{
	var ajax = {};
	if (!slave)
		slave = '0'
	if (!handler)
		handler = 'openVideo';
	ajax.url = window.getSlaveUrl('/.' + slave + '/' + handler + '.do?_=' + Date.now());
	if (!!message)
	{
		ajax.data = message;
		if (message[0] != '?')
			ajax.method = 'POST';
	}
	ajax.sync = sync;
	ajax.success = function (result)
	{
		ajax.result = result;
	};
	ajax.error = function (request)
	{
		ajax.request = request;
	};
	if (sync)
		return $.get(ajax);

	return $.ajax(ajax);
};

if (!!window.RCS && !!window.RCS.ConfigData && !!window.RCS.ConfigData.Robot)
	window.doRPA = function (id)
	{
		if (!arguments.length)
			return window.RCS.ConfigData.Robot;

		var ajax = {};
		ajax.rpa = $(this).data('rpa');
		if (!!ajax.rpa && ajax.rpa.indexOf(window.RCS.ConfigData.Robot) > 0)
			ajax.rpa = VideoMenu.$StopScript;
		else
			ajax.rpa = VideoMenu.$RunScript + ExtraData.STATUS_WORKING;
		ajax.url = '../Video.do?ID=' + id + '&Sweep=' + ajax.rpa + '&_=' + Date.now();
		ajax.dataType = 'text';
		ajax.success = function (result)
		{
			if (result.indexOf('Success') > 0 || result.indexOf('Sweep://') >= 0)
			{
				layui.layer.msg(lang('操作成功'));
				return;
			}

			try
			{
				var v = $.eval(result);
				layui.layer.msg(lang('操作失败：') + v.msg, { icon: 2 });
			}
			catch (x)
			{
				layui.layer.msg(lang('操作失败：') + x.message + '\r\n' + result, { icon: 2 });
			}
		};
		ajax.error = function (q)
		{
			layer.msg('Error:\r\n' + q.responseText, { icon: 2 });
		};
		$.ajax(ajax);
		return window.RCS.ConfigData.Robot;
	};

window.getLocalUrl = function (path)
{
	if (!path)
		path = '';
	if (!!window.slaveUrl)
		return window.slaveUrl.substring(0, window.slaveUrl.indexOf('/', window.slaveUrl.indexOf('://') + 3) + 1).replace('/0.0.0.0:', '/127.0.0.1:') + '.0/' + path;

	if (!!window.localUrl)
		return window.localUrl.replace('/0.0.0.0:', '/127.0.0.1:') + path;

	if (location.hostname == '127.0.0.1' || location.hostname == '::1' || location.hostname.toLowerCase() == 'localhost')
		return location.protocol + '//' + location.host + '/' + path;

	if (!!window.CS['LocalHttp'])
		return location.protocol + '//' + window.CS['LocalHttp'].replace('0.0.0.0', '127.0.0.1') + '/' + path;

	return location.protocol + '//localhost:7822/' + path;
};
window.getSlaveUrl = function (path)
{
	if (!window.slaveUrl)
		return getLocalUrl(path);

	if (!path)
		path = '';
	return window.slaveUrl.replace('/0.0.0.0:', '/127.0.0.1:') + path;
};

window.isWeakPassword = function (password, account)
{
	var t = password;
	if (!t)
		return -1;

	var l = t.length
	if (!l)
		return -2;

	if (!!account && !!account.length)
	{
		if (t.indexOf(account) >= 0)
			return -3;

		if (account.indexOf(t) >= 0)
			return -4;
	}
	if (l < 6)
		return -6;

	for (var i = l - 1; i > 2; i--)
	{
		var a = t.charCodeAt(i);
		var b = t.charCodeAt(i - 1);
		var c = t.charCodeAt(i - 2);
		var d = t.charCodeAt(i - 3);
		if ((a == c && b == d) || (a == d && b == c))
			return 10000 + i;
	}

	for (var i = l - 1; i > 1; i--)
	{
		var a = t.charCodeAt(i);
		var b = t.charCodeAt(i - 1);
		var c = t.charCodeAt(i - 2);
		if (a - b == b - c && Math.abs(a - c) <= 2)
			return 20000 + i;
	}

	var v = (/[0-9]+/.test(t) ? 1 : 0) +
			(/[A-Z]+/.test(t) ? 1 : 0) +
			(/[a-z]+/.test(t) ? 1 : 0) +
			(/[^0-9A-Za-z]+/.test(t) ? 1 : 0);
	if (v < 3)
		return 3

	var f = function (tv, tl, tp)
	{
		for (var i = tl - 1; i > 1; i--)
		{
			var a = tp.indexOf(tv[i]);
			var b = tp.indexOf(tv[i - 1]);
			var c = tp.indexOf(tv[i - 2]);
			if (a >= 0 && b >= 0 && c >= 0 && a - b == b - c && Math.abs(a - c) <= 2)
				return i;
		}
		return 0;
	};
	v = f(t, l, 'qwertyuiop[]\\asdfghjkl;\'zxcvbnm,./');
	if (v > 0)
		return 60000 + v;

	v = f(t, l, 'zxcvbnm,./asdfghjkl;\'qwertyuiop[]\\');
	if (v > 0)
		return 70000 + v;

	v = f(t, l, 'QWERTYUIOP[]\\ASDFGHJKL;\'ZXCVBNM,./');
	if (v > 0)
		return 80000 + v;

	v = f(t, l, 'ZXCVBNM,./ASDFGHJKL;\'QWERTYUIOP[]\\');
	if (v > 0)
		return 90000 + v;

	if (l < 8)
		return 8;

	return 0;
};


function toJson(data)
{
	return JSON.stringify(data);
}

function naturalLen(data)
{
	if (data != undefined)
		return contianChinese(data) == true ? Math.round(data.length * 2 / 1024 * Math.pow(10, 4)) : Math.round(data.length / 1024 * Math.pow(10, 4));
	else
		return 100;
}
function contianChinese(data)
{
	var reg = /[^\x00-\xff]/ig;//判断是否存在中文和全角字符
	//    var reg=/[A-Za-z]*[a-z0-9_-]|\s$/;//判断是否包含数字字母下划线  当使用这个时如果只有部分是中文字符还可以使用英文字体
	if (reg.test(data))
	{
		return true;//存在中文
	}
	return false;//不存在中文
}

var re = /[\u4E00-\u9FA5]/g; //测试中文字符的正则

/*
 * 翻页翻译成英文方法
 * 
 */
function laypageEN()
{
	$('.layui-laypage-btn').text(lang('确定'));
	$('.layui-laypage-count-total').text(lang('共'));
	$('.layui-laypage-count-items').text(lang('条'));
	$('.layui-laypage-page').text(lang('页'));
	$('.layui-laypage-skip-page').text(lang('到第'));
}
