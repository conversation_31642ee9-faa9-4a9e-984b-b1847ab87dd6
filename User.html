﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>用户页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style>
		#addEdit .layui-inline {
			margin-right: 0px;
		}

		#addEdit .edit {
			width: 160px;
		}

		.borderStyle {
			border: 1px solid red;
		}

		.layui-layer-content {
			overflow: visible !important;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/excel.js"></script>
	<script src="js/request.js"></script>
	<script type="text/javascript">
		var col = columnsName();
		var configData_userGroup = null;
		var configData_role = null;
		layui.config({ base: 'lib/layui/' });
		layui.extend({ notice: '../layui_exts/notice/notice' });
		layui.use(['element', 'table', 'layer', 'jquery', 'form', 'notice', 'laypage', 'excel', 'upload'], function ()
		{
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
				, upload = layui.upload
				, excel = layui.excel
				, notice = layui.notice;
			if (!!notice)
			{
				notice.options =
				{
					closeButton: true,//显示关闭按钮
					debug: false,//启用debug
					positionClass: "toast-bottom-right",//弹出的位置,
					showDuration: "300",//显示的时间
					hideDuration: "500",//消失的时间
					timeOut: "3000",//停留的时间
					extendedTimeOut: "1000",//控制时间
					showEasing: "swing",//显示时的动画缓冲方式
					hideEasing: "linear",//消失时的动画缓冲方式
					iconClass: 'toast-info', // 自定义图标，有内置，如不需要则传空 支持layui内置图标/自定义iconfont类名
					onclick: null, // 点击关闭回调
				};
			}
			lang.call(this, $);
			configData = null;
			table.init('userVideo', []);
			limitPageCount = 20;
			currentPage = 1;
			var col = columnsName();
			//创建Table的列名
			tableIni = table.render({
				elem: '#userList'
				, even: true
				, sort: true
				, loading: false
				, toolbar: window.deny.UserOptionSet ? [] : '#usertoolbar'
				, defaultToolbar: []
				, id: "userList"
				, title: lang('用户数据表')
				, limit: limitPageCount
				, cols: [col]
				, height: 'full-185'
				, data: []
			});

			if (window.deny.UserOptionSet)
				$('#divBC').hide();

			tableIniEx = table.render({
				elem: '#userListEx'
				, defaultToolbar: []
				, id: "userListEx"
				, title: lang('用户数据表')
				, limit: limitPageCount
				, cols: [col]
				, data: []
			});
			pageCount = 1;
			getRoleConfig();
			setTimeout(function () { FillDataTable() }, 100);
			function getRoleConfig()
			{
				AjaxApi.request({
					url: "../Role/List.do",
					data: { Flags: 0, Take: 9999 },
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						configData_userGroup = getRoleUserGroup(res.data, 'userGroup')
						configData_role = getRoleUserGroup(res.data, 'role');
						initRoleSelect();
					}
				})
			}
			//头工具栏事件
			table.on('toolbar(userVideo)', function (obj)
			{
				var checkStatus = table.checkStatus(obj.config.id);
				switch (obj.event)
				{
					case 'getCheckData':
						var data = checkStatus.data;
						Delete(data);
						break;
					case 'isAdd':
						var isLdap = true;
						AjaxApi.request({
							url: "/User/LoginType.do",
							data: {},
							dataType: 'text',
							success: function (result)
							{
								if (result == "OK")
								{
									$('#div_pwd').hide();
									$('#div_confirmPwd').hide();
								}
								else
								{
									isLdap = false;
									$('#div_pwd').show();
									$('#div_confirmPwd').show();
								}
							},
							error: function (r)
							{
								layer.alert(lang(r.textContent), { btn: [lang('确定')], title: lang('提示') });
							}
						});

						index = layer.open({
							title: lang('增加用户')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addEdit')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function ()
							{
								setRoleSelect();
								$($('p')[0]).hide();
							}
							, btn1: function (index)
							{
								var data = form.val("form-addEdit");
								if (data.username.trim().length == 0)
								{
									layer.alert(lang('用户名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.Role.length == 0)
								{
									layer.alert(lang('用户角色不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.username.trim().getByteLength() > limitByteLengthPwd)
								{
									layer.alert(lang('请输入有效长度！限制最大长度为：') + limitByteLengthPwd, { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (!isLdap)
								{
									if (data.pwd.length == 0)
									{
										layer.alert(lang('密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}
									if (data.confirmPwd.length == 0)
									{

										layer.alert(lang('确认密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}
									if (data.confirmPwd.trim().getByteLength() > limitByteLengthPwd || data.pwd.trim().getByteLength() > limitByteLengthPwd
										|| data.confirmPwd.trim().getByteLength() < minLimitByteLength || data.pwd.trim().getByteLength() < minLimitByteLength)
									{
										layer.alert(lang('请输入有效的密码长度！限制最小长度为：') + minLimitByteLength + lang('，最大长度为：') + limitByteLengthPwd, { btn: [lang('确定')], title: lang('提示') });
										return false;
									}
									if (data.pwd != data.confirmPwd)
									{
										layer.alert(lang('确认密码和密码不一致！！！'), { btn: [lang('确定')], title: lang('提示') });
										$('#pwd').addClass("borderStyle");
										$('#confirmPwd').addClass("borderStyle");
										return false;
									}
								}
								$('#pwd').removeClass("borderStyle");
								$('#confirmPwd').removeClass("borderStyle");
								AjaxApi.request({
									url: "/User/AddUser.do",
									data: data,
									dataType: 'text',
									success: function (result)
									{
										var res = $.eval(result);
										if (res.code == "Success")
										{
											layer.alert(lang('保存成功,是否继续?'), {
												title: lang('增加用户')
												, btn: [lang('继续'), lang('取消')]
												, btnAlign: 'c'
												, btn1: function (subIndex)
												{
													$('#addEdit')[0].reset();
													layer.close(subIndex);
													FillDataTable();
												}
												, btn2: function (subIndex)
												{
													$('#addEdit')[0].reset();
													layer.close(subIndex);
													layer.close(index);
													FillDataTable();
												}
											});
										}
										else if (res.msg == "Exist")
										{
											layer.alert(lang('用户已存在！'), { btn: [lang('确定')], title: lang("增加用户"), });
										}
										else if (res.msg == "LDAP Not Found")
										{
											layer.alert(lang('LDAP不存在此用户！'), { btn: [lang('确定')], title: lang("增加用户"), });
										}
										else
										{
											layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加用户"), });
										}
									},
									error: function (r)
									{
										layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加用户"), });
									}
								});
							}
							, end: function ()
							{
								$('#addEdit')[0].reset();
								$('#addEdit').css('display', 'none');
							}
						});
						break;
					//自定义头工具栏右侧图标 - 提示
					case 'LAYTABLE_TIPS':
						layer.alert(lang('这是工具栏右侧自定义的一个图标按钮'), { btn: [lang('确定')], title: lang('提示') });
						break;
					case 'ExportData':
						{
							exportExcelData();
							break;
						}
				};
			});

			/**
			 * 导出 Excel 数据
			 */
			function exportExcelData()
			{
				AjaxApi.request({
					url: "/User/GetUserListAllExport.do",
					data: gData,
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						for (var i = 0; i < res.data.length; i++)
						{
							if (!!res.data[i].Data9 && !!res.data[i].Data9.length)
								res.data[i].User = res.data[i].Data9.substring(9, res.data[i].Data9.length);
						}
						//加载table数据
						table.reload('userListEx', {
							data: res.data,
							limit: res.data.length,
							done: function (res, curr, count)
							{
								exportDIYStyleExcel('userListEx', excel, lang('人员管理.xlsx'));
							}
						});
					}
				})
			}

			//监听行工具事件
			table.on('tool(userVideo)', function (obj)
			{
				var data = obj.data;
				if (obj.event === 'del')
				{
					if (data.Name == window.login.Name)
					{
						layer.alert(lang('无法删除自己！'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					Delete(data);
				} else if (obj.event === 'edit')
				{
					$('#div_pwd').hide();
					$('#div_confirmPwd').hide();

					$($('p')[0]).show();
					var oldrole = data.roleInfo ? data.roleInfo.ID : '';
					var index1 = layer.open({
						title: lang('修改用户')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#addEdit')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function ()
						{
							setRoleSelect();
							form.val('form-addEdit', {
								"username": data.Name
								, "Deptment": data.Dept
								, "ID": data.ID
								, "Role": data.roleInfo ? data.roleInfo.ID : ''
							});
							$('#addEdit input[name=username]').attr('disabled', true);
						}
						, btn1: function (value, index)
						{
							var data1 = form.val("form-addEdit");
							data1["oldName"] = data.Name;
							if (data1.Role.length == 0)
							{
								layer.alert(lang('用户角色不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}
							delete data1.pwd;
							delete data1.Password;

							if (data1.Role != oldrole)
							{
								unbindRole(data);
								bindRole(data1.Role, data1.ID);
							}
							var editData = {};
							for (var key in data1)
							{
								if (key !== 'Role')
								{
									editData[key] = data1[key];
								}
							}
							AjaxApi.request({
								url: "/User/EditUser.do",
								data: editData,
								dataType: 'text',
								success: function (result)
								{
									// var res = $.eval(result);
									if (result == "OK" || result == 'Success')
									{
										layer.msg(lang('修改成功'));
										layer.close(index1);
										FillDataTable();
									}
									else if (result == "Exist" || result == 'Conflict')
									{
										layer.alert(lang('用户已存在！'), { btn: [lang('确定')], title: lang('提示') });
									}
									else
									{
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
							})
						}
						, end: function ()
						{
							$('#addEdit input[name=username]').attr('disabled', false);
							$('#addEdit')[0].reset();
							$('#addEdit').css('display', 'none');
						}
					});
				} else if (obj.event === 'editPwd')
				{
					/*if (data.Role == '') {
						layer.alert(lang('此用户禁止修改！'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}*/
					var oldrole = data.Role;
					var index1 = layer.open({
						title: lang('修改密码')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#editPwd')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function ()
						{
							form.val('form-editPwd', {
								"username": data.Name
								, "ID": data.ID
								, "pwd1": ''
								, "confirmPwd1": ''
							});
						}
						, btn1: function (value, index)
						{
							var data1 = form.val("form-editPwd");
							if (data1.pwd1.length == 0)
							{
								layer.alert(lang('密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}
							if (data1.confirmPwd1.length == 0)
							{
								layer.alert(lang('确认密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
								return false;
							}
							if (data1.confirmPwd1.trim().getByteLength() > limitByteLengthPwd || data1.pwd1.trim().getByteLength() > limitByteLengthPwd
								|| data1.confirmPwd1.trim().getByteLength() < minLimitByteLength || data1.pwd1.trim().getByteLength() < minLimitByteLength)
							{
								layer.alert(lang('请输入有效的密码长度！限制最小长度为：') + minLimitByteLength + lang('，最大长度为：') + limitByteLengthPwd, { btn: [lang('确定')], title: lang('提示') });
								return false;
							}
							if (data1.pwd1 != data1.confirmPwd1)
							{
								layer.alert(lang('确认密码和密码不一致！！！'), { btn: [lang('确定')], title: lang('提示') });
								$('#pwd1').addClass("borderStyle");
								$('#confirmPwd1').addClass("borderStyle");
								return false;
							}
							data1['username'] = data.Name;
							data1['ID'] = data.ID;
							AjaxApi.request({
								url: "/User/EditUsePwd.do",
								data: data1,
								dataType: 'text',
								success: function (result)
								{
									var res = $.eval(result);
									if (res.code == "Success")
									{
										layer.alert(lang('修改成功'), { btn: [lang('确定')], title: lang('提示') });
										layer.close(index1);
										FillDataTable();
									} else
									{
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
							})
						}
					});
				}
				else if (obj.event == 'singleclick')
				{
					var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
					if (checkCell.length > 0)
					{
						checkCell.click();
					}
				}
			});

			form.on('submit(userSearch)', function (data)
			{
				FillDataTable(1);
				return false;
			});
			form.on('select(selRoleVideo)', function (data)
			{
				FillDataTable(1);
			})
			$('button[type=button]').on("click", function ()
			{
				document.getElementById('form-select').reset();
				FillDataTable(1);
			})
			renderTable([]);
		});

		// 编辑时先解绑角色
		function unbindRole(info)
		{
			if (!info.AuthID) return;
			AjaxApi.request({
				url: "../User/JoinRole.do",
				data: {
					id: info.AuthID
				},
				dataType: 'text',
				success: function (result) { }
			});
		}
		// 编辑时再绑定角色
		function bindRole(roleId, user)
		{
			AjaxApi.request({
				url: "../User/JoinRole.do",
				data: {
					Role: roleId,
					User: user
				},
				dataType: 'text',
				success: function (result) { }
			});
		}

		function renderTable(data)
		{
			table.render({
				elem: '#userList'
				, even: true
				, sort: false
				, loading: false
				, toolbar: hasButtonPermission('UserOptionSet') ? '#usertoolbar' : []
				, defaultToolbar: []
				, id: "userList"
				, cols: [col]
				, height: 'full-185'
				, page: false
				, limit: limitPageCount
				, data: data
			})
		}
		function columnsName()
		{
			var columnsArray = [];
			var columnsNames = [
				{
					title: lang("序号"), field: "xuhao", width: 80, templet: function (e)
					{
						return limitPageCount * (currentPage - 1) + e.LAY_INDEX
					}
				}
				, { title: lang("ID"), field: "ID", hide: true }
				, { title: lang("用户名称"), field: "Name" }
				, { title: lang("所属部门"), field: "Dept" }
				, {
					title: lang("所属角色"), width: 150, templet: function (d)
					{
						if (d.roleInfo)
						{
							var name = d.roleInfo.Name.split('/');
							return name[1];
						}
						return '';
					}
				}
				, { title: lang("备注"), field: "Title", hide: true }
				, { title: lang("更新时间"), field: "Edit", templet: templet }
			];
			for (var i = 0; i < columnsNames.length; i++)
			{
				var o = {
					title: columnsNames[i].title,
					field: columnsNames[i].field,
					align: 'center',
					event: 'singleclick',
					templet: columnsNames[i].templet,
					hide: columnsNames[i].hide
				};
				if (columnsNames[i].width) o.width = columnsNames[i].width;
				columnsArray.push(o);
			}
			var columnsLast = {
				fixed: 'right',
				minWidth: 280,
				title: lang("操作"),
				toolbar: "#barDemo",
				event: 'singleclick',
				align: 'center',
				hide: !hasButtonPermission('UserOptionSet')
			}
			columnsArray.push(columnsLast);
			return columnsArray;
		}
		function Delete(data)
		{
			var ids = ',';
			var names = ',';
			if (data.length == undefined)
			{
				ids += data.ID;
			} else
			{
				for (var i = 0; i < data.length; i++)
				{
					ids += data[i].ID + ",";
				}
			}
			if (data.length == undefined)
			{
				names += data.Name;
			} else
			{
				for (var i = 0; i < data.length; i++)
				{
					names += data[i].Name + ",";
				}
			}
			if (ids == ',')
			{
				layer.alert(lang('请选择需要删除的信息！'), { btn: [lang('确定')], title: lang('批量删除') })
				return;
			}
			layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index)
			{
				AjaxApi.request({
					url: "/User/DeleteUser.do",
					data: { ID: ids, names: names },
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.code == "Success")
						{
							layer.msg(lang('删除成功'), { icon: 1 });
							FillDataTable();
						}
						else
						{
							layer.msg(lang('删除失败'), { icon: 2 });
						}
					},
					error: function (r)
					{
						layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
					}
				})
				layer.close(index);
			});
		}
		//选择导入文件
		function initupload()
		{
			upload.render({
				elem: '#LAY-excel-upload' //绑定元素
				, auto: false //选择文件后不自动上传
				, accept: 'file'
				, choose: function (obj)
				{// 选择文件回调
					var files = obj.pushFile()
					// var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
					var fileArr = Object.keys(files).map(function (key)
					{
						return files[key];
					});
					// 用完就清理掉，避免多次选中相同文件时出现问题
					for (var index in files)
					{
						if (files.hasOwnProperty(index))
						{
							delete files[index]
						}
					}
					$('#LAY-excel-upload').next().val('');
					init(fileArr);
				}
			});
		}
		//导入数据
		function init(files)
		{
			excel.importExcel(files, {}, function (data)
			{
				data = excel.filterImportData(data, {
					'Name': 'A'
					, 'Dept': 'B'
					, 'Role': 'C'
				});

				if (data[0].Sheet1.length > 0)
				{
					data[0].Sheet1.splice(0, 1);
					if (data[0].Sheet1.length > 200)
					{
						layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					for (var i = 0; i < data[0].Sheet1.length; i++)
					{
						if ($.trim(data[0].Sheet1[i].Name + '') == '')
						{
							layer.alert(lang('用户名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}
						if ($.trim(data[0].Sheet1[i].Role + '') == '')
						{
							layer.alert(lang('用户角色不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}
						if (IsCheckSpecialCharUser(data[0].Sheet1[i].Name) > 0
							|| IsCheckSpecialCharUser(data[0].Sheet1[i].Dept > 0)
							|| IsCheckSpecialCharUser(data[0].Sheet1[i].Role > 0))
						{
							layer.alert(lang('存在特殊字符，禁止导入'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}
					}
				}
				AjaxApi.request({
					type: "post",
					url: "../User/ImportData.do",
					data: JSON.stringify(data[0].Sheet1),
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						var names = '';
						if (!!res.msg)
						{
							var msgArr = res.msg.split('|');
							if (msgArr.length > 1)
							{
								res.msg = msgArr[0];
								names = msgArr[1];
							}
						}
						if (res.code == "Success")
						{
							layer.alert(lang('导入成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else if (res.msg == "Exist")
						{
							layer.alert(names + lang(' 用户已存在！'), { btn: [lang('确定')], title: lang("增加用户"), });
						}
						else if (res.msg == "LDAP Not Found")
						{
							layer.alert(lang(' LDAP不存在此用户：') + names, { btn: [lang('确定')], title: lang("增加用户"), });
						}
						else
						{
							layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加用户"), });
						}
						FillDataTable();
					}
				})
			});
		}
		var username = '';
		var gData = '';
		function initRoleSelect()
		{
			if (configData_role == null)
				return;
			$("#selRole").empty();
			$("#selRole").append('<option value="">' + lang('请选择') + '</option>');
			for (var con = 0; con < configData_role.length; con++)
			{
				var key = configData_role[con].Name;
				var value = key.split('/');
				if (value.length > 1)
				{
					$("#selRole").append("<option value='" + configData_role[con].ID + "'>" + value[1] + "</option>");
				}
			}
			form.render('select');
		}

		function FillDataTable(pages)
		{
			var data = form.val('form-select');
			currentPage = pages || currentPage;
			gData = { name: data.username, role: data.selRole, Take: limitPageCount, Skip: (currentPage - 1) * limitPageCount };
			if (data.selChief)
				gData[window.CS.AuthData.TYPE_CHIEF] = 1;
			AjaxApi.request({
				url: "/User/List.do",
				data: gData,
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					username = top.login.Name;
					var annex = res.annex;
					for (var i = 0; i < res.data.length; i++)
					{
						var u = res.data[i];
						var roleInfo = configData_role.filter(function (val)
						{
							return annex.some(function (item)
							{

								if (item.Type == window.CS.AuthData.TYPE_ROLE
									&& val.ID == item.Role && u.ID == item.User)
								{
									u.AuthID = item.ID; // 角色中绑定annex ID
									return true;
								}
							});
						});
						if (roleInfo.length > 0) u.roleInfo = roleInfo[0];

						if (!u.RoleName)
							u.RoleName = '';
						for (var j = 0; j < annex.length; j++)
						{
							if (u.ID == annex[j].User && annex[j].Type == window.CS.AuthData.TYPE_ROLE)
							{
								var qs = $.parseUrl(annex[j].Data + '&' + annex[j].Data1 + '&' + annex[j].Data2);
								var t = qs['RoleName'];
								if (!!t)
								{
									var c = t.indexOf('/');
									if (c > 0)
										if (u.RoleName)
											u.RoleName = u.RoleName + ',' + t.substring(c + 1, t.length);
										else
											u.RoleName = t.substring(c + 1, t.length);
								}
							}
						}
					}

					table.reload('userList', {
						data: res.data
					})
					laypage.render({
						elem: 'footer'
						, count: res.count
						, theme: '#FF5722'
						, limit: limitPageCount
						, curr: currentPage
						, prev: lang('上一页')
						, next: lang('下一页')
						, layout: ['count', 'prev', 'page', 'next', 'skip']
						, jump: function (obj, first)
						{
							currentPage = obj.curr;
							if (!first)
							{
								FillDataTable(currentPage);
							}
						}
					});
				}
			})
		}

		function setRoleSelect()
		{
			if (configData_role == null)
				return;

			$("#userRole").empty();
			$("#userRole").append('<option value="">' + lang('请选择') + '</option>');
			for (var con = 0; con < configData_role.length; con++)
			{
				var value = configData_role[con].Name.split('/');
				if (value.length > 1)
				{
					$("#userRole").append("<option value='" + configData_role[con].ID + "'>" + value[1] + "</option>");
				}
			}
			form.render('select');
		}
		function show()
		{
			if ($('#pwd').val().length == 0)
				$($('p')[0]).css("visibility", "visible");
			else
				$($('p')[0]).css("visibility", "hidden");
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>用户设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">用户名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="username" autocomplete="off" placeholder="请输入用户名称"
							   oninput="cleanSpelChar(this)">
					</div>
					<!--<div class="layui-inline">
						<label class="layui-form-label">所属部门:</label>
					</div>
					<div class="layui-inline" style="width:130px;">
						<input type="text" class="layui-input" name="Deptment" autocomplete="off" placeholder="请输入相关的部门"
							   oninput="cleanSpelChar(this)">
					</div>-->
					<div class="layui-inline">
						<label class="layui-form-label">用户角色:</label>
					</div>
					<div class="layui-inline  edit">
						<select name="selRole" id="selRole" lay-filter="selRoleVideo">
							<option value="">请选择</option>
						</select>
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="userSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="userList" lay-filter="userVideo"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="userListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="usertoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="divBC" class="layui-inline">
					<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加')}}</button>
				</div>
				<!--<div id="divMB" class="layui-inline">
					<a class="layui-btn layui-btn-sm m_UserMB" href="doc/UserTemplate.xls">{{lang('下载模板')}}</a>
				</div>
				<div id="divDR" class="layui-inline">
					<button type="button" class="layui-btn layui-btn-sm m_UserDR" id="LAY-excel-upload">{{lang('导入')}}</button>
				</div>
				<div id="divDC" class="layui-inline">
					<button type="button" class="layui-btn layui-btn-sm m_ExportFile" lay-event="ExportData">{{lang('导出')}}</button>
				</div>-->
			</div>
		</script>
		<script type="text/html" id="barDemo">
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			<!--<a class="layui-btn layui-btn-normal layui-btn-xs" name="eventRole" lay-event="editRole">{{lang('绑定角色')}}</a>-->
			<a class="layui-btn layui-btn-primary layui-btn-xs m_UserPwd" name="eventPwd" lay-event="editPwd">{{lang('修改密码')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>
	<form class="layui-form" id="addEdit" lay-filter="form-addEdit" style="display:none;padding-top: 20px;padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<lable class="layui-form-label" style="width:119px;">用户名称:</lable>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="username" autocomplete="off" placeholder="请输入用户名称"
					   oninput="cleanSpelCharUser(this)">
			</div>
			<div class="layui-inline" style="display:none;">
				<input type="text" class="layui-input" name="ID" autocomplete="off">
			</div>
			<div class="layui-inline" style="display:none;">
				<input type="text" class="layui-input" name="Password" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">用户角色:</label>
			</div>
			<div class="layui-inline edit">
				<select name="Role" id="userRole" lay-filter="role-select">
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">所属部门:</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="Deptment" autocomplete="off" placeholder="请输入相关的部门"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>
		<div class="layui-form-item" id="div_pwd">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">密码:</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="pwd" name="pwd" autocomplete="off" placeholder="请输入密码">
			</div>
		</div>
		<div class="layui-form-item" id="div_confirmPwd">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">确认密码:</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="confirmPwd" name="confirmPwd" autocomplete="off" placeholder="请输入确认密码">
			</div>
		</div>
	</form>
	<form class="layui-form" id="editPwd" lay-filter="form-editPwd" style="display:none;padding-top: 20px;padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">密码:</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="pwd1" name="pwd1" autocomplete="off" placeholder="请输入密码">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">确认密码</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="confirmPwd1" name="confirmPwd1" autocomplete="off" placeholder="请输入确认密码">
			</div>
		</div>
	</form>
</body>
</html>