﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title>AlarmList</title>
	<link href="lib/layui/css/layui.css" rel="stylesheet" />
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="/Config/List.do?Type=STOREHOUSE&js=storehouse"></script>
	<script src="lib/layui/excel.js"></script>
	<script src="js/xm-select.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script src="js/request.js"></script>
	<style>
		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}
	</style>
</head>
<body style="overflow-x: auto;">
	<form class="layui-form" lay-filter="logform" id="logform" style="margin: 10px;">
		<fieldset class="layui-elem-field">
			<legend><span>机台日志</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline" id="toollbl" style="margin-right: 5px;">
						<lable class="layui-form-label" style="padding: 0px; width: 85px; ">日志类型：</lable>
					</div>
					<div class="layui-inline" id="toolselect" style="width:110px;">
						<select name="tooltype" id="tooltype" lay-search="">
							<!--<option value="u">请选择</option>-->
							<option value="VIDEO_SWEEPER">操作日志</option>
							<option value="VIDEO_SNAPSHOT">截屏日志</option>
							<option value="VIDEO_RECORD">录屏日志</option>
						</select>
					</div>
					<div id="toolnlbl" class="layui-inline" style="margin-right: 5px;">
						<lable class="layui-form-label" style="padding: 0px; width: 85px; ">机台名称：</lable>
					</div>
					<div id="toolinput" class="layui-inline" style="width: 150px;">
						<input type="text" class="layui-input" name="toolname" autocomplete="off" placeholder="请输入机台名称">
					</div>
					<div id="timelbl" class="layui-inline" style="margin-right: 5px;">
						<lable class="layui-form-label" style="padding: 0px; width: 85px;">日期范围：</lable>
					</div>
					<div id="timeinput" class="layui-inline" style="width: 300px;">
						<input type="text" class="layui-input" id="datetime" name="datetime" autocomplete="off" placeholder="请选择">
					</div>
					<div class="layui-inline" style="margin-right: 5px;">
						<button type="submit" class="layui-btn" lay-filter="logSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline" style="margin-right: 5px;">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-table" id="loglist" lay-filter="loglist"></table>
		<script type="text/html" id="recordingOp">
			<a class="layui-btn layui-btn-xs" name="eventEdit" lay-event="view">{{lang('观看')}}</a>
		</script>
		<div id="footer" style="text-align:center; margin-top: 10px;"></div>
	</form>
</body>
</html>

<script type="text/javascript">
	var limitpagecount = 20, currentPage = 1;
	layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate', 'excel', 'upload'], function () {
		$ = layui.$
			, laydate = layui.laydate
			, form = layui.form
			, table = layui.table
			, layer = layui.layer
			, element = layui.element
			, excel = layui.excel
			, laypage = layui.laypage
			, upload = layui.upload;

		var endDate = new Date();
		var startDate = new Date();
		startDate.setDate(startDate.getDate() - 30);

		function formatDate(date) {
			var year = date.getFullYear();
			var month = (date.getMonth() + 1).toString().padStart(2, '0');
			var day = date.getDate().toString().padStart(2, '0');
			return year + '-' + month + '-' + day;
		}

		var defaultDateRange = formatDate(startDate) + ' 00:00:00' + ' 至 ' + formatDate(endDate) + ' 23:59:59';

		laydate.render({
			elem: '#datetime',
			type: 'datetime',
			range: "至",
			max: '{:date("YY-mm-dd 23:59:59")}',
			value: defaultDateRange,
			done: function (value, date, endDate) {
				// $('#datetime').data('date', { startdate: date, enddate: endDate });
			}
		});

		form.val('logform', {
			"tooltype": "VIDEO_SWEEPER",
			"datetime": defaultDateRange
		});

		table.on('tool(loglist)', function (obj)
		{
			var tool = obj.data;
			var toolName = '';
			if (obj.event === 'view')
			{
				var data = { start_date: tool.Time - 300000, end_date: tool.Edit + 300000, id: tool.Video }
				AjaxApi.request({
					url: "Log/GetToolRecording.do",
					data: data,
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.data.length == 0)
						{
							layer.msg('没有找到录屏记录');
							return;
						}
						if (tool['Data1'])
						{
							var info = $.parseUrl(tool['Data1'], tool);
							toolName = info.VideoName;
						} else
						{
							toolName = tool['Video'];
						}
						var src = '/file/' + res.data[res.data.length - 1].Data1.replace('\\', '/');
						var index1 = layer.open({
							title: toolName + ' 机台录屏回放'
							, type: 1
							, resize: false
							, area: ['60%', '60%']
							, content: '<video src="' + src + '" style="height: 100%; width: 100%; background-color: #393D49;" controls="" autoplay=""></video>'
							, shadeClose: true
							, success: function ()
							{
							}
							, end: function ()
							{
								layer.close(index1);
							}
						});
					}
				})
			}

		})

		$('button[type=button]').on('click', function () {
			document.getElementById('logform').reset();
			form.val('logform', {
				"tooltype": "VIDEO_SWEEPER",
				"datetime": defaultDateRange
			});
			FillTableData();
		});

		form.on('submit(logSearch)', function (data) {
			FillTableData();
			return false;
		});

		FillTableData();

		function RenderTable(content, data)
		{
			var columnsArray = [{
				title: lang("序号"), field: "xuhao", width: 80, align: 'center', templet: function (e)
				{
					return limitpagecount * (currentPage) + e.LAY_INDEX
				}
			}];

			if (content == "VIDEO_RECORD")
			{
				columnsArray.push({ title: lang("机台名称"), align: 'center', field: "Name" });
				columnsArray.push({ title: lang("URL名称"), align: 'center', field: "Data2" });
				columnsArray.push({ title: lang("储存路径"), align: 'center', field: "Data1" });
				columnsArray.push({ title: lang("录屏模式"), align: 'center', field: "Recording" });
				columnsArray.push({ title: lang("录屏时间"), align: 'center', field: "StartTime" });
			}
			else if (content == "VIDEO_SNAPSHOT") {
				columnsArray.push({ title: lang("储存路径"), align: 'center', field: "Data" });
				columnsArray.push({ title: lang("截屏时间"), align: 'center', field: "StartTime" });
			}
			else if (content == "VIDEO_SWEEPER") {
				columnsArray.push({
					title: lang("机台名称"), width: 150, align: 'center', templet: function (d)
					{
						if (d['Data1'])
						{
							var info = $.parseUrl(d['Data1'], d);
							return info.VideoName;
						} else
						{
							return d['Video'];
						}
					}
				});
				columnsArray.push({ title: lang("操作用户"), align: 'center', width: 120, field: "Name" });
				columnsArray.push({
					title: lang("操作日志"), minWidth: 300, align: 'center', templet: function (d)
					{
						return d['Data'];
					}
				});
				columnsArray.push({ title: lang("开始时间"), align: 'center', width: 180, field: "StartTime" });
				columnsArray.push({ title: lang("结束时间"), align: 'center', width: 180, field: "EndTime" });
				columnsArray.push({
					title: lang("访问时长"), width: 100, align: 'center', templet: function (data)
					{
						var tick = parseInt(data.Start);
						if (!tick)
							tick = parseInt(data.Time);
						tick = parseInt(data.Edit) - tick;
						var h = String(Math.floor(tick / (1000 * 60 * 60))).padStart(2, '0');// 计算差值的小时、分钟和秒数
						var m = String(Math.floor((tick % (1000 * 60 * 60)) / (1000 * 60))).padStart(2, '0');
						var s = String(Math.floor((tick % (1000 * 60)) / 1000)).padStart(2, '0');
						if (h == '00' && m == '00' && s == '00')
							return '-';

						return `${h}:${m}:${s}`;
					}
				});
				columnsArray.push({ title: lang("操作"), align: 'center', toolbar: '#recordingOp', width: 130 });
				/*data.record.forEach(function (r)
				{
					var index = data.findIndex(function (v) { return v.Video = r.Video });
					if (index > -1)
					{
						data[index].record = r;
					}
				})*/
			}

			table.render({
				elem: '#loglist'
				, even: true
				, sort: true
				, loading: false
				, id: "loglist"
				, limit: limitpagecount
				, cols: [columnsArray]
				, data: data
				, height: 'full-200'
			});
		}

		var dataRecord = {
			"A": '始终录屏', "N": '不录屏', "W": '观看时录屏', "C": '操作时录屏',
		};
		var dataRole = {
			"USER": '用户', "ROLE": '角色', "AREA": '区域', "TOOLGROUP": '机台组', "AUTH": '权限', "VIDEO": '机台'
		}
		var dataOp = {
			"ADD": '添加', "EDIT": '编辑', "DELETE": '删除', "BATCHDELETE": '批量删除', "ADDSUB": '添加',
			"REGISTERTOOL": '注册', "UNREGISTERTOOL": '注销',
		};

		function formatLogDate(date)
		{
			return (
				date.getFullYear() + "/" +
				(date.getMonth() + 1).toString().padStart(2, '0') + "/" +
				date.getDate().toString().padStart(2, '0') + " " +
				date.getHours().toString().padStart(2, '0') + ":" +
				date.getMinutes().toString().padStart(2, '0') + ":" +
				date.getSeconds().toString().padStart(2, '0')
			);
		}

		function FillTableData(pageNum) {
			var data = form.val('logform');
			data.pageCount = pageNum || 0;
			data.limitCount = limitpagecount;
			data.logtype = "T";
			if ($('#toolselect').css('display') != 'none') {
				data.logtype = data.tooltype;
			}
			// console.log(111, data)

			var date = data.datetime;
			var start_date = "";
			var end_date = "";
			if (!date == false) {
				var times = date.split("至")
				start_date = new Date(times[0]).getTime();
				end_date = new Date(times[1]).getTime();
			}

			data.start_date = start_date;
			data.end_date = end_date;

			AjaxApi.request({
				url: "/Log/GetAllLog.do",
				data: data,
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);
					////加载table数据
					for (var i = 0; i < res.data.length; i++) {
						$.parseUrl(res.data[i].Data, res.data[i]);
						var tick = parseInt(res.data[i].Start);
						if (!tick)
							tick = parseInt(res.data[i].Time);
						var date = new Date(tick);
						res.data[i].StartTime = formatLogDate(date);

						var endDate = new Date(parseInt(res.data[i].Edit));
						res.data[i].EndTime = formatLogDate(endDate);

						if (!!res.record)
							res.data.record = res.record;

						res.data[i].Recording = dataRecord[res.data[i].Recording];

						var type = res.data[i].Type.split('_');
						if (type.length == 3) {
							for (var key in dataRole) {
								if (type[1].indexOf(key) >= 0) {
									res.data[i].Type = dataRole[key]
								}
							}
							for (var key in dataOp) {
								if (type[2].indexOf(key) >= 0) {
									res.data[i].Type += dataOp[key]
								}
							}
						}
					}
					laypage.render({
						elem: 'footer'
						, count: res.count
						, theme: '#FF5722'
						, curr: data.pageCount + 1
						, limit: limitpagecount
						, prev: lang('上一页')
						, next: lang('下一页')
						, layout: ['count', 'prev', 'page', 'next', 'skip']
						, jump: function (obj, first) {
							currentPage = obj.curr - 1;
							if (first) {
								//加载treetable数据
								RenderTable(data.tooltype, res.data);
							}
							else
							{
								FillTableData(currentPage);
							}
						}
					});
				}
			})
		}

	});
</script>
