﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>iRCMDevice页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<style>
		#addkvm .layui-inline {
			margin-right: 0px;
		}

		.edit {
			width: 200px;
		}

		.borderStyle {
			border: 1px solid red;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}

		.layui-form-switch {
			margin-top: -3px;
		}

		.txMark {
		}

		.txMarkHide {
			display: none;
		}
        #addkvm .layui-form-item,
        #addcctv .layui-form-item,
        #addtouch .layui-form-item {
            height: 43px;
        }

        #addkvm .layui-inline,
        #addcctv .layui-inline,
        #addtouch .layui-inline {
            height: 43px;
        }

        #addkvm select[lay-search],
        #addcctv select[lay-search],
        #addtouch select[lay-search] {
            height: 43px;
        }

		#img-container {
			position: relative;
			width: 100%;
			height: 100%;
			background-size: contain;
			background-repeat: no-repeat;
		}

		#resizable-div {
			position: relative;
			width: 100%; /* Start width */
			height: 100%; /* Start height */
			top: 0%; /* Start top position */
			left: 0%; /* Start left position */
			border: 1px solid red;
		}

        .handle {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: rgba(255, 0, 0, 0.5);
        }

		#top-left {
			top: 0px;
			left: 0px;
			cursor: nwse-resize;
		}

		#top-right {
			top: 0px;
			right: 0px;
			cursor: nesw-resize;
		}

		#bottom-left {
			bottom: 0px;
			left: 0px;
			cursor: nesw-resize;
		}

		#bottom-right {
			bottom: 0px;
			right: 0px;
			cursor: nwse-resize;
		}

		#top-center {
			top: 0px;
			left: 50%;
			cursor: ns-resize;
		}

		#bottom-center {
			bottom: 0px;
			left: 50%;
			cursor: ns-resize;
		}

		#middle-left {
			top: 50%;
			left: 0px;
			cursor: ew-resize;
		}

		#middle-right {
			top: 50%;
			right: 0px;
			cursor: ew-resize;
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/echarts.js?v=1"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript" src="js/excel.js"></script>
	<script src="js/request.js"></script>
	<script type="text/javascript">
		var checkedItems = [];
		var machineList = [];
        /*$(window).on('resize', function () {
			var dialogElem = $('#layui-layer' + layer.index);
            if (dialogElem.length > 0) {
                layer.style(layer.index, {
					width: $(window).width() * 0.9,
                    height: $(window).height() * 0.9,
                    top: ($(window).height() - dialogElem.height()) / 2 + 'px',
                    left: $(window).width() > 768 ?
                        ($(window).width() - dialogElem.width()) / 2 + 'px' :
                        '5%'
                });
            }
        });*/
        layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laydate', 'laypage', 'echarts', 'excel', 'dropdown', 'upload'], function () {
			$ = layui.$
				, form = layui.form
				, table = layui.table
				, layer = layui.layer
				, laypage = layui.laypage
                , element = layui.element
                , echarts = layui.echarts
				, upload = layui.upload
				, dropdown = layui.dropdown
                , laydate = layui.laydate
				, excel = layui.excel;

			lang.call(this, $);
			toolData = null;
			table.init('tooltable', []);
			limitPageCount = 20;
			currentFirstIndex = 1;
			col = toolColumnsName();
			//创建Table的列名
			tableIni = table.render({
				elem: '#toollist'
				, even: true
				, sort: true
				, loading: false
				, toolbar: hasButtonPermission('ToolMachineSet') ? '#tooltoolbar' : []
				, defaultToolbar: ['filter']
				, id: "toollist"
				, title: lang('Unit数据表')
				, limit: limitPageCount
				, cols: [col]
				, height: 'full-185'
				, data: []
			});
			pageCount = 1;
            getAllMachine();
			//头工具栏事件
			table.on('toolbar(tooltable)', function (obj) {
				var checkStatus = table.checkStatus(obj.config.id);
				switch (obj.event) {
					case 'getCheckData':
						var data = checkStatus.data;
						Delete(data);
						break;
					case 'isAdd':
						setMachine('', 'pc');
						index = layer.open({
							title: lang('增加 PC')
							, type: 1
							, resize: false
							, id: "isAdd"
							, content: $('#addkvm')
							, area: '70%'
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {}
							, btn1: function (index) {
								var data = form.val("form-addkvm");
								if (data.name.trim().length == 0) {
									layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.ip.length == 0) {
									layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

                                if (data.machine == '不选') data.machine = "";

                                if (data.machine.length == 0) {
                                    layer.alert(lang('机台不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                    return false;
								}

								data.type = CS.VideoData.TypeKVM;
								data['Data'] = 'installTime=' + data.installTime + '&qualityTime=' + data.qualityTime;
								data['description'] = '';

								addEquipment({
									data: data,
									name: "PC",
									id: "addkvm",
									content: index
								})
							}
                            , end: function () {
								$('#addkvm')[0].reset();
								$('#addkvm').css('display', 'none');
							}
						});
						break;
					case 'ExportData':
						{
							exportExcelData();
							break;
						}
					case 'isAddCctv':
                        setMachine('', 'cctv');
						index = layer.open({
							title: lang('增加 CCTV')
							, type: 1
							, resize: false
							, id: "isAdd"
                            , content: $('#addcctv')
                            , area: '70%'
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {
							}
							, btn1: function (index) {
								var data = form.val("form-addcctv");
								if (data.name.trim().length == 0) {
									layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.ip.length == 0) {
									layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

                                if (data.machine == '不选') data.machine = "";

                                if (data.machine.length == 0) {
                                    layer.alert(lang('机台不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                    return false;
                                }

                                data['Data'] = 'installTime=' + data.installTime + '&qualityTime=' + data.qualityTime;
                                data['description'] = '';

								data.type = CS.VideoData.TypeCCTV;

								addEquipment({
									data: data,
									name: "CCTV",
									id: "addcctv",
									content: index
								})
							}
							, end: function () {
								$('#addcctv')[0].reset();
								$('#addcctv').css('display', 'none');
							}
						});
						break;
					case 'isAddTouch':
                        setMachine('', 'touch');
						index = layer.open({
							title: lang('增加 Touch Panel')
							, type: 1
							, resize: false
							, id: "isAdd"
                            , content: $('#addtouch')
                            , area: '70%'
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {
							}
							, btn1: function (index) {
								var data = form.val("form-addtouch");
								if (data.name.trim().length == 0) {
									layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (data.ip.length == 0) {
									layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

                                if (data.machine == '不选') data.machine = "";

                                if (data.machine.length == 0) {
                                    layer.alert(lang('机台不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                    return false;
                                }

                                data['Data'] = 'installTime=' + data.installTime + '&qualityTime=' + data.qualityTime;
                                data['description'] = '';

								data.type = CS.VideoData.TypeTouch;
								data.scheme = 'VNCR';

								addEquipment({
									data: data,
									name: "Touch Panel",
									id: "addtouch",
									content: index
								})
							}
							, end: function () {
								$('#addtouch')[0].reset();
								$('#addtouch').css('display', 'none');
							}
						});
						break;
					case 'BatchDelete':
						{
                            BatchDelete();
							break;
						}
					case 'BatchUpdate':
						{
							BatchUpload();
							break;
						}
				};
			});
			// 添加设备pc cctv touch
			function addEquipment(obj) {
				AjaxApi.request({
					url: "/Equipment/AddEquipment.do",
					data: obj.data,
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);
						if (res.code == "Success") {
							FillDataTable();
							layer.alert(lang('保存成功,是否继续?'), {
								title: lang('增加 ' + obj.name)
								, btn: [lang('继续'), lang('取消')]
								, btnAlign: 'c'
								, btn1: function (subIndex) {
									$('#' + obj.id)[0].reset();
									layer.close(subIndex);
								}
								, btn2: function (subIndex) {
									$('#' + obj.id)[0].reset();
									layer.close(subIndex);
									layer.close(obj.content);
									//FillDataTable();
								}
							});
						}
						else {
							layer.alert(res.msg, { btn: [lang('确定')], title: lang("增加 " + obj.name), });
						}
					},
					error: function (r) {
						layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加 " + obj.name), });
					}
				})
			}
			// 批量删除
			function BatchDelete() {
				if (checkedItems.length == 0) {
                    layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
                    return;
				}

				layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
					AjaxApi.request({
						type: "post",
						url: "/Equipment/DatchDeleteEquipment.do",
						data: JSON.stringify(checkedItems),
						dataType: 'text',
						success: function (result) {
							var res = $.eval(result);
							if (res.code == "Success") {
								checkedItems = [];
								// col[col.length - 1].hide = true;
								table.reload('toollist', {
									cols: [col],
								})

								$('#normaldiv').show();
								$('#batchdiv').hide();

								layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
							}
							else {
								layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除Unit"), });
							}
							FillDataTable();
						}
					})
				});
			}

			/**
			 * 导出 Excel 数据
			 */
			function exportExcelData() {
				AjaxApi.request({
					url: "/tool/GetToolListAllExport.do",
					data: gData,
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);

						// col[col.length - 1].hide = true;
						//加载table数据
						table.reload('toollist', {
							data: refillData(res.data),
							limit: res.data.length,
							cols: [col],
							done: function (res, curr, count) {
								exportDIYStyleExcel('toollist', excel, lang('Unit设置.xlsx'));
							}
						});
					}
				})
			}

			//监听行工具事件
			table.on('tool(tooltable)', function (obj) {
				var that = this;
				var data = obj.data;

				if (obj.event === 'del') {
					Delete(data);
				}
				else if (obj.event === 'edit') {
					if (data.Role == '') {
						layer.alert(lang('此用户禁止修改！'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}

					var elem = 'add' + data.Type.toLowerCase();

					var index1 = layer.open({
						title: lang('修改 ') + data.Type
						, type: 1
						, resize: true
						, id: 'edit'
						, content: $('#' + elem)
                        , area: '70%'
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function () {
							var d = { name: data.Name, mark: data.Landmark, ip: data.Url.replace(/^.+:\/\/([^\/]+)(\/.*)?/g, '$1'), port: '', user: '', psd: '' };
							var i = data.Url.indexOf('?');
							if (!!data.Data)
							{
								var ds = $.parseUrl(data.Data, {});
								for (var k in ds)
									d['data-' + k] = ds[k];
							}
							if (i > 0)
							{
								var ds = $.parseUrl(data.Url.substring(i + 1, data.Url.length), {});
								for (var k in ds)
									d['query-' + k] = ds[k];
							}
							i = d.ip.indexOf('@');
							if (i > 0)
							{
								d.psd = d.ip.substring(0, i);
								d.ip = d.ip.substring(i + 1, d.ip.length);
								i = d.ip.indexOf(':');
								if (i > 0)
								{
									d.port = d.ip.substring(i + 1, d.ip.length);
									d.ip = d.ip.substring(0, i);
								}
								i = d.psd.indexOf(':');
								if (i >= 0)
								{
									d.user = d.psd.substring(0, i);
									d.psd = d.psd.substring(i + 1, d.psd.length);
									d.user = decodeURIComponent(d.user);
								}
								d.psd = decodeURIComponent(d.psd);
							}
							d.alias = data.Alias;
							d.recording = data.Recording;
							d.eqp = data.Eqp;
							switch (data.Type)
							{
								case CS.VideoData.TypeKVM:
									setMachine(data.Machine, 'pc');
									refreshAreaAndGroup(data.areaname, data.unitname, '_pc');
									form.val('form-addkvm', d);
									break;
								case CS.VideoData.TypeCCTV:
									setMachine(data.Machine, 'cctv');
									refreshAreaAndGroup(data.areaname, data.unitname, '_cctv');
									form.val('form-addcctv', d);
									break;
								case CS.VideoData.TypeTouch:
									setMachine(data.Machine, 'touch');
									refreshAreaAndGroup(data.areaname, data.unitname, '_touch');
									form.val('form-addtouch', d);
									break;
							}
						}
						, btn1: function (value, index) {
							var data1 = form.val("form-" + elem);

							data1["id"] = data.ID;
							data1['type'] = data.Type;

                            if (data1.ip.length == 0) {
                                layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                return false;
                            }

                            if (data1.machine == '不选') data1.machine = "";

                            if (data1.machine.length == 0) {
                                layer.alert(lang('机台不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                                return false;
                            }
                            data1['Data'] = 'installTime=' + data1.installTime + '&qualityTime=' + data1.qualityTime;
							data1['description'] = '';
							if (data.Type == CS.VideoData.TypeTouch) data1.scheme = 'VNCR';

							AjaxApi.request({
								url: "/Equipment/EditEquipment.do",
								data: data1,
								dataType: 'text',
								success: function (result) {
									var res = $.eval(result);
									if (res.code == "Success") {
										layer.msg(lang('修改成功'), { icon: 1 });
										layer.close(index1);
										FillDataTable();
									}
									else {
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
							})
						}
						, end: function () {
							$('#' + elem)[0].reset();
							$('#' + elem).css('display', 'none');
							$('#' + elem + ' input[name=name]').attr("disabled", false);
							$('#' + elem + ' input[name=name]').removeClass("layui-disabled");
						}
					});
				}
				else if (obj.event === 'change') {
					//	img.src = 'http://ip:8001/mt/0/' + Date.now() + '.jpg';//1,2

					var index1 = layer.open({
						title: lang('修改 Recipe')
						, type: 1
						, resize: false
						, id: 'edit'
						, content: $('#changeRecipe')
						, btn: [lang('确认'), lang('取消')]
						, btnAlign: 'c'
						, success: function () {

						}
						, btn1: function (value, index) {
							var recipe = form.val('form-changeRecipe');

							if (data.Type == 'CCTV')
								return;

							var ip = data.Url.toLowerCase().replace(/^.+:\/\/([^\/]+).*$/img, '$1');

							if (recipe.enable == undefined) {
								$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/0/' + Date.now() + '.jpg');
							}
							else {
								$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/' + recipe.recipe + '/' + Date.now() + '.jpg');
								//$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/rpa/' + recipe.recipe + '/' + Date.now() + '.jpg');
							}

							layer.msg('已切换Recipe');

							layer.close(index1);
						}
						, end: function () {
						}
					});
				}
				else if (obj.event == 'singleclick') {
					var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
					if (checkCell.length > 0) {
						checkCell.click();
					}
				} else if (obj.event === 'more') {
					//更多下拉菜单
					dropdown.render({
						elem: that
						, show: true //外部事件触发即显示
						, data: [
							{ title: '区域', id: 'area' },
							{ title: '资源监控', id: 'zyjk' },
							{ title: '固件管理', id: 'gjgl' }
						]
						, click: function (type, othis) {
							//根据 id 做出不同操作
							if (type.id === 'area') {
								chooseArea(data);
							}
							if (type.id === 'zyjk') {
								console.log('资源监控');
								onKVMMsg();
							}
							if (type.id === 'gjgl') {
								console.log('固件管理');
								uploadFirmware();
							}
						}
						, align: 'right'
						, style: 'box-shadow: 1px 1px 10px rgb(0 0 0 / 12%);'
					});
				}
			});

			table.on('checkbox(tooltable)', function (obj) {
				if (obj.checked) {
					if (obj.type == 'all') {
						checkedItems = table.cache.toollist;
					} else {
						checkedItems.push(obj.data);
					}
				} else {
					if (obj.type == 'all') {
						checkedItems = [];
					} else {
						var i = 0;
						$.each(checkedItems, function (key, value) {
							if (value.id == obj.data.id) {
								checkedItems.splice(i, 1);
								return false;
							}
							i++;
						})
					}
				}
			});

			form.on('submit(toolSearch)', function (data) {
				FillDataTable();
				return false;
			});
			form.on('select(selRoleVideo)', function (data) {
				FillDataTable();
			})
            $('button[type=button]').on('click', function () {
				document.getElementById('form-select').reset();
				FillDataTable();
			})
			function toolColumnsName() {
				var columnsArray = [];
				var columnsNames = [
					{
						title: lang("序号"), field: "xuhao", width: 80, templet: function (e) {
							return limitPageCount * (pageCount - 1) + e.LAY_INDEX
						}
					}
					, { title: lang("Device名称"), field: "Name" }
					, { title: lang("Device类型"), field: "Type" }
                    , { title: lang("所属机台"), field: "machinename" }
                    , { title: lang("Url"), field: "Url" }
					// , { title: lang("地标"), field: "Landmark" }
                    , {
						title: lang("安装日期"), field: "Data", templet: function (e) {
                            var DataObj = {};
                            $.parseUrl(e.Data, DataObj);
                            return DataObj.installTime ? DataObj.installTime : '';
						}
					}
                    , {
                        title: lang("质保期(年)"), field: "Data", templet: function (e) {
                            var DataObj = {};
							$.parseUrl(e.Data, DataObj);
                            return DataObj.qualityTime ? DataObj.qualityTime : '';
						}
					}
					, { title: lang("固件版本"), field: "Data9" }
					, { title: lang("最后心跳"), field: "Pong", templet: templet }
				];

				for (var i = 0; i < columnsNames.length; i++) {
					var o = {
						title: columnsNames[i].title,
						field: columnsNames[i].field,
						align: 'center',
						event: 'singleclick',
						templet: columnsNames[i].templet,
						hide: columnsNames[i].hide
					};
					if (columnsNames[i].width) o.width = columnsNames[i].width;
					columnsArray.push(o);
				}

				var columnsLast = {
					minWidth: 200,
					title: lang("操作"),
					toolbar: "#operation",
					event: 'singleclick',
					align: 'center',
					fixed: 'right',
					hide: !hasButtonPermission('ToolMachineSet')
				}
				columnsArray.push(columnsLast);

				var columnsLastAgain = {
					title: lang("选中"),
					type: 'checkbox',
					event: 'singleclick',
					align: 'center',
				}
				columnsArray.unshift(columnsLastAgain);

                return columnsArray;
			}

			function Delete(data) {
				layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
					AjaxApi.request({
						url: "/Equipment/DeleteEquipment.do",
						data: { id: data.ID, type: data.Type, name: data.Name },
						dataType: 'text',
						success: function (result) {
							var res = $.eval(result);
							if (res.code == "Success") {
								layer.msg(lang('删除成功'), { icon: 1 });
								FillDataTable();
							}
							else {
								layer.msg(lang('删除失败'), { icon: 2 });
							}
						},
						error: function (r) {
							layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
						}
					})
					layer.close(index);
				});
			}

			laydate.render({ elem: '#installTime_pc', type: 'datetime' });
			laydate.render({ elem: '#installTime_cctv', type: 'datetime' });
            laydate.render({ elem: '#installTime_touch', type: 'datetime' });

			element.on('tab(tab)', function (data) {
				if (data.index == 0) {
					onKVMVideoInfoShow();
				} else if (data.index == 1) {
					onKVMDeviceInfoShow();
				}
			});
		});

		// 批量上传
		function BatchUpload() {
            if (checkedItems.length == 0) {
                layer.alert(lang('未选择数据！'), { btn: [lang('确定')], title: lang('提示') });
                return;
			}
            
            uploadFirmware(checkedItems)
		}
		// 框选范围
		function chooseArea(data) {
			var AreaDialog = layer.open({
				title: lang('选择范围 ')
				, type: 1
				, resize: false
				, id: 'areaChoose'
                , area: ['70%', '70%']
				, content: $('#chooseArea')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
				, success: function () {
					var ip = data.Url.split("://");
					$("#img-container").css('background-image', "url(" + '/frame/' + ip[1] + ")");
					if (!!data.AreaDenial) {
						var pos = data.AreaDenial.split(',');
						$('#resizable-div').css('top', parseFloat(pos[1]) * 100 + '%')
						$('#resizable-div').css('left', parseFloat(pos[0]) * 100 + '%')
						$('#resizable-div').css('width', parseFloat(pos[2]) * 100 + '%')
						$('#resizable-div').css('height', parseFloat(pos[3]) * 100 + '%')
					}
					else {
						$('#resizable-div').css('top', '0%')
						$('#resizable-div').css('left', '0%')
						$('#resizable-div').css('width', '100%')
						$('#resizable-div').css('height', '100%')
					}
					var resDiv = document.getElementById('resizable-div');
					var container = document.getElementById('img-container');
					var containerWidth = container.offsetWidth;
					var containerHeight = container.offsetHeight;
					var startX, startY, startWidth, startHeight, startTop, startLeft;
					var draggable = false;
					var currentHandle = null;

					var handles = Array.prototype.slice.call(document.querySelectorAll('.handle'));
					handles.forEach(function (handle) {
						handle.addEventListener('mousedown', function (e) {
							draggable = true;
							currentHandle = e.target.id;
							startX = e.clientX;
							startY = e.clientY;
							startWidth = parseFloat(resDiv.offsetWidth);
							startHeight = parseFloat(resDiv.offsetHeight);
							startTop = parseFloat(window.getComputedStyle(resDiv, null).getPropertyValue('top'));
							startLeft = parseFloat(window.getComputedStyle(resDiv, null).getPropertyValue('left'));
							containerWidth = container.clientWidth;
							containerHeight = container.clientHeight;
							e.preventDefault();
						}, false);
					});

					document.addEventListener('mousemove', function (e) {
						if (!draggable) return;
						var dx = e.clientX - startX;
						var dy = e.clientY - startY;

						switch (currentHandle) {
							case 'top-left':
								resDiv.style.top = Math.max(0, Math.min(containerHeight - resDiv.offsetHeight, startTop + dy)) + 'px';
								resDiv.style.left = Math.max(0, Math.min(containerWidth - resDiv.offsetWidth, startLeft + dx)) + 'px';
								resDiv.style.width = Math.min(containerWidth - startLeft, Math.max(0, startWidth - dx)) - 2 + 'px';
								resDiv.style.height = Math.min(containerHeight - startTop, Math.max(0, startHeight - dy)) - 2 + 'px';
								break;
							case 'top-center':
								resDiv.style.height = Math.min(containerHeight - startTop, Math.max(0, startHeight - dy)) - 2 + 'px';
								resDiv.style.top = Math.max(0, Math.min(containerHeight - resDiv.offsetHeight, startTop + dy)) + 'px';
								break;
							case 'top-right':
								resDiv.style.width = Math.min(containerWidth - startLeft, Math.max(0, startWidth + dx)) - 2 + 'px';
								resDiv.style.height = Math.min(containerHeight - startTop, Math.max(0, startHeight - dy)) - 2 + 'px';
								resDiv.style.top = Math.max(0, Math.min(containerHeight - resDiv.offsetHeight, startTop + dy)) + 'px';
								break;
							case 'middle-left':
								resDiv.style.width = Math.min(containerWidth - startLeft, Math.max(0, startWidth - dx)) - 2 + 'px';
								resDiv.style.left = Math.max(0, Math.min(containerWidth - resDiv.offsetWidth, startLeft + dx)) + 'px';
								break;
							case 'middle-right':
								resDiv.style.width = Math.min(containerWidth - startLeft, Math.max(0, startWidth + dx)) - 2 + 'px';
								break;
							case 'bottom-left':
								resDiv.style.width = Math.min(containerWidth - startLeft, Math.max(0, startWidth - dx)) - 2 + 'px';
								resDiv.style.height = Math.min(containerHeight - startTop, Math.max(0, startHeight + dy)) - 2 + 'px';
								resDiv.style.left = Math.max(0, Math.min(containerWidth - resDiv.offsetWidth, startLeft + dx)) + 'px';
								break;
							case 'bottom-center':
								resDiv.style.height = Math.min(containerHeight - startTop, Math.max(0, startHeight + dy)) - 2 + 'px';
								break;
							case 'bottom-right':
								resDiv.style.width = Math.min(containerWidth - startLeft, Math.max(0, startWidth + dx)) - 2 + 'px';
								resDiv.style.height = Math.min(containerHeight - startTop, Math.max(0, startHeight + dy)) - 2 + 'px';
								break;
						}
						e.preventDefault();
					}, false);

					document.addEventListener('mouseup', function () {
						draggable = false;
						currentHandle = null;
					}, false);

				}
				, btn1: function () {
					var resDiv = document.getElementById('resizable-div');
					var containerWidth = document.getElementById('img-container').offsetWidth;
					var containerHeight = document.getElementById('img-container').offsetHeight;
					var x = (resDiv.offsetLeft / containerWidth).toFixed(2);
					var y = (resDiv.offsetTop / containerHeight).toFixed(2);
					var width = (resDiv.offsetWidth / containerWidth).toFixed(2);
					var height = (resDiv.offsetHeight / containerHeight).toFixed(2);
					// console.log(data, 'dimensions: ', x, y, width, height);
					var videoData = '';
					var query = data.Data.split('&');
					query.forEach(value => {
						if (value == '' || value.indexOf('AreaDenial') > -1)
							return;
						videoData += '&' + value;
					})
					videoData += `&AreaDenial=${x},${y},${width},${height}`;
					AjaxApi.request({
						url: '/Equipment/SetAreaDenial.do',
						data: { "id": data.ID, data: videoData },
						success: function () {
							layer.msg("设置成功");
							FillDataTable();
							layer.close(AreaDialog);
						}
					})
				}
			})
		}

		var col = null;

		//选择导入文件
		function initupload() {
			upload.render({
				elem: '#LAY-excel-upload' //绑定元素
				, auto: false //选择文件后不自动上传
				, accept: 'file'
				, choose: function (obj) {// 选择文件回调
					var files = obj.pushFile()
					// var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
					var fileArr = Object.keys(files).map(function (key) {
						return files[key];
					});
					// 用完就清理掉，避免多次选中相同文件时出现问题
					for (var index in files) {
						if (files.hasOwnProperty(index)) {
							delete files[index]
						}
					}
					$('#LAY-excel-upload').next().val('');
					init(fileArr);
				}
			});
		}
		//导入数据
		function init(files) {
			excel.importExcel(files, {}, function (data) {
				data = excel.filterImportData(data, {
					'Name': 'A'
					, 'Area': 'B'
					, 'Unit': 'C'
					, 'LandMark': 'D'
					, 'Url': 'E'
					, 'Data': 'F'
				});

				if (data[0].Sheet1.length > 0) {
					data[0].Sheet1.splice(0, 1);
					if (data[0].Sheet1.length > 200) {
						layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					for (var i = 0; i < data[0].Sheet1.length; i++) {
						if ($.trim(data[0].Sheet1[i].Name + '') == '') {
							layer.alert(lang('Unit名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}

						switch (data[0].Sheet1[i].Data) {
							case '始终录屏':
								data[0].Sheet1[i].Data = '&Recording=A';
								break;
							case '操作时录屏':
								data[0].Sheet1[i].Data = '&Recording=C';
								break;
							case '观看时录屏':
								data[0].Sheet1[i].Data = '&Recording=W';
								break;
							case '手动录屏':
								data[0].Sheet1[i].Data = '&Recording=N';
								break;
						}

					}
				}
				AjaxApi.request({
					type: "post",
					url: "../Tool/ImportData.do",
					data: JSON.stringify(data[0].Sheet1),
					dataType: 'text',
					success: function (result) {
						var res = $.eval(result);
						var names = '';
						if (!!res.msg) {
							var msgArr = res.msg.split('|');
							if (msgArr.length > 1) {
								res.msg = msgArr[0];
								names = msgArr[1];
							}
						}
						if (res.code == "Success") {
							layer.alert(lang('导入成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else if (res.msg == "Exist") {
							layer.alert(names + lang(' Unit已存在！'), { btn: [lang('确定')], title: lang("增加Unit"), });
						}
						else {
							layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加Unit"), });
						}
						FillDataTable();
					}
				})
			});
		}
		var username = '';
		var gData = '';
		function initRoleSelect() {
			if (configData == null)
				return;
			for (var con = 0; con < configData.length; con++) {
				var key = configData[con].Name;
				var value = key.split('/');
				if (value.length > 1) {
					$("#selRole").append("<option value='" + key + "'>" + value[1] + "</option>");
				}
			}
			form.render('select');
		}
		function FillDataTable() {
			if (window.top) {
				window.top.FillFavTreeTable();
			}
			var data = form.val('form-select');
			if (data.type == '-') delete data.type;
            if (data.machine == '-') delete data.machine;

			var gData = { pageCount: 0, limitCount: limitPageCount, namelike: data.name, machine: data.machine, type: data.type };
			AjaxApi.request({
				url: "/Equipment/GetEquipment.do",
				data: gData,
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);
					var res_data = refillData(res.data);
					var res_count = res.count;

                    laypage.render({
                        elem: 'footer'
                        , count: res_count
                        , theme: '#FF5722'
                        , limit: limitPageCount
                        , prev: lang('上一页')
                        , next: lang('下一页')
                        , layout: ['count', 'prev', 'page', 'next', 'skip']
                        , jump: function (obj, first) {
                            pageCount = obj.curr;
                            if (first) {
                                //加载table数据
                                table.reload('toollist', {
                                    data: res_data,
                                    page: false,
                                    cols: [col]
                                });
                            }
                            else {
                                setTable({ pageCount: pageCount - 1, limitCount: limitPageCount, namelike: data.name, machine: data.machine, type: data.type });
                            }
                            laypageEN();//翻页翻译成英文方法
                        }
                    });
				}
			})
		}

		function setTable(data) {
			AjaxApi.request({
				url: "/Equipment/GetEquipment.do",
				data: data,
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);
					var res_data = refillData(res.data);
                    table.reload('toollist', {
                        data: res_data,
                        page: false,
                        cols: [col]
                    });
				}
			})
		}
		function show() {
			if ($('#pwd').val().length == 0)
				$($('p')[0]).css("visibility", "visible");
			else
				$($('p')[0]).css("visibility", "hidden");
		}
		// 设置机台select
		function setMachine(id, nameId) {
            $("#machine_" + nameId).empty();
			if (nameId == 'search') {
				$("#machine_" + nameId).append("<option>-</option>");
            } else {
                $("#machine_" + nameId).append("<option>不选</option>");
			}

			
			for (var i = 0; i < machineList.length; i++) {

				var option = '';
                if (!!id && !!id.length && id == machineList[i].ID)
                    option = "<option selected value='" + machineList[i].ID + "'>" + machineList[i].Name + "</option>";
				else
                    option = "<option value='" + machineList[i].ID + "'>" + machineList[i].Name + "</option>";

                $("#machine_" + nameId).append(option);
			}

			form.render('select');
		}

		function refillData(data) {
			for (var i = 0; i < data.length; i++) {
				$.parseUrl(data[i].Data, data[i]);
				$.parseUrl(data[i].Data9, data[i]);

				data[i].xuhao = i + 1;
				var machine = data[i].Machine;
                var temp = machineList.filter(function (i, index, arr) { return i.ID == machine });
                if (temp.length > 0) data[i].machinename = temp[0].Name;
				switch (data[i].Recording) {
					case 'A':
						data[i].RecordingName = '始终录屏';
						break;
					case 'C':
						data[i].RecordingName = '操作时录屏';
						break;
					case 'W':
						data[i].RecordingName = '观看时录屏';
						break;
					case 'N':
						data[i].RecordingName = '手动录屏';
						break;
				}
			}

			return data;
		}
		// 获取所有机台
		function getAllMachine() {
			AjaxApi.request({
                url: "../Config/list.do",
                data: { Type: 'Machine' },
				dataType: 'text',
				success: function (result) {
					var res = $.eval(result);

					machineList = res;
                    setMachine('', 'search')
					FillDataTable();
				}
			})
		}
		// 查看资源监控
		function onKVMMsg(element) {
            // videoID = element.id;
            onKVMVideoInfoShow();
			layer.open({
				type: 1,
				title: '资源监控信息',
				id: 'messabelabel',
				area: '400px',
				content: $('#toolkvm'),
				// offset: [ 100, 100], 
				resize: false,
				shadeClose: true,
				success: function () {
				},
				end: function () {
					$('#toolkvm')[0].reset();
				}
			});
		}
		function onKVMVideoInfoShow() {
			var testData = { "pn": "AIRCON-T2HLC", "sn": "05150011142", "APP_v": "T-R2B.2.1.28", "buildtime": "2023-05-05 18:54:01\n", "BSP_v": "2.1.4\n", "cpu_T": "78.1℃", "cpurate": "19.5%", "memrate": "7.2%", "rootfsdiskrate": "86.0%", "APP_status": true, "userdatafsdiskrate": "5.0%", "appfsdiskrate": "35.0%", "video_input": true, "v_resolution": "1920x1080", "v_fps": "60", "v_interlaced": "逐行", "v_colorspace": "RGB", "audio_input": "HDMI IN", "audio_sample": "48000 16bit", "encod_status": true, "sub_encod_status": true, "encod_fps": "60", "sub_encod_fps": "15", "imag_encod_fps": "0", "interlock_state": "远端" };

			var kvmInfoData = testData;

			var titleData = ['视频信号输入：', '输入分辨率：', '刷新频率：',
				'扫描类型：', '颜色格式：', '音频源：',
				'音频采样率：', '主码流：',
				'子码流：', '主码流编码帧率：', '子码流编码帧率：', '抓图码流编码帧率：'];

			var contentData = [];
			contentData.push(kvmInfoData.video_input);
			contentData.push(kvmInfoData.v_resolution);
			contentData.push(kvmInfoData.v_fps);
			contentData.push(kvmInfoData.v_interlaced);
			contentData.push(kvmInfoData.v_colorspace);
			contentData.push(kvmInfoData.audio_input);
			contentData.push(kvmInfoData.audio_sample);
			contentData.push(kvmInfoData.encod_status);
			contentData.push(kvmInfoData.sub_encod_status);
			contentData.push(kvmInfoData.encod_fps);
			contentData.push(kvmInfoData.sub_encod_fps);
			contentData.push(kvmInfoData.imag_encod_fps);

			var n = $('#kvmVideoInfo'), dom = '';

			n.children().remove();

			for (var i = 0; i < contentData.length; i++) {
				var div = '<div style="display:flex; margin-bottom:10px;">';
				var label = '<label class="kvmTitleLabel">' + titleData[i] + '</label>';
				if (!!contentData[i] == false)
					contentData[i] = '';
				var span = '<div><span class="kvmContentSpan">' + contentData[i] + '</span></div>';
				dom += div + label + span + '</div>';
			}

			n.eq(0).append(dom);
		}

		function onKVMDeviceInfoShow() {
			var testData = {
				"pn": "AIRCON-T2HLC", "sn": "05150011142", "APP_v": "T-R2B.2.1.28", "buildtime": "2023-05-05 18:54:01\n", "BSP_v": "2.1.4\n",
				"cpu_T": "78.1℃", "cpurate": "19.5%", "memrate": "7.2%", "rootfsdiskrate": "86.0%", "APP_status": true, "userdatafsdiskrate": "5.0%", "appfsdiskrate": "35.0%", "video_input": true, "v_resolution": "1920x1080", "v_fps": "60", "v_interlaced": "逐行", "v_colorspace": "RGB", "audio_input": "HDMI IN", "audio_sample": "48000 16bit", "encod_status": true, "sub_encod_status": true, "encod_fps": "60", "sub_encod_fps": "15", "imag_encod_fps": "0", "interlock_state": "远端"
			};

			var kvmInfoData = testData;

			var titleData = ['设备型号：', '设备序列号：', '系统版本：', '更新时间：', 'BSP版本：',];

			var contentData = [];
			contentData.push(kvmInfoData.pn);
			contentData.push(kvmInfoData.v_resolution);
			contentData.push(kvmInfoData.sn);
			contentData.push(kvmInfoData.APP_v);
			contentData.push(kvmInfoData.BSP_v);

			var n = $('#kvmDeviceInfo'), dom = '';

			n.children().remove();

			for (var i = 0; i < contentData.length; i++) {
				var div = '<div style="display:flex; margin-bottom:10px;">';
				var label = '<label class="kvmTitleLabel">' + titleData[i] + '</label>';
				if (!!contentData[i] == false)
					contentData[i] = '';
				var span = '<div><span class="kvmContentSpan">' + contentData[i] + '</span></div>';
				dom += div + label + span + '</div>';
			}

			n.eq(0).append(dom);
		}
		
		function uploadFirmware() {
            var index = layer.open({
                type: 1,
                content: $('#firmware'),
				title: '固件管理',
                area: '400px',
                shadeClose: true,
                btn: ['关闭'],
                resize: false,
                success: function () {
                    upload.render({
                        elem: '#firmwareUpload'
                        , auto: false
                        , accept: 'file'
                        // , exts: ''
                        , choose: function (obj) {
                            $('#firmwareUpload').next().val('');
                            obj.preview(function (index, file, result) {
                                $('#uploadDemoView').removeClass('layui-hide').find('span').html(file.name);
								console.log(file, result)
                                /*AjaxApi.request({
                                    type: "post",
                                    url: "Role/SetRoleAlarmAudio.do",
                                    data: result,
                                    dataType: 'text',
                                    success: function () {
                                        layer.msg(lang('上传成功'));
                                        layer.close(index1);
                                    }
                                })*/
                            });
                        }
                    });
                },
                btn1: function () {
                    layer.close(index);
                }
            });
		}

	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>Device 设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<img id="recipe_img" style="width:1px; height:1px;" />
						<lable class="layui-form-label" style="width:85px;">Device名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
							   oninput="cleanSpelChar(this)">
					</div>
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">所属机台：</lable>
					</div>
					<div class="layui-inline">
						<select name="machine" id="machine_search" lay-search="">
						</select>
					</div>
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">Device类型：</lable>
					</div>
					<div class="layui-inline">
						<select name="type" id="type" lay-search="">
							<option value="-" selected="selected">-</option>
							<option value="KVM">PC</option>
							<option value="CCTV">CCTV</option>
							<option value="Touch">TouchPanel</option>
						</select>
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="toolSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="toollist" lay-filter="tooltable"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="userListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="tooltoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加 PC')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAddCctv">{{lang('增加 CCTV')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAddTouch">{{lang('增加 Touch Panel')}}</button>
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
						<button type="button" class="layui-btn-primary layui-btn layui-btn-sm" lay-event="BatchUpdate" id="batchUpdateBtn">{{lang('批量升级')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="operation">
			<!--<a class="layui-btn layui-btn-xs m_UserBC" name="eventChange" lay-event="change">{{lang('切换Recipe')}}</a>-->
			<!--<a class="layui-btn layui-btn-xs m_UserBC" name="eventArea" lay-event="area">{{lang('区域')}}</a>-->
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>
			<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>
			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="more">更多 <i class="layui-icon layui-icon-down"></i></a>
		</script>
		<script type="text/html" id="checkedOne">
			<input type="checkbox" name="like1[write]" lay-skin="primary">
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="addkvm" class="layui-form" lay-filter="form-addkvm" style="display: none; min-width: 800px;">
		<fieldset class="layui-elem-field layui-field-title">
			<legend>Device配置</legend>
		</fieldset>
		<div class="layui-row">
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device名称</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device类型</label>
					</div>
					<div class="layui-inline edit">
						<select name="scheme" id="scheme_pc" lay-search="">
							<option value="MC" selected="selected">MC</option>
							<option value="NQVM">RCS</option>
							<option value="NQVM_">OF</option>
							<option value="NQVM_">Aten</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device所属机台</label>
					</div>
					<div class="layui-inline edit">
						<select name="machine" id="machine_pc" lay-search="">
							<option value="">请选择</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device地标位置</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">DeviceIP地址</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item txMark txMarkHide">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device 标号</label>
					</div>

					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="signal" autocomplete="off" placeholder="请输入Device标号"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device 副屏IP</label>
					</div>

					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="slaveip" autocomplete="off" placeholder="请输入Device副屏IP"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device录屏模式</label>
					</div>
					<div id="pc_record" class="layui-inline edit" lay-filter="pcrecord">
						<select name="recording" id="recording_pc" lay-search="">
							<option value="N" selected="selected">手动录屏</option>
							<option value="W">观看时录屏</option>
							<option value="C">操作时录屏</option>
							<option value="">跟随系统</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device安装日期</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" id="installTime_pc" name="installTime" placeholder="请选择时间">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device质保期</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="qualityTime" autocomplete="off" placeholder="请输入Device质保期"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
		</div>

		<!--<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
		</div>
		<div class="layui-inline edit">
			<select name="areaname" id="areaname_pc" lay-search="">
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="width:119px;">Device属于的组</label>
		</div>
		<div class="layui-inline edit">
			<select name="toolgroupname" id="toolgroupname_pc" lay-search="">
			</select>
		</div>
	</div>-->
		<fieldset class="layui-elem-field layui-field-title">
			<legend>其他配置</legend>
		</fieldset>
		<div class="layui-row">
			<div class="layui-col-xs6">
				<div class="layui-form-item stic eswin naura">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">相对鼠标</label>
					</div>
					<div id="relative_mouse" class="layui-inline edit" lay-filter="relative">
						<select name="relative" id="relative" lay-search="">
							<option value="" selected="selected">否</option>
							<option value="1">是</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item stic eswin naura">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">忽略权限</label>
					</div>
					<div id="ignore_sweep" class="layui-inline edit" lay-filter="sweep">
						<select name="sweep" id="sweep_pc" lay-search="">
							<option value="" selected="selected">否</option>
							<option value="1">是</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item stic naura" style="height: 90px;">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">扩展功能</label>
					</div>
					<div id="io_tools" class="layui-inline edit" lay-filter="extra">
						<select name="extra" id="extra" lay-search="">
							<option value="" selected="selected">无</option>
							<option value="Burnishing">抛光 机械按钮</option>
							<option value="Growing">拉晶 机械按钮</option>
							<option value="WinCC">拉晶 WinCC</option>
						</select>
						<div>&nbsp;</div>
						<input type="text" class="layui-input" name="extraMore" autocomplete="off" placeholder="请输入按钮地址" oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
		</div>	
	</form>

	<form id="addcctv" class="layui-form" lay-filter="form-addcctv" style="display: none; min-width: 800px;">
		<fieldset class="layui-elem-field layui-field-title">
			<legend>Device配置</legend>
		</fieldset>
		<div class="layui-row">
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device名称</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device类型</label>
					</div>
					<div class="layui-inline edit">
						<select name="scheme" id="scheme_cctv" lay-search="">
							<option value="TPLink" selected="selected">TPLink</option>
							<option value="HK">HK</option>
							<option value="DH">DH</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device所属机台</label>
					</div>
					<div class="layui-inline edit">
						<select name="machine" id="machine_cctv" lay-search="">
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device地标位置</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">DeviceIP</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device安装日期</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" id="installTime_cctv" name="installTime" placeholder="请选择时间">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device质保期</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="qualityTime" autocomplete="off" placeholder="请输入Device质保期"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item" style="height: 90px;">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">用户名/密码</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="user" autocomplete="off" placeholder="请输入用户名"
							   oninput="cleanNoIPADRESS(this)">
						<input type="text" style="margin-top: 10px;" class="layui-input" name="psd" autocomplete="off" placeholder="请输入密码"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
		</div>

		<fieldset class="layui-elem-field layui-field-title">
			<legend>其他配置</legend>
		</fieldset>
		<div class="layui-row">
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">支持PTZ</label>
					</div>
					<div id="has_ptz" class="layui-inline edit" lay-filter="ptz">
						<select name="ptz" id="sweep_cctv" lay-search="">
							<option value="" selected="selected">不支持</option>
							<option value="1">支持</option>
						</select>
					</div>
				</div>
			</div>
		</div>
	</form>

	<form id="addtouch" class="layui-form" lay-filter="form-addtouch" style="display: none; min-width: 800px;">
		<fieldset class="layui-elem-field layui-field-title">
			<legend>Device配置</legend>
		</fieldset>
		<div class="layui-row">
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device名称</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device所属机台</label>
					</div>
					<div class="layui-inline edit">
						<select name="machine" id="machine_touch" lay-search="">
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device地标位置</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device地标位置</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device IP 地址</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device 端口</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="port" autocomplete="off" placeholder="请输入端口"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device 密码</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="psd" autocomplete="off" placeholder="请输入密码"
							   oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device录屏模式</label>
					</div>

					<div class="layui-inline edit">
						<select name="recording" id="recording_touch" lay-search="">
							<option value="N" selected="selected">手动录屏</option>
							<option value="W">观看时录屏</option>
							<option value="C">操作时录屏</option>
							<option value="">跟随系统</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device安装日期</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" id="installTime_touch" name="installTime" placeholder="请选择时间">
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">Device质保期</label>
					</div>
					<div class="layui-inline edit">
						<input type="text" class="layui-input" name="qualityTime" autocomplete="off" placeholder="请输入Device质保期"
							   oninput="cleanSpelChar(this)">
					</div>
				</div>
			</div>
		</div>

		<fieldset class="layui-elem-field layui-field-title">
			<legend>其他配置</legend>
		</fieldset>
		<div class="layui-row">
			<div class="layui-col-xs6">
				<div class="layui-form-item bmot tm19 stic eswin naura">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">忽略键盘</label>
					</div>
					<div id="no_kb" class="layui-inline edit" lay-filter="noKB">
						<select name="noKB" id="noKB" lay-search="">
							<option value="" selected="selected">否</option>
							<option value="1">是</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item bmot tm19 stic eswin naura">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">忽略权限</label>
					</div>
					<div id="pc_record" class="layui-inline edit" lay-filter="sweep">
						<select name="sweep" id="sweep_touch" lay-search="">
							<option value="" selected="selected">否</option>
							<option value="1">是</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-col-xs6">
				<div class="layui-form-item bmot tm19 stic naura" style="height: 90px;">
					<div class="layui-inline">
						<label class="layui-form-label" style="width:119px;">扩展功能</label>
					</div>
					<div id="io_tools" class="layui-inline edit" lay-filter="extra">
						<select name="extra" id="extra" lay-search="">
							<option value="" selected="selected">无</option>
							<option value="Burnishing">抛光 机械按钮</option>
							<option value="Growing">拉晶 机械按钮</option>
							<option value="WinCC">拉晶 WinCC</option>
						</select>
						<div>&nbsp;</div>
						<input type="text" class="layui-input" name="extraMore" autocomplete="off" placeholder="请输入按钮地址" oninput="cleanNoIPADRESS(this)">
					</div>
				</div>
			</div>
		</div>
	</form>

	<form id="changeRecipe" class="layui-form" lay-filter="form-changeRecipe" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">是否启用RPA</label>
			</div>
			<div class="layui-inline edit">
				<input type="checkbox" name="enable" lay-skin="switch" lay-text="启用|关闭" title="">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Recipe Type</label>
			</div>
			<div class="layui-inline edit">
				<select name="recipe" id="scheme_rpa" lay-search="">
					<option value="1" selected="selected">Recipe1</option>
					<option value="2">Recipe2</option>
					<option value="3">Recipe3</option>
					<option value="4">Recipe4</option>
					<option value="5">Recipe5</option>
				</select>
			</div>
		</div>
	</form>

	<form id="chooseArea" style="display: none; height: calc(100% - 2px); width: calc(100% - 2px);">
		<div id="img-container">
			<div id="resizable-div">
				<div class="handle" id="top-left"></div>
				<div class="handle" id="top-right"></div>
				<div class="handle" id="bottom-left"></div>
				<div class="handle" id="bottom-right"></div>
				<div class="handle" id="top-center"></div>
				<div class="handle" id="bottom-center"></div>
				<div class="handle" id="middle-left"></div>
				<div class="handle" id="middle-right"></div>
			</div>
		</div>
	</form>

	<form id="toolkvm" class="layui-form" lay-filter="form-toolkvm" style="display:none;">
		<div class="layui-tab" style="height: calc(100% - 31px); min-width: inherit; padding: 10px !important;" lay-filter="tab">
			<ul class="layui-tab-title" style="font-size: 12px;">
				<li class="layui-this">视频信息</li>
				<li>设备信息</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item  layui-show" style="overflow-y: auto; overflow-x:auto;">
					<fieldset class="layui-elem-field">
						<legend><span>视频信息</span></legend>
						<blockquote class="layui-elem-quote layui-quote-nm" id="kvmVideoInfo">
						</blockquote>
					</fieldset>
				</div>
				<div class="layui-tab-item" style="overflow-y: auto; overflow-x: auto;">
					<fieldset class="layui-elem-field">
						<legend><span>设备信息</span></legend>
						<blockquote class="layui-elem-quote layui-quote-nm" id="kvmDeviceInfo">
						</blockquote>
					</fieldset>
				</div>
			</div>
		</div>
	</form>

	<div id="firmware" style="margin: 15px 20px 0; display: none;">
		<div class="layui-upload-drag" id="firmwareUpload">
			<i class="layui-icon"></i>
			<p>点击上传，或将文件拖拽到此处</p>
			<div class="layui-hide" id="uploadDemoView">
				<hr>
				<p>文件名：<span></span></p>
			</div>
		</div>
	</div>
</body>
</html>