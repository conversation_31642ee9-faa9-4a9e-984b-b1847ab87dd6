﻿//行合并 start
function execRadioRows(childFilterArr, ckChildFilterArr, flag)
{
	//获取td的个数和种类
	var chChildFilterTextObj = {};//种类对应的合并数量
	var chText = [];//td的种类
	var chIndex = [];

	for (var i = 0; i < ckChildFilterArr.length; i++)
	{
		var chChildText = flag ? ckChildFilterArr[i].innerHTML : ckChildFilterArr[i].textContent;
		if (chChildFilterTextObj[chChildText] == undefined)
		{
			chChildFilterTextObj[chChildText] = 1;
			chText.push(chChildText);
		} else
		{
			var num = chChildFilterTextObj[chChildText];
			chChildFilterTextObj[chChildText] = num * 1 + 1;
		}
	}
	for (var i = 0; i < chText.length; i++)
	{
		var chNum = 0;
		for (var j = 0; j < ckChildFilterArr.length; j++)
		{
			var chChildText = flag ? ckChildFilterArr[j].innerHTML : ckChildFilterArr[j].textContent;
			if (chText[i] == chChildText)
			{
				chNum = chNum + 1
			}
		}
		chIndex.push(chNum);
	}
	var newIndex = [];
	for (var i = 0; i < chIndex.length; i++)
	{
		if (i == 0)
		{
			newIndex.push(0);
		} else
		{
			var newNum = 0;
			for (var j = 0; j < chIndex.length; j++)
			{
				if (j < i) { newNum = newNum + chIndex[j]; }
				newIndex.push(newNum);
			}
		}
		//chIndex = newIndex;
		for (var j = 0; j < childFilterArr.length; j++)
		{
			var findFlag = false;
			for (var k = 0; k < newIndex.length; k++)
			{
				if (j == newIndex[k])
				{
					findFlag = true;
					if (newIndex[k + 1] != null)
					{
						childFilterArr[j].setAttribute("rowspan", newIndex[k + 1] - j);
						$(childFilterArr[j]).find("div.rowspan").parent("div.layui-table-cell").addClass("rowspanParent");
						$(childFilterArr[j]).find("div.layui-table-cell")[0].style.height = (newIndex[k + 1] - j) * 38 - 10 + "px";

					} else
					{
						childFilterArr[j].setAttribute("rowspan", (childFilterArr.length - j));
						$(childFilterArr[j]).find("div.rowspan").parent("div.layui-table-cell").addClass("rowspanParent");
						$(childFilterArr[j]).find("div.layui-table-cell")[0].style.height = (childFilterArr.length - j) * 38 - 10 + "px";
					}
				}
			}
			if (findFlag == false)
			{
				if (j % 8 != 0)
					childFilterArr[j].style.display = "none";
			}
		}
	}
}
function execRowspan(tableId, fieldName, index, flag, ckRows)
{
	var fixedNode = $('div[lay-id=' + tableId + ']').find(".layui-table-body")[index];
	var child = $(fixedNode).find("td");
	var childFilterArr = [];
	for (var j = 0; j < child.length; j++)
	{
		//child[j].getAttribute('data-field')
		if (child[j].getAttribute("data-field") == fieldName)
		{
			childFilterArr.push(child[j]);
		}
	}

	var ckChildFilterArr = [];
	if (fieldName == "0")
	{
		for (var j = 0; j < child.length; j++)
		{
			//child[j].getAttribute('data-field')
			if (child[j].getAttribute("data-field") == ckRows)
			{
				ckChildFilterArr.push(child[j]);
			}
		}
		execRadioRows(childFilterArr, ckChildFilterArr, flag);
		return;
	}

	//获取td的个数和种类
	var childFilterTextObj = {};
	for (var i = 0; i < childFilterArr.length; i++)
	{
		var childText = flag ? childFilterArr[i].innerHTML : childFilterArr[i].textContent;
		if (childFilterTextObj[childText] == undefined)
		{
			childFilterTextObj[childText] = 1;
		} else
		{
			var num = childFilterTextObj[childText];
			childFilterTextObj[childText] = num * 1 + 1;
		}
	}
	var canRowspan = true;
	var maxNum;//以前列单元格为基础获取的最大合并数
	var finalNextIndex;//获取其下第一个不合并单元格的index
	var finalNextKey;//获取其下第一个不合并单元格的值
	for (var i = 0; i < childFilterArr.length; i++)
	{
		(maxNum > 9000 || !maxNum) && (maxNum = $(childFilterArr[i]).prev().attr("rowspan") && fieldName != "8" ? $(childFilterArr[i]).prev().attr("rowspan") : 9999);
		var key = flag ? childFilterArr[i].innerHTML : childFilterArr[i].textContent;//获取下一个单元格的值
		var nextIndex = i + 1;
		var tdNum = childFilterTextObj[key];
		var curNum = maxNum < tdNum ? maxNum : tdNum;
		if (canRowspan)
		{
			for (var j = 1; j <= curNum && (i + j < childFilterArr.length);)
			{//循环获取最终合并数及finalNext的index和key
				finalNextKey = flag ? childFilterArr[i + j].innerHTML : childFilterArr[i + j].textContent;
				finalNextIndex = i + j;
				if ((key != finalNextKey && curNum > 1) || maxNum == j)
				{
					canRowspan = true;
					curNum = j;
					break;
				}
				j++;
				if ((i + j) == childFilterArr.length)
				{
					finalNextKey = undefined;
					finalNextIndex = i + j;
					break;
				}
			}
			childFilterArr[i].setAttribute("rowspan", curNum);
			if ($(childFilterArr[i]).find("div.rowspan").length > 0)
			{//设置td内的div.rowspan高度适应合并后的高度
				$(childFilterArr[i]).find("div.rowspan").parent("div.layui-table-cell").addClass("rowspanParent");
				$(childFilterArr[i]).find("div.layui-table-cell")[0].style.height = curNum * 38 - 10 + "px";
			}
			canRowspan = false;
		} else
		{
			childFilterArr[i].style.display = "none";
		}
		if (--childFilterTextObj[key] == 0 | --maxNum == 0 | --curNum == 0 | (finalNextKey != undefined && nextIndex == finalNextIndex))
		{
			canRowspan = true;
		}
	}
}
function layuiRowspan(tableId, fieldNameTmp, index, flag, ckRows)
{
	var fieldName = [];
	if (typeof fieldNameTmp == "string")
	{
		fieldName.push(fieldNameTmp);
	} else
	{
		fieldName = fieldName.concat(fieldNameTmp);
	}
	for (var i = 0; i < fieldName.length; i++)
	{
		execRowspan(tableId,fieldName[i], index, flag, ckRows);
	}
}
