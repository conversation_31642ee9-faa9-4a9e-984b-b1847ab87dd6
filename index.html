﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>北京珂阳智能远程控制系统</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css" />
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="../user/loginMenu.docss" />
	<link rel="stylesheet" href="lib/layui_exts/tinymce/tinymce/skins/ui/oxide/content.min.css">
	<link rel="stylesheet" href="lib/layui_exts/tinymce/tinymce/skins/content/default/content.min.css">
	<style type="text/css">
		.MinMaxClose {
			float: right;
			width: 30px;
			height: 30px;
			line-height: 30px;
			cursor: pointer;
		}

		.layui-nav .layui-icon {
			font-size: 15px;
			color: skyblue;
		}

		.layui-nav .layui-nav-more {
			top: 1px;
			font-size: 12px;
		}

		.layui-side-scroll li {
			margin-bottom: 1px;
			background-color: rgb(26,188,156);
		}

		.layui-nav-child {
			padding: 1px 0px;
		}

		.layui-side-scroll dd {
			background-color: rgb(52, 73, 94);
		}

			.layui-side-scroll dd:hover {
				background-color: #003311;
				color: red;
			}

		/*        .layui-layer-btn .layui-layer-btn0 {
			border-color: red !important;
			background-color: red !important;
			color: black;
		}*/

		.layui-table, .layui-table-view {
			margin: 0;
			background-color: transparent;
			color: white;
		}

		.layui-tab-title {
			height: 20px;
			display: flex;
		}

			.layui-tab-title .layui-this {
				color: red;
			}

				.layui-tab-title .layui-this:after {
					height: 21px;
				}

			.layui-tab-title li {
				font-size: 14px;
				line-height: 20px;
				min-width: auto;
				flex-grow: 1;
				margin-right: 2px;
				margin-left: 1px;
				border-radius: 2px;
				color: #fff;
			}

		.layui-tab-content {
			padding: 0px;
			height: 100%;
			bottom: 0px;
		}

		.layui-tab-item {
			height: 100%;
			overflow: scroll;
			white-space: nowrap;
		}

		.layui-table th, .layui-table td {
			padding: 2px 4px !important;
			border: 1px dotted #445954;
		}

		.layui-icon {
			cursor: pointer;
		}

		.table-view thead tr {
			background-color: transparent !important;
		}

		input::-webkit-input-placeholder {
			text-align: center;
		}

		.fav-button {
			width: 20px;
			height: 20px;
			border: none;
			background-color: transparent;
			color: white;
			line-height: 20px;
			display: inline-block;
			margin-top: 5px;
		}

			.fav-button:before {
				content: "\e68c";
			}

			.fav-button:hover {
				color: lightgray;
			}

		.fav-button-all {
			width: 20px;
			height: 20px;
			border: none;
			background-color: transparent;
			color: white;
			line-height: 20px;
			display: inline-block;
			margin-top: 5px;
		}

			.fav-button-all:hover {
				color: lightgray !important;
			}

		.fav-full:hover {
		}

		.fav-full:before {
			content: "\e68f";
		}

		.fav-full {
			color: rgb(26,188,156)
		}

		.fav-delete-full:before {
			content: "\e640";
		}

		.fav-delete-full {
			color: orangered;
			font-size: 20px;
		}

		#table-matrix-menu {
			width: 288px;
			height: 240px;
			background-color: #348ebd;
			border: solid 1px #348ebd;
		}

			#table-matrix-menu td {
				width: 32px;
				height: 30px;
				line-height: 20px;
				background-color: white;
				border: solid 1px #348ebd;
			}

		.layui-footer {
			left: 261px !important;
			box-shadow: -1px 0 4px rgb(0 0 0 / 20%) !important;
		}

		.cef-height {
			height: calc(100% + 40px) !important;
		}

		@media (max-width: 900px) {
			.nav-text {
				display: none; /* 在小屏自动隐藏文本 */
			}
		}

		.ew-tree-table {
			border: 1px dotted #445954 !important;
		}

		.ew-tree-table-cell-content:hover {
			color: #666 !important;
		}

		.ew-tree-table-head, .ew-tree-table:before, .ew-tree-table:after, .ew-tree-table .ew-tree-table-head:after {
			background-color: transparent !important;
		}

		.ew-tree-table-head {
			border-right-color: transparent !important;
		}

			.ew-tree-table-head tr {
				background-color: #1f2d3d !important;
			}

		.ew-tree-table .ew-tree-pack {
			font-size: 13px;
		}

		.tableSide {
			width: 270px !important;
			transition: width 0.5s ease;
		}

		.collapsed {
			width: 0px !important;
		}

		.iframeBody {
			left: 270px !important;
			padding-bottom: 40px;
			transition: left 0.5s ease;
		}

		.collapsedIframe {
			left: 0px !important;
		}

		#collapseLi, #tableMenu {
			display: none
		}
	</style>
	<script type="text/javascript">
		if (window.top != window.self)
			top.location.href = window.location.href;
		window.webName = 'index';
	</script>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/index.js?v=1"></script>
	<script type="text/javascript" src="../myconfig.dojs?user=1"></script>
	<script type="text/javascript" src="js/sortable.min.js"></script>
	<script type="text/javascript" src="js/request.js"></script>
	<script type="text/javascript" src="js/menu/menu.js"></script>
	<script type="text/javascript" id="document.ready">
		window.ldb('.page', window.webName);
		window.lastInput = 0;
		document.title = lang(window["CS"]['AssemblyTitle']);
		$(document).ready(function ()
		{
			if (!!window.Data)
			{
				var w = window.screen.width > 1450 ? window.screen.width : 1450;
				var h = window.screen.height > 900 ? window.screen.height : 900;
				/*window.Data('width', w + '');
				window.Data('height', (h - 40) + '');
				window.Data('top', 0 + '');
				window.Data('left', 0 + '');*/
				//$('.layui-layout-admin .layui-body').css('top', '30px');
				if (!window.Data('CT', null) || parseInt(window.Data('CT', null)) < 10)
					$('#n-ui-command').show();
				if (location.search.indexOf('?dev=') >= 0 || location.search.indexOf('&dev=') >= 0 || window.Data('Dev', '?') == 'Dev')
					$('#devTool').show();
				window.webbrowser = false;
			}
			else
			{
				window.webbrowser = true;
			}
			removeDeny();
			if (!window.login.Name)
			{
				location.href = location.href.replace(/index\.html/img, 'Login.html');
				return;
			}

			if (window.clientType === 3)
			{
				$('#Monitor').addClass('cef-height');
			}

			var t = '';
			for (var i = 0; i < 6; i++)
			{
				t += '<tr>';
				for (var ii = 0; ii < 6; ii++)
					t += '<td id="td-matrix-' + i + '-' + ii + '" title="' + (i + 1) + 'x' + (ii + 1) + '"><br/></td>';
				t += '</tr>';
			}
			$('#table-matrix-menu').html(t);
			$('#table-matrix-menu').on('mouseleave', function ()
			{
				$('#table-matrix-menu td').css('background-color', 'white');
			})
			$('#table-matrix-menu td').on('mousemove', function ()
			{
				var t = this.id;
				var r = parseInt(t.substring(10, 11));
				var c = parseInt(t.substring(12, 13));
				$('#table-matrix-menu td').css('background-color', 'white');
				for (var i = 0; i <= r; i++)
				{
					for (var ii = 0; ii <= c; ii++)
						$('#td-matrix-' + i + '-' + ii).css('background-color', 'skyblue');
				}
				$(this).css('background-color', 'deepskyblue');
			});
			$('#table-matrix-menu td').on('mouseup', function ()
			{
				var t = this.id;
				var r = parseInt(t.substring(10, 11)) + 1;
				var c = parseInt(t.substring(12, 13)) + 1;
				$('#dl-matrix-menu').removeClass('layui-show');

				var data1 = {};
				data1['username'] = window.login.name;
				data1['ID'] = window.login.ID;
				data1.row1 = r;
				data1.column1 = c;
				AjaxApi.request({
					url: "/User/EditUserGrid.do",
					data: data1,
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.code == "Success")
						{
							try
							{
								if (!!window.matrix)
									matrix.initGrid();
							}
							catch (e) { }
							layui.layer.msg('Current matrix size: ' + r + 'x' + c);
						} else
						{
							layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
						}
					}
				})
			});
			if (!window.login.Permission || !window.login.Permission.length)
				window.login.Permission = null;
			if (!!window.CS.noUserTitle || !window.login.Title)
				document.title = window.login.Name + ' ' + lang(window["CS"]['AssemblyTitle']);
			else
				document.title = window.login.Title + ' ' + lang(window["CS"]['AssemblyTitle']);
			if (!!window.CS.onready)
				window.CS.onready.call(this, window.$);
			$("#systemName").text(lang(window["CS"]['AssemblyTitle'].slice(0, 14)));
			$("#companyName").text(window["CS"]['Variables']['AssemblyCompany'] + '  欢迎您！');
			if (!!window.CS.noUserTitle || !window.login.Title)
				$('#UserTitle').text(window.login.Name + ' ');
			else
				$('#UserTitle').text(window.login.Title + ' ');
			$('#UserTitle').parent().css('width', ($('#UserTitle').width() + 50) + 'px');
			$('#MonitorId').html(window['local.config']['ProductVersion'].replace(/\.\d+$/, ''));
			$('#MonitorId').parent().parent().css('right', ($('#UserTitle').width() + 50) + 'px');
			var ve = window.ldb('version');
			if (ve.indexOf('Product') >= 0)
				$('#spanTitle').css('color', '#c91f3e');
			$('#spanTitle').text(ve);
			$('.layui-side-scroll li').each(function () // 当父菜单下所有子菜单全部隐藏时父菜单也隐藏
			{
				if (!!$(this).find('dl').length && !$(this).find('dd').length) // 没有子菜单
					$(this).remove(); // 切换隐藏父菜单
			});
			setTimeout(function ()
			{
				var a = $('.layui-side-scroll dd a');
				if (!a.length)
				{
					a = $('.layui-side-scroll a');
					a = $(a[0]);
				}
				else
				{
					a = $(a[0]);
					a.parent().trigger('click');//li/dl/dd
					$(a.parents('li').find('a')[0]).trigger('click');//li/a
				}
				a.trigger('click');
			}, 100); // 显示第一个有权限的菜单界面

			$('#homeLi a').on('mouseenter', function (e)
			{
				e.stopPropagation();
			})
			$('#homeLi .layui-icon-down').on('mouseenter', function ()
			{
				$('#dl-matrix-menu').addClass('layui-show');
				$(this).addClass('layui-nav-mored')
			})

			iframeIsOnload(); // 监听iframe是否重载
		});
	</script>
	<script type="text/javascript" id="function openWorkVideo(data)">
		function openWorkVideo(data)
		{
			if (!window.login || !window.login.ID)
				return;

			//$('#work-video').attr('src', 'workVideo.html');
			window.open('workVideo.html?ID=1933B55A7C8444F2B479010171120147&Url=' + encodeURIComponent('http://**************/'), '1933B55A7C8444F2B479010171120147');
		}
	</script>
	<script type="text/javascript" id="layui.use">
		//JavaScript代码区域
		layui.use(['element', 'layer', 'form', 'dropdown'], function ()
		{
			$ = layui.$;
			form = layui.form;
			layer = layui.layer;
			dropdown = layui.dropdown;
			lang.call(this, $);
			$('.layui-footer span').html(window["local.config"].ProductVersion.replace(/\.\d+$/, ''));

			$('.CloseWindow').on('click', function ()
			{
				layer.alert(lang('是否确认退出系统？'), {
					title: lang('系统退出')
					, btn: [lang('是'), lang('否')]
					, btnAlign: 'c'
					, btn1: function ()
					{
						top.window.close();
					}
				});
				return false;
			});
			initIsLdap();
			function initIsLdap()
			{
				var isLdap = true;
				AjaxApi.request({
					url: "/User/LoginType.do",
					data: {},
					dataType: 'text',
					success: function (result)
					{
						var data1 = [{
							title: lang('切换用户')
							, id: 99
						}, {
							title: lang('修改密码')
							, id: 100
						}];

						/*if (window.CS.isConfigUser())
						{
							data1.push({
								title: "修改菜单"
								, id: 101
							})
						}*/

						if (result == "OK")
						{
							data1 = [{
								title: lang('切换用户')
								, id: 99
							}];
							ddRender(data1);
						} else
						{
							ddRender(data1);
						}
					},
					error: function (r)
					{
						layer.alert(lang(r.textContent), { btn: [lang('确定')], title: lang('提示') });
					}
				})
			}
			function ddRender(dataDD)
			{
				//自定义事件 - hover
				dropdown.render({
					elem: '#menu-user'
					, trigger: 'hover'
					, data: dataDD
					, align: 'right'
					, click: function (obj)
					{
						if (obj.id == 99)
						{
							layer.confirm(lang('确定切换用户？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') },
								function (index, layero)
								{
									location.href = location.href.replace(/index\.html/img, 'Login.html');
								});
						}
						if (obj.id == 100)
						{
							$("#editPwd")[0].reset();
							var indexPwd = layer.open({
								title: lang('修改密码')
								, type: 1
								, resize: false
								, id: "isAdd"
								, content: $('#editPwd')
								, btn: [lang('确认'), lang('取消')]
								, btnAlign: 'c'
								, btn1: function (index)
								{
									AjaxApi.request({
										url: "/User/GetUserInfo.do",
										data: { pwd: $('#oldPwd').val().trim() },
										dataType: 'text',
										success: function (result)
										{
											var old = $('#oldPwd').val().trim();
											var newP = $('#newPwd').val().trim();
											var conf = $('#confirmPwd').val().trim();
											if (old.length == 0)
											{
												layer.alert(lang('旧密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
												return false;
											}
											if (newP.length == 0)
											{
												layer.alert(lang('新密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
												return false;
											}
											if (conf.length == 0)
											{
												layer.alert(lang('确认密码不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
												return false;
											}
											if (conf.getByteLength() > limitByteLengthPwd || newP.getByteLength() > limitByteLengthPwd
												|| conf.getByteLength() < minLimitByteLength || newP.getByteLength() < minLimitByteLength)
											{
												layer.alert(lang('请输入有效的密码长度！限制最小长度为：') + minLimitByteLength + lang('，最大长度为：') + limitByteLengthPwd, { btn: [lang('确定')], title: lang('提示') });
												return false;
											}
											if (newP != conf)
											{
												layer.alert(lang('新密码和确认密码不一致！！！'), { btn: [lang('确定')], title: lang('提示') });
												return false;
											}
											var res = $.eval(result);
											if (!res.data)
											{
												layer.alert(lang('旧密码不正确！'), { btn: [lang('确定')], title: lang('提示') });
											} else
											{
												AjaxApi.request({
													url: "/User/EditPassword.do",
													data: { confirmPwd: conf },
													dataType: 'text',
													success: function (result)
													{
														var res = $.eval(result);
														if (res.code == "Success")
														{
															layer.close(indexPwd);
															layer.alert(lang('修改密码成功'), { btn: [lang('确定')], title: lang('提示') });
														}
														else
															layer.alert(lang('修改密码失败'), { btn: [lang('确定')], title: lang('提示') });
													},
													error: function (r)
													{
														layer.alert(lang('修改密码失败'), { btn: [lang('确定')], title: lang('提示') });
													}
												})
											}
										},
										error: function (r)
										{
											layer.alert(lang(r.textContent), { btn: [lang('确定')], title: lang('提示') });
										}
									})
								}
								, success: function (result)
								{
									AjaxApi.request({
										url: "/User/CheckUserCowin.do",
										data: {},
										dataType: 'text',
										success: function (result)
										{
											var res = $.eval(result);
											if (res.data == 1)
											{
												$('#divAlert').show();
												$('#oldPwd').attr('disabled', true);
												$('#newPwd').attr('disabled', true);
												$('#confirmPwd').attr('disabled', true);
												$('.layui-layer-btn0').css('pointer-events', 'none');
												$('.layui-layer-btn0').css('cursor', 'default');
											} else
											{
												$('#divAlert').hide();
												$('#oldPwd').attr('disabled', false);
												$('#newPwd').attr('disabled', false);
												$('#confirmPwd').attr('disabled', false);
											}
										},
										error: function (r)
										{
											layer.alert(lang(r.textContent), { btn: [lang('确定')], title: lang('提示') });
										}
									})
								}
								, end: function ()
								{
									$('#editPwd')[0].reset();
									$('#editPwd').css('display', 'none');
								}
							});
						}
						if (obj.id == 101)
						{
							console.log(101);
						}
					}
				});
			}
		});
	</script>
</head>
<body class="layui-layout-body">
	<div class="layui-layout layui-layout-admin">
		<div class="layui-header" style="background-color: #263445; ">
			<div class="layui-logo" style="width: 270px !important; background-color: #1f2d3d; ">
				<!--img class="layui-inline" src="imgs/favicon.png" style="margin:5px 0 0 5px;" /-->
				<div class="layui-inline" style="margin-left:0px;color: rgba(255,255,255,.95);font-size: 16px;font-weight: bold; margin-top:4px;"><img id="imgOpenVideo" src="/OpenVideo.dopng" width="1" height="1" /><span id="systemName">智能远程控制系统（iRCM）</span></div>
				<span id="spanTitle" style="position: absolute; top: 22px; font-size: 12px; left: 56px; color: rgba(255,255,255,.95); height: 36px;"></span>
			</div>
			<!-- 头部区域（可配合layui 已有的水平导航） -->
			<ul id="nav-ul" class="layui-nav layui-layout-left" lay-filter="nav-menu" style="left:270px !important; font-size: 24px; min-height: 62px; white-space: nowrap;">
				<!-- 移动端显示 -->
				<li id="collapseLi" class="layui-nav-item layui-show-xs-inline-block" lay-header-event="menuLeft">
					<!--<i class="layui-icon layui-icon-spread-left"></i>-->
					<i id="expandIcon" class="layui-icon layui-icon-spread-left" onclick="toggleWidth()"></i>
					<i id="collapseIcon" class="layui-icon layui-icon-shrink-right layui-hide" onclick="toggleWidth()"></i>
				</li>
				<li id="homeLi" class="layui-nav-item">
					<a href="matrix.html" target="Monitor">
						<i class="layui-icon layui-icon-app" title="主界面"></i>
						<span class="nav-text"> 主界面</span>
					</a>
					<dl class="layui-nav-child" id="dl-matrix-menu">
						<dd><table id="table-matrix-menu"></table></dd>
					</dl>
				</li>
			</ul>
			<ul class="layui-nav layui-layout-right">
				<li class="layui-nav-item" id="menu-user">
					<i class="layui-icon layui-icon-username" style="font-size:20px;"></i>
					<i id="UserTitle" style="font-family: Arial;"></i>
					<i class="layui-icon layui-icon-more-vertical layui-font-12"></i>
				</li>
			</ul>
			<script type="text/javascript" id="generateMenu">
				function generateMenuHTML(menuData)
				{
					let html = '';

					for (var i = 0; i < menuData.length; i++)
					{
						var menu = menuData[i];

						if (menu.type === 'group' && menu.children)
						{
							html += generateGroupMenu(menu);
						} else if (menu.type === 'item')
						{
							html += generateSingleMenu(menu);
						} else if (menu.special)
						{
							html += generateSpecialMenu(menu);
						}
					}

					return html;
				}

				function generateGroupMenu(menu)
				{
					var html = '<li class="layui-nav-item" id="' + menu.id + '">' +
						'<a href="javascript:;"><i class="layui-icon ' + menu.icon + '" title="' + menu.name + '"></i> <span class="nav-text"> ' + menu.name + '</span></a>' +
						'<dl class="layui-nav-child">';

					for (var i = 0; i < menu.children.length; i++)
					{
						var child = menu.children[i];
						var className = child.className ? ' class="' + child.className + '"' : '';
						html += '<dd id="' + child.id + '"' + className + '><a href="' + child.url + '" target="Monitor"><i class="layui-icon ' + child.icon + '"></i> ' + child.name + '</a></dd>';
					}

					html += '</dl></li>';
					return html;
				}

				function generateSingleMenu(menu)
				{
					return `<li class="layui-nav-item" id="${menu.id}">
								<a href="${menu.url}" target="Monitor">
									<i class="layui-icon ${menu.icon}" title="${menu.name}"></i><span class="nav-text"> ${menu.name}</span>
								</a>
							</li>`;
				}

				function generateSpecialMenu(menu)
				{
					return `<li id="${menu.id}" class="layui-nav-item" lay-filter="nav-matrix" lay-unselect="1" onselect="javascript: return false;" onselectstart="javascript: return false;">
								<a href="#" onclick="${menu.action}; return false;"><i class="layui-icon ${menu.icon}" title="${menu.name}"></i><span class="nav-text"> ${menu.name}</span></a>
							</li>`;
				}

				function insertMenusAfterHomeLi(menuData)
				{
					var navUl = document.getElementById('nav-ul');
					var homeLi = document.getElementById('homeLi');

					var menuHTML = generateMenuHTML(menuData);

					var tempDiv = document.createElement('div');
					tempDiv.innerHTML = menuHTML;

					var currentElement = homeLi;
					var menuItems = tempDiv.children;

					for (var i = 0; i < menuItems.length; i++)
					{
						var menuItem = menuItems[i];
						navUl.insertBefore(menuItem, currentElement.nextSibling);
						currentElement = menuItem;
						i--;
					}
				}

				insertMenusAfterHomeLi(RCS_Menu);
			</script>
			<script type="text/javascript" id="permissionMenu">
				/*for (let item in window.deny)
				{
					$('#' + item).css('display', 'none');
				}*/
				var pageArray = ['ToolManagement', 'AreaSetup', 'ToolGroupSetup', 'ToolSetup', 'EquipmentSetup', 'ToolMachineSetup', 'LayoutSetup',
					'AlarmSetup', 'LogManagement', 'ToolLog', 'SystemLog', 'NoticeSetup', 'SummarySearch',
					'SystemSetup', 'UserSetup', 'RoleSetup', 'UserGroupSetup', 'AuthSetup', 'ParamSetup'];
				var userGroupMenus = ['SystemSetup', 'UserGroupSetup'];
				var isFirstLoad = true;
				if (window.login.Permission)
				{
					var pagePermission = window.login.Permission.split(',');
					for (var i = 0; i < pageArray.length; i++)
					{
						var idx = pagePermission.findIndex(function (item) { return item == pageArray[i] });
						if (idx < 0)
						{
							if (userGroupMenus.includes(pageArray[i]))
							{
								$('#' + pageArray[i]).css('display', 'none');
							} else
							{
								$('#' + pageArray[i]).remove();
							}

						}
					}
				}

				var navUl = document.querySelectorAll('#nav-ul li');
				$('#homeLi').on('click', function ()
				{
					for (var i = 0; i < navUl.length; i++)
					{
						navUl[i].classList.remove('layui-this');
					}
					$(this).addClass('layui-this');
				})
			</script>
			<script type="text/javascript" id="function toggleWidth()">
				function toggleWidth()
				{
					var table = document.getElementById('tableMenu');
					table.classList.toggle('collapsed');
					var iframe = document.getElementById('iframeDiv');
					iframe.classList.toggle('collapsedIframe');
					var footer = document.getElementById('companyName');
					footer.classList.toggle('collapsedIframe');
					var collapseIcon = document.getElementById('collapseIcon');
					var expandIcon = document.getElementById('expandIcon');
					collapseIcon.classList.toggle('layui-hide');
					expandIcon.classList.toggle('layui-hide');
				}

				$(document).ready(function ()
				{
					if (!!RCS_ShowTable)
					{
						toggleWidth();
						$("#tableMenu").show();
						$("#collapseLi").css('display', 'inline-block');
					}
				})

			</script>
		</div>
		<div id="tableMenu" class="layui-side tableSide collapsed" style="background-color: #1f2d3d; ">
			<div class="layui-side-scroll" style="min-width: 270px; overflow-y: hidden;">
				<!-- 左侧导航区域（可配合layui已有的垂直导航） -->
				<div class="layui-tab" style="height: calc(100% - 31px); min-width:inherit;" lay-filter="tab">
					<ul class="layui-tab-title" style="font-size: 12px;">
						<li>区域</li>
						<li>机台组</li>
						<li class="layui-this">收藏夹</li>
					</ul>
					<div class="layui-tab-content">
						<div class="layui-tab-item" style="overflow-y: auto; overflow-x:auto;">
							<form class="layui-form" lay-filter="form-area" id="form-area" onsubmit="return false;">
								<input table="area" type="text" style="width: 93%; margin-left: 3%; margin-top: 3px;" name="toolname" autocomplete="off" placeholder="请输入机台名称"
									   oninput="cleanNoIPADRESS(this); $('#form-area').children('a:eq(0)').show(); if (this.value.length == 0) $('#form-area').children('a:eq(0)').css('display', 'none');" />
								<a id="areaClear" style="cursor:pointer;height: 20px;width: 20px;margin-top: 3px;position: absolute;margin-left: -25px;line-height:20px;text-align:center;display:none;">x</a>
							</form>
						</div>
						<div class="layui-tab-item" style="overflow-y: auto; overflow-x: auto;">
							<form class="layui-form" lay-filter="form-toolgroup" id="form-toolgroup" onsubmit="return false;">
								<input table="toolgroup" type="text" style="width: 93%; margin-left: 3%; margin-top: 3px;" name="toolname" autocomplete="off" placeholder="请输入机台名称"
									   oninput="cleanNoIPADRESS(this); $('#form-toolgroup').children('a:eq(0)').show(); if (this.value.length == 0) $('#form-toolgroup').children('a:eq(0)').css('display', 'none');" />
								<a id="toolgroupClear" style="cursor:pointer;height: 20px;width: 20px;margin-top: 3px;position: absolute;margin-left: -25px;line-height:20px;text-align:center;display:none;">x</a>
							</form>
						</div>
						<div class="layui-tab-item layui-show" style="overflow-y: auto; overflow-x: auto;">
							<form class="layui-form" lay-filter="form-fav" id="form-fav" onsubmit="return false;">
								<input table="fav" type="text" style="width: 93%; margin-left: 3%; margin-top: 3px;" name="toolname" autocomplete="off" placeholder="请输入机台名称"
									   oninput="cleanNoIPADRESS(this); $('#form-fav').children('a:eq(0)').show(); if (this.value.length == 0) $('#form-fav').children('a:eq(0)').css('display', 'none');" />
								<a id="favClear" style="cursor:pointer;height: 20px;width: 20px;margin-top: 3px;position: absolute;margin-left: -25px;line-height:20px;text-align:center;display:none;">x</a>
							</form>
						</div>
						<div id="tabTable" style=" position: absolute; top: 55px; left: 0px;">
							<table id="table-tab" class="layui-table" lay-filter="table-tab" cellpadding="0" cellspacing="0" border="0"></table>
						</div>
					</div>
				</div>
				<!--<iframe id="Tree" name="Tree" style="width: 100%; height: calc(100% - 10px); margin-top: 10px;" frameborder="0" src="tree.html"></iframe>-->
			</div>
		</div>

		<script type="text/html" id="fav-toolbar">
			<div class="layui-btn-container" style="height:30px;">
				<div class="layui-inline" style="width:50%;">
					<button type="button" class="layui-btn layui-btn-sm" style="background-color: rgb(26,188,156); margin: 7px 5px 1px 9px; height: 19px; line-height: 19px; width: calc(100% - 14px); " lay-event="add">增加</button>
				</div>
				<div class="layui-inline" style="width:50%;">
					<button type="button" class="layui-btn layui-btn-sm" style="background-color: rgb(26,188,156); margin: 7px 5px 1px 5px; height: 19px; line-height: 19px; width: calc(100% - 14px);" lay-event="unfold" id="UnfoldBtn">全部展开</button>
					<button type="button" class="layui-btn layui-btn-sm" style="background-color: rgb(26,188,156); margin: 7px 5px 1px 5px; height: 19px; line-height: 19px; width: calc(100% - 14px); display: none; " lay-event="fold" id="FoldBtn">全部折叠</button>
				</div>
			</div>
		</script>

		<script type="text/javascript">
			var tabIdx = 2;
			layui.config({ base: 'lib/layui/' });
			layui.extend({ tinymce: '../layui_exts/tinymce/tinymce' });
			layui.extend({ treeTable: './treeTable-lay' });
			layui.use(['element', 'layer', 'form', 'dropdown', 'tinymce', 'util', 'treeTable'], function () {
				$ = layui.jquery;
				treeTable = layui.treeTable;
				element = layui.element;
				layer = layui.layer;
				form = layui.form;
				dropdown = layui.dropdown;
				textarea = layui.tinymce;

				noticeShow();
				window.removeDeny();

				element.on('tab(tab)', function (data) {
					if (data.index == 0)
					{
						tabIdx = 0;
						RenderTreeTable(areaData);
						setCheckData(checkedAreaData);
						tabTable.expandAll();
					} else if (data.index == 1)
					{
						tabIdx = 1;
						RenderTreeTable(toolgroupData);
						setCheckData(checkedToolGroupData);
						tabTable.expandAll();
					} else if (data.index == 2)
					{
						tabIdx = 2;
						//RenderTreeTable(favData);
						FillFavTreeTable(true);
						//setCheckData(checkedFavData);
					}
				});

				FillFavTreeTable();

				// 防抖
                function searchDebounce(func, wait, immediate) {
                    var timeout;
                    return function executedFunction() {
                        var context = this;
                        var args = arguments;
                        var later = function () {
                            timeout = null;
                            if (!immediate) func.apply(context, args);
                        };
                        var callNow = immediate && !timeout;
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                        if (callNow) func.apply(context, args);
                    };
                }
				function filterInput(name)
				{
					$("input[table="+ name +"]").on('input', function ()
					{
						if (!!tabTable)
						{
							searchDebounce(filterTabTable('form-' + name), 300);
						}
					});
					$("#"+ name +"Clear").on("click", function ()
					{
						$('#form-' + name)[0].reset();
						$(this).css('display', 'none');
						if (!!tabTable)
						{
							filterTabTable('form-' + name);
						}
					})
				}
				filterInput('area');
				filterInput('toolgroup');
				filterInput('fav');
				function filterTabTable(formname) {
					var data = form.val(formname);
					var value = data.toolname;
					tabTable.filterData(value);
				}

				treeTable.on('tool(table-tab)', function (obj)
				{
                    var data = obj.data, event = obj.event;
					switch (obj.event) {
						case 'show':
							Show(data);
							break;
						case 'add':
							AddToFav(data);
							break;
						case 'del':
							DeleteFromFav(data);
							break;
						default:
							layer.msg('未定义方法:' + event);
							break;
					}
				})

				// 获取点击的树节点中所有节点数据
				function getSelectNodes(node)
				{
					if (!node || typeof node !== 'object')
					{
						return [];
					}

					var result = [node];

					if (node.children && node.children.length)
					{
						node.children.forEach(function (child)
						{
							if (child.children && child.children.length)
							{
								var childNodes = getSelectNodes(child);
								childNodes.shift();
								result.push(child);
								result = result.concat(childNodes);
							} else
							{
								result.push(child);
							}
						});
					}

					return result;
				}
				function AddToFav(data) {
					clearCheckedData();
					var select_data = getSelectNodes(data);
					if (select_data.length > 0)
					{
						AddFavorites(select_data);

						select_data.forEach(function (item, index, arr)
						{
							if (checkVideo(item.Type))
							{
								item.fav = true;
							}

							$('#' + item.ID + item.tableType).addClass('fav-full');
							$('#' + item.ID + item.tableType).attr('lay-event', 'del');
						});

					}
				}

				function DeleteFromFav(data) {
					layer.confirm(lang('是否要从收藏夹中取消？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
						clearCheckedData();
						var select_data = getSelectNodes(data);
						select_data.forEach(function (item, index, arr) {
							if (checkVideo(item.Type)) {
								item.fav = false;
                                $('#' + item.ID + item.tableType).removeClass('fav-full');
                                $('#' + item.ID + item.tableType).attr('lay-event', 'add');
							}
							else {
								$('#' + item.ID + item.tableType).removeClass('fav-full');
								$('#' + item.ID + item.tableType).attr('lay-event', 'add');
							}
						});

                        outFavorites(select_data);
					});
				}

				// treetable 点击树节点事件
				treeTable.on('checkbox(table-tab)', function (obj)
				{
					justShowVideo(obj);
				});

				treeTable.on('toolbar(table-fav)', function (data) {
					var event = data.event;
					if (event == "add") {
						var index1 = layer.open({
							title: lang('增加收藏夹')
							, type: 1
							, resize: false
							, content: $('#form-fav-add')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {
								//form.render();
							}
							, btn1: function (value, index) {
								var data1 = form.val("form-fav-add");
                                if (data1.favname.length == 0) {
									layer.alert(lang('请输入收藏夹名称！！！'), { btn: [lang('确定')], title: lang('提示') });
									return false;
								}

								if (!!data.config.data) {
                                    data1.index = data.config.data.length;
								}

								AjaxApi.request({
                                    url: "/Favorites/AddFavority.do",
									data: data1,
									dataType: 'text',
									success: function (result) {
                                        var res = $.eval(result);
                                        if (res.code == "Success") {
                                            layer.msg(lang('添加成功'));
                                            layer.close(index1);
                                            FillFavTreeTable();
                                        }
									}
								})

                                $('#form-fav-add')[0].reset();
								$('#form-fav-add').css('display', 'none');
								layer.close(index1);
							}
							, end: function () {
								$('#form-fav-add')[0].reset();
								$('#form-fav-add').css('display', 'none');
								layer.close(index1);
							}
						});
					}
					else if (event == "unfold") {

					}
					else if (event == "fold") {

					}
				});

                // 勾选显示机台
				function justShowVideo(d)
				{
					if (d.data && d.checked && d.data._checkedByProgram)
					{
						return false;
					}
					if (d.data) d.data._checkedByProgram = false;
					clearCheckedData();
					var select_data = [];
					if (tabIdx == 0)
					{
						d.type == 'all' ?
							select_data = (d.checked ? areaData : []) :
							select_data = tabTable.checkStatus(false);
						checkedAreaData = select_data;
					} else if (tabIdx == 1)
					{
						d.type == 'all' ?
							select_data = (d.checked ? toolgroupData : []) :
							select_data = tabTable.checkStatus(false);
						checkedToolGroupData = select_data;
					} else if (tabIdx == 2)
					{
						d.type == 'all' ?
							select_data = (d.checked ? favData : []) :
							select_data = tabTable.checkStatus(false);
						checkedFavData = select_data;
					}

                    if (!!window.matrix.onlyViewVideo)
						matrix.onlyViewVideo(select_data, true);
				}

				// 设置默认勾选
				function setCheckData(arr)
				{
					if (arr.length == 0) return;
					var select = [];
					arr.forEach((item) =>
					{
						select.push(item.ID);
					})
					tabTable.setChecked(select);
				}

				function noticeShow()
				{
					var n_id = window.ldb('n_id');
					var n_show = window.ldb('n_show');

					AjaxApi.request({
                        type: "post",
                        url: "../Notice/GetNotice.do",
						data: {},
						dataType: 'text',
						success: function (result) {
                            var res = $.eval(result);
                            if (res.code == "Success") {
                                if (!!res.id == false && !!res.data == false) {
                                    return;
                                }

                                if (!!n_id && !!n_show) {
                                    if (res.id == n_id && n_show == 'on')
                                        return;
                                }

								var index1 = layer.open({
									title: false
									, type: 1
									, resize: true
									, content: $('#form-nevershow')
									, btn: [lang('确认')]
									, btnAlign: 'c'
									, shade: 0.8
									, moveType: 1
                                    , area: ['60%', '70%']
									, success: function ()
									{
										var html = $('#notice_div').html();
										$('#notice_div').html(res.data + html);
										n_id = res.id;
									}
									, btn1: function (value, index)
									{
										layer.close(index1);
									}
									, end: function ()
									{
										var result = form.val('form-nevershow');
										if (!!result.nevershow)
										{
											window.ldb('n_show', result.nevershow);
											window.ldb('n_id', n_id);
										}
										$('#form-nevershow')[0].reset();
										$('notice_div').css('display', 'none');
									}
								});
                            }
						}
					})
				}
			})
			// 清空勾选
			function clearCheckedData()
			{
				checkedAreaData = [];
				checkedToolGroupData = [];
				checkedFavData = [];
			}

			function iframeIsOnload()
			{
				var iframe = document.getElementById('Monitor');

				iframe.addEventListener('load', function ()
				{
					/*console.log('iframe 页面已切换');
					var currentHtml = iframe.contentWindow.location.href.match(/\/([^\/]+)$/)[1];
					console.log('当前 URL:', iframe.contentWindow.location.href);*/
					clearCheckedData();
					if (tabTable) tabTable.removeAllChecked();
				});
			}

            function buildTree(data) {
                var map = new Map();
                var result = [];

                data.forEach(function(item) {
                    map.set(item.id, item);
                });

                // 递归构建树形结构
                function build(id) {
                    var item = map.get(id);
                    if (!item) return;

                    var children = [];
                    data.forEach(function (child) {
                        if (child.pid === id) {
                            children.push(build(child.id));
                        }
                    });

                    item.children = children;
                    return item;
                }

                data.forEach(function (item) {
                    if (!item.pid) {
                        result.push(build(item.id));
                    }
                });

                return faltten(result);
			}
            function faltten(tree) {
                var flatData = [];
                var stack = tree.slice();
                var idx = 0;
                while (stack.length) {
                    var node = stack.shift();
                    if (node.children) {
                        stack.unshift.apply(stack, node.children);
                    }
                    node.dataIndex = idx++;
                    flatData.push(node);
                }
                return flatData;
			}

			var tabTable;
			var checkedAreaData = [];
			var checkedToolGroupData = [];
            var checkedFavData = [];
			var areaData = [];
			var toolgroupData = [];
			var favData = [];
			var toolsData = [];

			// treeIframe中回调确认弹窗
			function treeIframeDialog(row, callback) {
				layer.confirm(lang('是否要从收藏夹中取消？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
                    if (callback) {
                        callback(row);
                    }
                    layer.close(index);
				})
			}
			function treeIframeTips(text) {
                layer.msg(text);
			}

			function ColumnsName(name) {
				var titleName = 'Tool';
				if (tabIdx == 0) {
					titleName = 'Area';
				} else if (tabIdx == 1) {
					titleName = 'Group';
				} else {
					titleName = 'Fav';
				}
				return [
					{ type: "checkbox", width: '50px' },
					{
						key: 'title',
						title: titleName,
						templet: function (item)
						{
							var color = '#aaa';
							if (item.pid == 0 && !checkVideo(item.Type))
							{
								color = 'red';
							} else if (!checkVideo(item.Type))
							{
								color = 'green';
							}
							return '<span style="color:' +  color + '">' + item.title + '</span>';
						}
					},
					{
						title: '操作',
						align: 'center',
						width: '55px',
						templet: function (item)
						{
							if (item.tableType == CS.ConfigData.TYPE_AREA || item.tableType == CS.ConfigData.TYPE_TOOLGROUP) {
								if (!!item.fav)
									return '<button class="fav-button layui-icon fav-full" lay-event="del" id="' + item.ID + item.tableType + '"></button>';

								return '<button class="fav-button layui-icon" lay-event="add" id="' + item.ID + item.tableType + '"></button>';
							}
							else if (item.tableType == CS.ConfigData.TYPE_FAVORITES) {
								// if (item.Type == CS.AuthData.TYPE_FAVORITES) {
								/*if (!!item.Video) {
									/*var buttons = '<button class="fav-button-all layui-icon layui-icon-addition" title="添加子收藏夹" style="color:aquamarine" lay-event="add" id="' + item.ID + CS.ConfigData.TYPE_FAVORITES + '"></button>';
									buttons += '<button class="fav-button-all layui-icon layui-icon-subtraction" title="删除当前收藏夹" style="color:#FF5722" lay-event="del" id="' + item.ID + CS.ConfigData.TYPE_FAVORITES + '"></button>';
									buttons += '<button class="fav-button-all layui-icon layui-icon-set" title="重命名当前收藏夹" lay-event="edit" id="' + item.ID + CS.ConfigData.TYPE_FAVORITES + '"></button>';

									return buttons;*/

									return '<button class="fav-button layui-icon fav-delete-full" lay-event="del" id="' + item.ID + CS.ConfigData.TYPE_FAVORITES + '"></button>';
								} else {
									return '<button class="fav-button layui-icon fav-delete-full" lay-event="del" id="' + item.ID + item.tableType + '"></button>';
								/*}*/

							}
						}
					}
				];
			}

			function RenderTreeTable(data) {
				/*var toolbar_name = []
				if (table == '#table-fav') {
                    toolbar_name = '#fav-toolbar'
				}*/

				var config = {
					elem: '#table-tab',
					height: 'full-75',
					//toolbar: toolbar_name,
					defaultToolbar: [],
					data: data,
					sort: true,
					size: 'sm',
					tree: {
						iconIndex: 1,
						idName: 'id',
						pidName: 'pid',
						isPidData: true,
						getIcon: ''
					},
					cols: ColumnsName(),
				}

				tabTable = treeTable.render(config);
				if (tabIdx == 2)
				{
					var el = document.querySelector('#tabTable .ew-tree-table-box tbody');
					//设置配置
					var ops = {
						animation: 500,
						dataIdAttr: "data-index",
						ghostClass: 'sortable-ghost',
						//拖动结束
						onEnd: function (evt)
						{
							var arr = sortable.toArray();
							var favId = [];
							for (var i = 0; i < arr.length; i++)
							{
								var idx = Number(arr[i]);
								favId.push(favData[idx].favorited.ID)
							}
							SortFavorites(favId)
						},
					};
					//初始化
					var sortable = Sortable.create(el, ops);
				}
			}
			function showTableData()
			{
				switch (tabIdx)
				{
					case 0:
						RenderTreeTable(areaData);
						tabTable.expandAll();
						break;
					case 1:
						RenderTreeTable(toolgroupData);
						tabTable.expandAll();
						break;
					case 2:
						RenderTreeTable(favData);
						tabTable.expandAll();
						break;
				}
			}
            function deepClone(obj) {
                // 如果不是对象类型或者为 null，则直接返回
                if (obj === null || typeof obj !== 'object') {
                    return obj;
                }

                // 创建一个新的对象或数组来存储深拷贝的结果
                var result = Array.isArray(obj) ? [] : {};

                // 遍历原对象的所有属性并进行深度拷贝
                for (var key in obj) {
                    if (Object.prototype.hasOwnProperty.call(obj, key)) {
                        result[key] = deepClone(obj[key]); // 递归深拷贝
                    }
                }

                return result;
            }
			function FillAreaTreeTable(areaDatas, equipData) {
				$('#form-area')[0].reset();
                if (!areaDatas || areaDatas.length == 0) return

				var slave_Data = [];
                slave_Data = deepClone(equipData);
                for (var i = 0; i < areaDatas.length; i++) {
                    if (!!areaDatas[i].Parent && !!areaDatas[i].Parent.length)
                        areaDatas[i].pid = areaDatas[i].Parent.substring(0, 32);
                    else
                        areaDatas[i].pid = 0;

                    if (!!areaDatas[i].ID && !!areaDatas[i].ID.length)
                        areaDatas[i].id = areaDatas[i].ID;

                    if (!!areaDatas[i].Name && !!areaDatas[i].Name.length)
                        areaDatas[i].title = areaDatas[i].Name;

                    areaDatas[i].tableType = CS.ConfigData.TYPE_AREA;
                }

				for (var i = 0; i < slave_Data.length; i++) {
                    slave_Data[i].id = slave_Data[i].ID;
                    slave_Data[i].title = slave_Data[i].Name;
                    slave_Data[i].tableType = CS.ConfigData.TYPE_AREA;
                    if (slave_Data[i].Area) {
                        slave_Data[i].pid = slave_Data[i].Area;
                        slave_Data[i].Parent = slave_Data[i].Area;
					} else {
                        slave_Data[i].pid = 0;
                    }
                    areaDatas.forEach(function (item) {
                        if (slave_Data[i].pid == item.ID && !!slave_Data[i].fav) {
                            item.fav = true;
                        }
                    });
                }
				var area_data = areaDatas.concat(slave_Data);
				areaData = area_data;
			}

			function FillToolGroupTreeTable(groupData, equipData) {
                $('#form-toolgroup')[0].reset();
                if (!groupData || groupData.length == 0) return
                var slave_Data = [];
                slave_Data = deepClone(equipData);
                for (var i = 0; i < groupData.length; i++) {
                    if (!!groupData[i].Parent && !!groupData[i].Parent.length)
                        groupData[i].pid = groupData[i].Parent.substring(0, 32);
                    else
                        groupData[i].pid = 0;

                    if (!!groupData[i].ID && !!groupData[i].ID.length)
                        groupData[i].id = groupData[i].ID;

                    if (!!groupData[i].Name && !!groupData[i].Name.length)
                        groupData[i].title = groupData[i].Name;

                    groupData[i].tableType = CS.ConfigData.TYPE_TOOLGROUP;
				}

                for (var i = 0; i < slave_Data.length; i++) {
                    slave_Data[i].id = slave_Data[i].ID;
                    slave_Data[i].title = slave_Data[i].Name;
                    slave_Data[i].tableType = CS.ConfigData.TYPE_TOOLGROUP;
                    if (slave_Data[i].Unit) {
                        slave_Data[i].pid = slave_Data[i].Unit;
                        slave_Data[i].Parent = slave_Data[i].Unit;
                    } else {
                        slave_Data[i].pid = 0;
                    }
                    groupData.forEach(function (item) {
                        if (slave_Data[i].pid == item.ID && !!slave_Data[i].fav) {
                            item.fav = true;
                        }
                    });
				}
				var toolgroup_data = groupData.concat(slave_Data);
				// console.log(333, toolgroup_data, buildTree(toolgroup_data))
				toolgroupData = toolgroup_data;
			}

            // 快排
            function quickSort(arr, compareFn) {
                if (arr.length <= 1) {
                    return arr;
                }

                const pivot = arr[0];
                const left = [];
                const right = [];

                for (let i = 1; i < arr.length; i++) {
                    if (compareFn(arr[i], pivot) < 0) {
                        left.push(arr[i]);
                    } else {
                        right.push(arr[i]);
                    }
                }

                return quickSort(left, compareFn).concat([pivot], quickSort(right, compareFn));
            }

			function FillFavTreeTable(showFav)
			{
				clearCheckedData();
				AjaxApi.request({
                    url: "../listMyTools.do",
					data: {},
					dataType: 'text',
					success: function (result)
					{
						var res = $.eval(result);
						if (res.code != "Success" || !res.data)
							return;

						var topic = '';
						var areas = [];
						var groups = [];
						var favs = [];
						var tools = []
						var favs_data = []
						for (var i = res.data.length - 1; i >= 0; i--)
						{
							var v = res.data[i];
							if (v.Type == CS.ConfigData.TYPE_FAVORITES && !v.Video)
								favs.push(v);
							else if (v.Type == CS.ConfigData.TYPE_AREA)
								areas.push(v);
							else if (v.Type == CS.ConfigData.TYPE_TOOLGROUP)
								groups.push(v);
							else if (v.Type == 'AuthVideo')//(v.RowKey == 'AuthData')//
								window.CS.AuthData[v.ID] = v.Data;
							else if (v.RowKey == 'VideoData')
								tools.push(v);
							/*else if (v.Type != CS.ConfigData.TYPE_FAVORITES)
								tools.push(v);*/
						}
						for (var i = tools.length - 1; i >= 0; i--)
						{
							var v = tools[i];
							v.AuthData = window.CS.AuthData[v.ID];
							window.CS.VideoData[v.ID] = v;
							window.ldb('#' + v.ID, JSON.stringify(v));
							for (var ii = res.data.length - 1; ii >= 0; ii--)
							{
								var vv = res.data[ii];
								if (vv.Type == CS.ConfigData.TYPE_FAVORITES && vv.Video == v.ID)
								{
									v.fav = true;
									if (!vv.Value)
										vv.Value = 0;
									for (k in vv)
									{
										if (typeof (v[k]) == typeof (v.undefined))
											v[k] = vv[k];
									}
									v.favorited = vv;
									favs_data.push(v);
									if (!v.Authority)
										v.Authority = v.UrlAuthority;
									topic = topic + ',' + v.Authority + ',' + v.ID;
									break;
								}
							}
						}
						if (topic.length > 8000)
						{
							window.topic = null;
							window.serverMessage('topic=');
						}
						else
						{
							window.topic = 'topic=,' + topic + ',';
							window.serverMessage(window.topic);
						}
						toolsData = deepClone(tools);
						FillAreaTreeTable(areas, toolsData)
						FillToolGroupTreeTable(groups, toolsData)

						// console.log(areas, groups, favs, tools, favs_data)
						var fav_show = []
						if (favs_data.length > 0)
						{
							/*favs_data.sort(function (a, b) {
								if (a.favorited && b.favorited) {
									return a.favorited.Value - b.favorited.Value;
								}
							});*/
							favs_data = quickSort(favs_data, function (a, b)
							{
								if (a.favorited && b.favorited)
								{
									return a.favorited.Value - b.favorited.Value;
								}
								return 0;
							});
							for (var i = 0; i < favs_data.length; i++)
							{
								favs_data[i].title = favs_data[i].Name;
								favs_data[i].id = favs_data[i].ID;
								favs_data[i].pid = 0;
								favs_data[i].fav = true;
								favs_data[i].tableType = CS.ConfigData.TYPE_FAVORITES;
								favs_data[i].Position = i; //video视频画面index
								// checkedFavData.push(favs_data[i]);
							}
						}

						fav_show = deepClone(favs_data);
						// console.log(111, fav_show, buildTree(fav_show))
						window.favorites = fav_show;
						window.toolDatas = toolsData; // 所有设备数据
						favData = fav_show;
						showTableData();

						var iframe = document.getElementById('Monitor');
						iframe.contentWindow.postMessage('parentLoaded', '*'); // 给iframe发送消息
						if (!!window.matrix)
						{
							if (!!window.matrix.onlyViewVideo && !!showFav)
								matrix.onlyViewVideo(fav_show, true);
						}

					},
                    error: function (r) {
                        layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
                    }
				})
			}

			// 监听matrix页面是否加载完成
            function iframeMatrixLoaded() {
				var treeIframe = document.getElementById('Tree');
                if (treeIframe && treeIframe.contentWindow && treeIframe.contentWindow.callMaxtrixMethod) {
                    treeIframe.contentWindow.callMaxtrixMethod();
                }
            }
            // 将方法绑定到 window 对象
            window.iframeMatrixLoaded = iframeMatrixLoaded;

			function onModifyFav(elem) {
				dropdown.render({
					elem: elem
                    , data: [{
                        title: '添加子收藏夹'
						, id: 100
						, templet: '<i class="layui-icon layui-icon-addition"></i> {{d.title}}'
                        , href: '#'
					},
                        { type: '-'},
					{
						title: '修改当前收藏夹名'
                        , templet: '<i class="layui-icon layui-icon-set"></i> {{d.title}}'
						, id: 101
                        , href: '#'
                    }]
                    , show: true
                    , click: function (obj) {
						if (obj.id == 100) {
                            var index1 = layer.open({
                                title: lang('增加子收藏夹')
                                , type: 1
                                , resize: false
                                , content: $('#form-fav-add')
                                , btn: [lang('确认'), lang('取消')]
                                , btnAlign: 'c'
                                , success: function () {
                                }
                                , btn1: function (value, index) {
                                    var data1 = form.val("form-fav-add");
                                    if (data1.favname.length == 0) {
                                        layer.alert(lang('请输入收藏夹名称！！！'), { btn: [lang('确定')], title: lang('提示') });
                                        return false;
                                    }

									data1.Parent = elem.id.replace('fav_', '');

									AjaxApi.request({
                                        url: "/Favorites/AddFavority.do",
                                        data: data1,
										dataType: 'text',
										success: function (result) {
                                            var res = $.eval(result);
                                            if (res.code == "Success") {
                                                layer.msg(lang('添加成功'));
                                                layer.close(index1);
                                                FillFavTreeTable();
                                            }
										}
									})

                                    $('#form-fav-add')[0].reset();
                                    $('#form-fav-add').css('display', 'none');
                                    layer.close(index1);
                                }
                                , end: function () {
                                    $('#form-fav-add')[0].reset();
                                    $('#form-fav-add').css('display', 'none');
                                    layer.close(index1);
                                }
                            });
						}
						else if (obj.id == 101) {
                            var index1 = layer.open({
                                title: lang('修改收藏夹名称')
                                , type: 1
                                , resize: false
                                , content: $('#form-fav-add')
                                , btn: [lang('确认'), lang('取消')]
                                , btnAlign: 'c'
                                , success: function () {
                                    form.val("form-fav-add", {
                                        favname: $(elem).attr('name')
                                    });
                                }
                                , btn1: function (value, index) {
                                    var data1 = form.val("form-fav-add");
                                    if (data1.favname.length == 0) {
                                        layer.alert(lang('请输入收藏夹名称！！！'), { btn: [lang('确定')], title: lang('提示') });
                                        return false;
                                    }

									data1.ID = elem.id.replace('fav_', '');

									AjaxApi.request({
                                        url: "/Favorites/UpdateFavority.do",
										data: data1,
										dataType: 'text',
										success: function (result) {
                                            var res = $.eval(result);
                                            if (res.code == "Success") {
                                                layer.msg(lang('添加成功'));
                                                layer.close(index1);
                                                FillFavTreeTable();
                                            }
										}
									})

                                    $('#form-fav-add')[0].reset();
                                    $('#form-fav-add').css('display', 'none');
                                    layer.close(index1);
                                }
                                , end: function () {
                                    $('#form-fav-add')[0].reset();
                                    $('#form-fav-add').css('display', 'none');
                                    layer.close(index1);
                                }
                            });
						}
                    }
                });
			}

			function onNoticeShow() {
                var index1 = layer.open({
                    title: lang('公告设置')
                    , type: 1
                    , resize: true
                    , content: $('#form-notice-area')
                    , btn: [lang('确认'), lang('预览'), lang('取消')]
					, btnAlign: 'c'
                    , area: ['60%', '70%']
					, success: function () {
                        textarea.render({
							elem: "#notice-area",
							width: '100%',
							height: '100%',
							contextmenu: '',
                            quickbars_selection_toolbar: '',
                            setup: function (editor) {
                                editor.ui.registry.addContextToolbar('textselection', {
                                    items: 'bold italic | blockquote',
                                    position: 'line',
                                    scope: 'node'
                                });
							},
                        });
                    }
					, btn1: function (value, index) {
						var content = textarea.get('#notice-area').getContent()
						if (content.length != 0) {
							AjaxApi.request({
								type: "post",
                                url: "../Notice/SetNotice.do",
                                data: content,
								dataType: 'text',
								success: function (result) {
                                    var res = $.eval(result);
                                    if (res.code == "Success") {
                                        layer.msg(lang('添加成功'));
                                        layer.close(index1);
                                    }
								}
							})
						}

                        $('#form-notice-area')[0].reset();
						$('#form-notice-area').css('display', 'none');
                        layer.close(index1);
					}
					, btn2: function (value, index) {
						$('#form-nevershow').show();
						var content = textarea.get('#notice-area').getContent();
                        var index1 = layer.open({
                            title: lang('公告预览')
                            , type: 1
                            , resize: true
                            , content: $('#notice_div')
                            , btn: [lang('确认')]
                            , btnAlign: 'c'
                            , shade: 0.8
                            , area: ['60%', '70%']
							, success: function () {
                                $('#notice_div').html(content);
                            }
                            , end: function () {
                                $('#form-nevershow').css('display', 'none');
                            }
						});

						return false;
					}
                    , end: function () {
                        $('#form-notice-area')[0].reset();
                        $('#form-notice-area').css('display', 'none');
                        layer.close(index1);
                    }
                });
			}
			// 添加到收藏
			function AddFavorites(data) {
				var gdata = { ids: ',' };
                var favData = data.filter(function (item, index, arr) { return checkVideo(item.Type) });
                for (var i = 0; i < favData.length; i++) {
                    gdata.ids += favData[i].ID + ',';
				}

                if (gdata.ids.length < 2) {
                    return false;
                }
                AjaxApi.request({
                    url: "/InFavorite.do",
                    data: gdata,
                    dataType: 'text',
                    success: function (result) {
                        layer.msg('成功添加到收藏夹');
                        FillFavTreeTable(true);
                    }
                })
			}
			// 排序收藏夹
			function SortFavorites(data) {
				var gdata = {ids: ',', indexes: ','}
				gdata.ids = '';
				gdata.indexes = '';
				for (var i = 0; i < data.length; i++) {
					gdata.ids += ',' + data[i];
					gdata.indexes += ',' + i;

				}

				AjaxApi.request({
					url: "/SortFavorite.do",
					data: gdata,
					dataType: 'text',
					success: function (result) {
                        FillFavTreeTable(true);
					}
				})
			}
			// 从收藏夹删除
			function outFavorites(data) {
				var gdata = { ids: ',' };
                for (var i = 0; i < data.length; i++) {
					if (checkVideo(data[i].Type) && !!data[i].favorited) {
                        gdata.ids += data[i].favorited.ID + ',';
                    }
                }
                if (gdata.ids.length < 2) {
                    return false;
				}

                AjaxApi.request({
                    url: "/OutFavorite.do",
                    data: gdata,
                    dataType: 'text',
                    success: function (result) {
                        layer.msg('成功从收藏夹中删除');
                        FillFavTreeTable(true);
                    }
                })
			}

            function checkVideo(type) {
                if (type == CS.VideoData.TypeKVM ||
                    type == CS.VideoData.TypeCCTV ||
                    type == CS.VideoData.TypeTouch)
                    return true;
                else
                    return false;
			}
		</script>

		<div id="iframeDiv" class="layui-body iframeBody collapsedIframe">
			<!-- 内容主体区域 -->
			<iframe id="Monitor" name="Monitor" style="width: 100%; height: 100%; background-color: white;" frameborder="0" src="matrix.html"></iframe>
		</div>

		<div id="companyName" class="layui-footer collapsedIframe">
			<!-- 底部固定区域 -->
			北京珂阳科技有限公司合肥分公司 欢迎您！
		</div>
	</div>
	<script type="text/javascript">
		//JS
		layui.config({ base: 'lib/layui/' });
		layui.extend({ notice: '../layui_exts/notice/notice' });
		layui.use(['element', 'notice', 'layer', 'util'], function ()
		{
			$ = layui.$;
			util = layui.util;
			layer = layui.layer;
			notice = layui.notice;
			if (!!notice)
			{
				notice.options =
				{
					closeButton: true,//显示关闭按钮
					debug: false,//启用debug
					positionClass: "toast-bottom-right",//弹出的位置,
					showDuration: "300",//显示的时间
					hideDuration: "500",//消失的时间
					timeOut: "3000",//停留的时间
					extendedTimeOut: "1000",//控制时间
					showEasing: "swing",//显示时的动画缓冲方式
					hideEasing: "linear",//消失时的动画缓冲方式
					iconClass: 'toast-info', // 自定义图标，有内置，如不需要则传空 支持layui内置图标/自定义iconfont类名
					onclick: null, // 点击关闭回调
				};
				notice.info(lang(window["local.config"]['AssemblyTitle']) + '\r\n' + window["local.config"].ProductVersion.replace(/\.\d+$/, ''));
			}
			//头部事件
			util.event('lay-header-event', {
				//左侧菜单事件
				menuLeft: function (othis)
				{
					//layer.msg('展开左侧菜单的操作', { icon: 0 });
				}
				, menuRight: function ()
				{
					/*layer.open({
						type: 1
						, content: '<div style="padding: 15px;">处理右侧面板的操作</div>'
						, area: ['260px', '100%']
						, offset: 'rt' //右上角
						, anim: 5
						, shadeClose: true
					});*/
				}
			});

		});
	</script>

	<!--<iframe style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; right: 0px; bottom: 0px; z-index: 1111;" frameborder="0" scrolling="no" src="imgs/logo.png" name="work-video" id="work-video"></iframe>-->
	<div id="devLayer" style="display: none; padding: 2px; border: dashed 1px blue;">
		<ul type="A">
			<li><span onclick="javascript: this.innerHTML = navigator.userAgent;">UA</span></li>
			<li>-</li>
			<li><a href="javascript: void(0);" onclick="javascript: openVideo.call(this, '1933B55A7C8444F2B479010171120121', 'hk://**************/');">WinV121</a></li>
			<li>-</li>
			<li><a href="javascript: void(0);" onclick="javascript: openVideo.call(this, '1933B55A7C8444F2B479010171120145', 'http://**************/');">WinV145</a></li>
			<li><a href="javascript: void(0);" onclick="javascript: openVideo.call(this, '1933B55A7C8444F2B479010171120148', 'http://**************/', '1933B55A7C8444F2B479010171120146', 'http://**************/');">WinV148 &amp; 146</a></li>
			<li>-</li>
			<li><a href="javascript: void(0);" onclick="javascript: openVideo.call(this, '1933B55A7C8444F2B470010171120158', 'nvqm://**************/');">NVQM-158</a></li>
			<li>-</li>
			<li><a href="javascript: void(0);" onclick="javascript: openWorkVideo.call(this);">Open Web Video</a></li>
			<li>-</li>
			<li><a href="javascript: void(0);" onclick="javascript: top.window.Data('Dev', location.href);">Developer Tools</a></li>
			<li><a href="javascript: window.location.reload();">Reload</a></li>
		</ul>
		<hr />
		<script type="text/javascript" id="function searchVideo()">
			function doOpenVideo(id, url)
			{
				if (arguments[arguments.length - 1] == arguments.callee)//controller callback
				{
					var qs = $.parseUrl('ID=' + id);
					console.log(id);
					qs.js = window.ldb('#' + qs.ID);
					if (!qs.js)
						return -1;

					try
					{
						qs.vi = $.eval(qs.js);
					}
					catch (x)
					{
						return -2;
					}

					window.Data('OpenVideo', '?Process=' + qs.Process + '&Text=' + encodeURIComponent(qs.vi.Name + lang(' - 操作台 - ') + document.title));
					return;
				}

				//Side=256,192,50,50,0,0 //side-max,side-min,side-max,side-min,side-max,side-min,side-save //save = (leng % 2 == 1)
				return openVideo.call(this, id, url, 'Side=256,192,64,64,0,0,1');
			}
		</script>
		<script type="text/javascript" id="function searchVideo()">
			function searchVideo()
			{
				if (!arguments.length)
				{
					var ajax = {};
					ajax.data = {};
					ajax.data.name = $('#dev-video-name').val();
					ajax.data.take = parseInt($('#dev-video-size').val());
					ajax.data.skip = parseInt($('#dev-video-page').val());
					ajax.data.tick = Date.now();
					ajax.url = '/video.do';
					ajax.success = arguments.callee;
					ajax.error = arguments.callee;
					$.ajax(ajax);
					return;
				}

				var v = arguments[0];
				if (arguments.length == 1)
				{
					$('#dev-video-page').val(parseInt($('#dev-video-page').val()) + v);
					return;
				}

				if (!!v.readyState)
				{
					$('#dev-list').html('<li>' + v.responseText + '</li>');
					return;
				}

				try
				{
					var vs = eval('(' + v + ')');
					v = '';
					for (var i = 0; i < vs.length; i++)
						v = v + '<li><a href="javascript: void(0);" onclick="javascript: doOpenVideo.call(this, \'' + vs[i].ID + '\', \'' + vs[i].Url + '\');">' + vs[i].Name + '</a></li>\r\n';
					v = v + '<li style="color: green;">' + new Date().toLocaleString() + '</li>';
					$('#dev-list').html(v);
				}
				catch (x)
				{
					$('#dev-list').html('<li>' + x.message + '</li><li>' + v + '</li>');
				}
			}
		</script>
		<ol id="dev-list" type="I">-</ol>
		<hr />
		<form action="?" target="_blank">
			<input type="text" id="dev-video-name" style="text-align: center;" /><br />
			<input type="button" onclick="javascript: searchVideo.call(this, -1);" value="&lt;" style="text-align: center;" />
			<input type="text" id="dev-video-page" value="0" style="width: 30px; text-align: center;" />
			<input type="button" onclick="javascript: searchVideo.call(this, 1);" value="&gt;" style="text-align: center;" />
			<input type="text" id="dev-video-size" value="10" style="width: 30px; text-align: center;" />
			<input type="button" onclick="javascript: searchVideo.call(this);" value="search" style="text-align: center;" />
		</form>
	</div>
	<a id="devTool" style="color: red; position: absolute; display: none; right: 0; bottom: 0px;z-index: 999;" href="javascript: top.window.Data('Dev', 'Dev');" onclick="javascript: layui.layer.open({ type: 1, content: $('#devLayer'), title: this.title }); return false;" title="development tools">dt</a>
	<form class="layui-form" id="editPwd" lay-filter="form-addEdit" style="display:none;padding-top: 20px;padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:120px;">旧密码:</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="oldPwd" name="oldPwd" autocomplete="off" placeholder="请输入旧密码">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:120px;">新密码:</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="newPwd" name="newPwd" autocomplete="off" placeholder="请输入新密码">
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:120px;">确认密码:</label>
			</div>
			<div class="layui-inline edit">
				<input type="password" class="layui-input" id="confirmPwd" name="confirmPwd" autocomplete="off" placeholder="请输入确认密码">
			</div>
		</div>
		<div class="layui-form-item" id="divAlert" style="display: none;text-align: center;">
			<div class="layui-inline" style="color:red;">
				<span>此用户禁止修改密码</span>
			</div>
		</div>
	</form>

	<form class="layui-form" lay-filter="form-fav-add" id="form-fav-add" style="display:none; width:300px;">
		<div style="margin:10px;">
			<input type="text" class="layui-input" name="favname" autocomplete="off" placeholder="请输入收藏夹名称" oninput="cleanSpelChar(this)" />
		</div>
	</form>

	<form class="layui-form" lay-filter="form-notice-area" id="form-notice-area" style="display: none; width: 100%; height: calc(100% - 20px);">
		<div style="margin: 10px; width: calc(100% - 20px); height: 100%">
			<textarea id="notice-area"></textarea>
		</div>
	</form>


	<form id="form-nevershow" class="layui-form" lay-filter="form-nevershow" style="padding: 10px; display: none; overflow: auto; height: calc(100% - 20px);">
		<div id="notice_div" class="mce-content-body layui-form-item" style="margin: 10px;">
		</div>

		<div class="layui-form-item" style="margin: 0px;">
			<input class="layui-form-checkbox" type="checkbox" name="nevershow" lay-skin="primary" title="本次公告不再显示">
		</div>
	</form>

	<iframe id="frm-hide" style="position: absolute; top: 0px; left: 0px; width: 1px; height: 1px; border: none;" width="1" height="1" frameborder="0" scrolling="no"></iframe>
</body>
</html>
