﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>汇总查询</title>
	<style type="text/css">
		body {
			margin: 0;
			padding: 0;
			font-family: sans-serif;
		}

		#loading-indicator {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: #f5f5f5;
			display: flex;
			justify-content: center;
			align-items: center;
			z-index: 9999;
		}

		#loading-spinner {
			width: 50px;
			height: 50px;
			border: 5px solid rgba(0, 0, 0, 0.1);
			border-radius: 50%;
			border-top-color: #3498db;
			animation: spin 1s ease-in-out infinite;
		}

		@keyframes spin {
			to {
				transform: rotate(360deg);
			}
		}

		#main {
			width: 100%;
			min-width: 1000px;
			height: 100%;
			min-height: 755px;
			background-color: #eee;
			padding-bottom: 10px;
			opacity: 0;
			transition: opacity 0.5s ease;
		}

		.item-wrapper {
			width: 100%;
			height: 100%;
			padding: 0 5px;
			font-size: 60px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.chartsGroup {
			height: calc(75vh - 28px);
			width: calc(100% - 20px);
			min-height: 520px;
			margin: 5px 10px;
			display: flex;
		}

			.chartsGroup .leftCharts,
			#circlepie-wrapper {
				height: 100%;
				margin: 0 5px;
				border-radius: 10px;
			}

		#line-wrapper,
		#barwb-wrapper {
			width: 100%;
			height: calc(50% - 5px);
			background-color: #fff;
			border-radius: 10px;
		}

		#line-wrapper, #barwb-wrapper, #circlepie-wrapper {
			position: relative;
		}

		#line-from, #bar-from, #kvmList-wrapper {
			position: absolute;
			top: 5px;
			right: 10px;
		}
	</style>

	<link rel="stylesheet" href="lib/layui/css/layui.css" media="print" onload="this.media='all'">
	<link rel="stylesheet" href="css/common.css" media="print" onload="this.media='all'">
	<link rel="stylesheet" href="lib/layui/treetable/treetable.css" media="print" onload="this.media='all'">

	<link rel="preload" href="lib/jquery-1.12.3.min.js" as="script">
	<link rel="preload" href="js/common.js" as="script">
</head>
<body style="overflow-x: auto;">
	<div id="loading-indicator">
		<div id="loading-spinner"></div>
	</div>

	<div id="main">
		<div style="height: calc(25% - 20px); width: calc(100% - 20px); min-height: 220px; display:flex; padding:5px 10px;">
			<div style="height: 100%; width: 25%; background-color: white; margin: 0px 5px; border-radius: 10px;">
				<div style="margin: 10px 0px 0px 10px;">
					<label style="font-size: 18px; font-weight: bold;">已运行时间</label>
				</div>
				<div style="width: 100%; height: 188px;">
					<div class="item-wrapper">
						<i class="layui-icon" style="font-weight:bold; font-size: 60px; color: dodgerblue; margin-right: 10px;">&#xe68d;</i>
						<span id="timespan" style="font-size: 28px;">--</span>
					</div>
				</div>
			</div>

			<div style="height: 100%; width: 25%; background-color: white; margin: 0px 5px; border-radius: 10px; ">
				<div style="margin: 10px 0px 0px 10px;">
					<label style="font-size: 18px; font-weight: bold;">当前网络带宽</label>
				</div>
				<div id="" style="width: 100%; height: 188px;">
					<div class="item-wrapper">
						<i class="layui-icon layui-icon-wifi" style="font-weight:bold; font-size: 60px; color: dodgerblue; margin-right: 10px;"></i>
						<p>
							<span id="web_text" style="font-size: 50px;">--</span>
							<span style="font-size: 30px;">MB/s</span>
						</p>
					</div>
				</div>
			</div>

			<div style="height: 100%; width: 25%; background-color: white; margin: 0px 5px; border-radius: 10px; ">
				<div style="margin: 10px 0px 0px 10px;">
					<label style="font-size: 18px; font-weight: bold;">当前设备个数</label>
				</div>
				<div style="width: 100%; height: 188px;">
					<div class="item-wrapper">
						<i class="layui-icon" style="font-weight:bold; font-size: 60px; color: dodgerblue; margin-right: 10px;">&#xe628;</i>
						<p>
							<span id="equip_num" style="font-size: 50px;">--</span>
							<span style="font-size: 30px;">个</span>
						</p>
					</div>
				</div>
			</div>

			<div style="height: 100%; width: 25%; background-color: white; margin: 0px 5px; border-radius: 10px;">
				<div style="margin: 10px 0px 5px 10px;">
					<label style="font-size: 18px; font-weight: bold;">ServerCPU</label>
				</div>
				<div id="" style="width: 100%; height: 188px;">
					<div style="margin-left: calc(50% - 100px); margin-top: 0px; height: 100%;">
						<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
							<circle r="70"
									cx="100"
									cy="100"
									fill="transparent"
									stroke="#eee"
									stroke-width="12" />

							<circle id="cpu"
									r="70"
									cx="100"
									cy="100"
									fill="transparent"
									stroke="yellow"
									stroke-width="12"
									stroke-dasharray="439.822964"
									stroke-dashoffset="439.822964"
									transform="rotate(270, 100, 100)" />
							<text id="cpu_text" font-size="25px" font-weight="bold" x="75" y="110">0.0%</text>
						</svg>
					</div>
				</div>
			</div>
		</div>

		<div class="chartsGroup">
			<div class="leftCharts" style="width: 55%;">
				<div id="line-wrapper" style="margin-bottom: 10px;">
					<div id="line" style="width: 100%; height: 100%;"></div>
					<form id="line-from" class="layui-form" lay-filter="form-line">
						<div class="layui-form-item">
							<div class="layui-inline">
								<input type="text" class="layui-input" id="time_line" name="lineTime" placeholder="请选择时间">
							</div>
						</div>
					</form>
				</div>
				<div id="barwb-wrapper">
					<div id="barwb" style="width: 100%; height: 100%;"></div>
					<form id="bar-from" class="layui-form" lay-filter="form-bar">
						<div class="layui-form-item">
							<div class="layui-inline">
								<input type="text" class="layui-input" id="time_bar" name="barTime" placeholder="请选择时间">
							</div>
						</div>
					</form>
				</div>
			</div>
			<div id="circlepie-wrapper" style="width: 45%; background-color: #fff;">
				<div id="circlepie" style="width: 100%; height: 100%;"></div>
				<form id="kvmList-wrapper" class="layui-form" lay-filter="form-kvm">
					<div class="layui-form-item">
						<div class="layui-inline">
							<input type="text" class="layui-input" id="time_pie" name="pieTime" placeholder="请选择时间">
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>

	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>

	<script type="text/javascript">

		var $loadingIndicator, $main;

		function getDaysBetween(date1, date2)
		{
			const d1 = new Date(date1);
			const d2 = new Date(date2);
			const diff = Math.abs(d2.getTime() - d1.getTime());
			return Math.floor(diff / (24 * 60 * 60 * 1000));
		}

		function getLastMonth()
		{
			var end = new Date();
			var start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
			return formatDate(start) + ' - ' + formatDate(end);
		}

		function formatDate(date)
		{
			var year = date.getFullYear();
			var month = date.getMonth() + 1;
			var day = date.getDate();
			month = month < 10 ? '0' + month : month;
			day = day < 10 ? '0' + day : day;
			return year + '/' + month + '/' + day;
		}

		var circle_pie_chart, line_chart, barWB_chart;
		var userData = [];
		var equipData = [];
		var kvmOfflineData = [];
		var kvmData = [];
		var daysNumber = 0;
		var emptyOption = {
			title: {
				text: '暂无数据',
				x: 'center',
				y: 'center',
			}
		};
	</script>

	<script type="text/javascript" src="lib/layui/layui.js" defer></script>
	<script type="text/javascript" src="js/echarts.min.js" defer></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4" defer></script>
	<script type="text/javascript" src="../local.config.js" defer></script>
	<script type="text/javascript" src="js/common.js" defer></script>
	<script type="text/javascript" src="../user/loginMenu.dojs" defer></script>
	<script type="text/javascript" src="js/summary.js" defer></script>
	<script type="text/javascript" src="js/request.js" defer></script>
	<script type="text/javascript" src="../Log/GetPerformance.dojs?js=window.summary" defer></script>

	<script type="text/javascript">
		$(function ()
		{
			$loadingIndicator = $('#loading-indicator');
			$main = $('#main');

			var initQueue = [];

			initQueue.push(function (next)
			{
				layui.use(['element', 'layer', 'jquery', 'form', 'laypage', 'laydate', 'table'], function ()
				{
					$ = layui.$;
					laydate = layui.laydate;
					form = layui.form;
					table = layui.table;
					layer = layui.layer;
					element = layui.element;

					initDatePickers();
					next();
				});
			});

			initQueue.push(function (next)
			{
				if (!circle_pie_chart)
				{
					setTimeout(function ()
					{
						initCharts();
						next();
					}, 100);
				} else
				{
					next();
				}
			});

			initQueue.push(function (next)
			{
				var timeValue = getLastMonth().split('-');
				timeFilter(timeValue[0], timeValue[1]);
				next();
			});

			function executeQueue(index)
			{
				if (index >= initQueue.length)
				{
					$loadingIndicator.fadeOut(300);
					$main.css('opacity', 1);
					return;
				}

				initQueue[index](function ()
				{
					executeQueue(index + 1);
				});
			}

			executeQueue(0);

			window.addEventListener('resize', onResize);
		});

		function onResize()
		{
			if (circle_pie_chart) circle_pie_chart.resize();
			if (line_chart) line_chart.resize();
			if (barWB_chart) barWB_chart.resize();
		}

		function initDatePickers()
		{
			laydate.render({
				elem: '#time_bar',
				type: 'date',
				format: 'yyyy/MM/dd',
				range: true,
				value: getLastMonth(),
				done: function (value, date)
				{
					if (value.length == 0) return;
					var time = value.split('-');
					timeFilter(time[0], time[1], 'bar');
				}
			});

			laydate.render({
				elem: '#time_line',
				type: 'date',
				format: 'yyyy/MM/dd',
				range: true,
				value: getLastMonth(),
				done: function (value, date)
				{
					if (value.length == 0) return;
					var time = value.split('-');
					timeFilter(time[0], time[1], 'line');
				}
			});

			laydate.render({
				elem: '#time_pie',
				type: 'date',
				format: 'yyyy/MM/dd',
				range: true,
				value: getLastMonth(),
				done: function (value, date)
				{
					if (value.length == 0)
					{
						layer.alert(lang('请选择时间段！'), { btn: [lang('确定')] });
						return;
					}
					var time = value.split('-');
					timeFilter(time[0], time[1], 'pie');
				}
			});
		}

		function initCharts()
		{
			circle_pie_chart = echarts.init(document.getElementById('circlepie'), 'shine');
			line_chart = echarts.init(document.getElementById('line'), 'shine');
			barWB_chart = echarts.init(document.getElementById('barwb'), 'shine');
			renderCirclePie();
			renderLine();
			renderBarWB();
		}

		function FormatLogData(logs, type)
		{
			userData = [];
			equipData = [];
			kvmData = [];
			kvmOfflineData = [];

			for (var i = 0; i < logs.length; i++)
			{
				if (logs[i].Name == window.CS.ExtraData.SESSION && logs[i].Type == window.CS.ExtraData.SESSION
					&& logs[i].Data == window.CS.BaseDB.USER)
				{
					logs[i].times = (logs[i].Value / 1000 / 60).toFixed(0);
					logs[i].names = logs[i].Data1;
					userData.push(logs[i]);
				}
				if (logs[i].Name == window.CS.ExtraData.TYPE_VIDEO_SWEEPER && logs[i].Type == window.CS.ExtraData.TYPE_VIDEO_SWEEPER
					&& logs[i].Data == window.CS.BaseDB.VIDEO)
				{
					logs[i].times = (logs[i].Value / 1000 / 60).toFixed(0);
					logs[i].names = logs[i].Data1;
					equipData.push(logs[i]);
				}
				if (logs[i].Name == 'Ping' && logs[i].Type == 'Ping'
					&& logs[i].Data == window.CS.BaseDB.VIDEO)
				{
					logs[i].times = (logs[i].Value / 1000 / 60).toFixed(0);
					logs[i].names = logs[i].Data1;
					kvmOfflineData.push(logs[i]);
				}
			}

			var times = 0;
			for (var i = 0; i < kvmOfflineData.length; i++)
			{
				times += Number(kvmOfflineData[i].times);
			}
			kvmData.push({ name: '离线时长', value: times });
			kvmData.push({ name: '在线时长', value: (daysNumber * 24 * 60) - times });

			if (!type)
			{
				renderLine(userData);
				renderBarWB(equipData);
				renderCirclePie(kvmData);
			}
			if (type == 'line') renderLine(userData);
			if (type == 'bar') renderBarWB(equipData);
			if (type == 'pie') renderCirclePie(kvmData);
		}

		function timeFilter(s, e, type)
		{
			daysNumber = getDaysBetween(s, e);
			AjaxApi.request({
				url: "../Log/GetPerformance.dojs",
				data: { start: Date.parse(s), expire: Date.parse(e + ' 23:59:59') },
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					window.timeoffset = Date.now() - res.Now;
					
					updateUIWithData(res);

					FormatLogData(res.Log, type);
				},
				error: function ()
				{
					if ($loadingIndicator.is(':visible'))
					{
						$loadingIndicator.fadeOut(300);
						$main.css('opacity', 1);
					}
				}
			});
		}

		function updateUIWithData(res)
		{
			if (!window.timeIntervalSet)
			{
				setInterval(function ()
				{
					var now = new Date(Date.now() - window.timeoffset);
					now -= window.CS.StartTime;
					$('#timespan').html(
						Math.floor(now / (24 * 60 * 60 * 1000)) + ' 天 ' +
						Math.floor(now % (24 * 60 * 60 * 1000) / (60 * 60 * 1000)) + ' 时 ' +
						Math.floor(now % (60 * 60 * 1000) / (60 * 1000)) + ' 分 '
					);
				}, 1000);
				window.timeIntervalSet = true;
			}

			$('#web_text').html((res.NetBand / 1024 / 8).toFixed(0));
			$('#equip_num').html(res.VideoCount);

			var cpuOffset = 3.1415926 * 140 / 360 * (360 - res.OsProcessor);
			$('#cpu').attr('stroke-dashoffset', cpuOffset);
			$('#cpu_text').html(Math.round(100 - res.OsProcessor) + '%');
		}

		function renderCirclePie(arr)
		{
			if (!circle_pie_chart) return;

			var data = arr || [];

			if (data.length == 0)
			{
				circle_pie_chart.clear();
				circle_pie_chart.setOption(emptyOption);
				return;
			}

			circle_pie_option.series[0].data = data;
			circle_pie_chart.setOption(circle_pie_option);
		}

		function renderLine(arr)
		{
			if (!line_chart) return;

			var data = arr || [];

			if (data.length == 0)
			{
				line_chart.clear();
				line_chart.setOption(emptyOption);
				return;
			}

			var names = data.map(function (item) { return item.names });
			var durations = data.map(function (item) { return item.times });

			if (data.length < 20)
			{
				line_option.dataZoom = [];
			} else
			{
				line_option.dataZoom = [{
					type: 'slider',
					show: true,
					start: 0,
					end: 100,
					height: 20
				}];
			}

			line_option.xAxis.data = names;
			line_option.series[0].data = durations;
			line_chart.setOption(line_option);
		}

		function renderBarWB(arr)
		{
			if (!barWB_chart) return;

			var data = arr || [];

			if (data.length == 0)
			{
				barWB_chart.clear();
				barWB_chart.setOption(emptyOption);
				return;
			}

			var machines = data.map(function (item) { return item.names });
			var durations = data.map(function (item) { return item.times });

			if (data.length < 15)
			{
				barWB_option.dataZoom = [];
			} else
			{
				barWB_option.dataZoom = [{
					type: 'slider',
					show: true,
					start: 0,
					end: 100,
					height: 20
				}];
			}

			barWB_option.xAxis.data = machines;
			barWB_option.series[0].data = durations;
			barWB_chart.setOption(barWB_option);

			if (data.length > 5)
			{
				setupTooltipRotation(data.length);
			}
		}

		function setupTooltipRotation(totalDataCount)
		{
			if (window.tooltipInterval)
			{
				clearInterval(window.tooltipInterval);
			}

			var currentIndex = 0;

			var showTooltip = function ()
			{
				if (!barWB_chart) return;

				barWB_chart.dispatchAction({
					type: 'showTip',
					seriesIndex: 0,
					dataIndex: currentIndex
				});

				currentIndex = (currentIndex + 1) % totalDataCount;
			};

			window.tooltipInterval = setInterval(showTooltip, 2000);

			barWB_chart.on('mouseover', function ()
			{
				clearInterval(window.tooltipInterval);
				window.tooltipInterval = null;
			});

			barWB_chart.on('mouseout', function ()
			{
				if (!window.tooltipInterval)
				{
					window.tooltipInterval = setInterval(showTooltip, 2000);
				}
			});
		}
	</script>
</body>
</html>