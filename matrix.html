﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>机台宫格 — 远程控制中心系统</title>
	<link type="text/css" rel="stylesheet" href="lib/layui/css/layui.css">
	<link type="text/css" rel="stylesheet" href="css/common.css" />
	<link type="text/css" rel="stylesheet" href="../user/loginMenu.docss" />
	<link type="text/css" rel="stylesheet" href="lib/layui/treetable/treetable.css" />
	<style type="text/css">
		.mainBody {
			background-color: #fff; /*gray;*/
			position: absolute;
			height: 100%;
			width: 100%;
			overflow-x: auto;
			display: flex;
			flex-direction: column;
		}

		#sortList {
			display: -ms-flexbox; /* IE10-11 的 Flexbox 前缀 */
			display: flex;
			-ms-flex-wrap: wrap; /* IE10-11 的 Flex-wrap 前缀 */
			flex-wrap: wrap;
			width: 100%;
			height: 100%;
			position: relative;
			overflow-y: auto;
		}

		.mainFrame {
			/*display: flex;
			flex-direction: column;
			width: 100%;
			height: calc(100% - 100px);*/
			background-color: #fff; /*gray;*/
			flex-grow: 1;
			min-width: 800px;
			margin-bottom: 40px;
		}

		.rowFrame {
			background-color: #fff; /*gray;*/
			width: calc(100% - 50px);
			height: calc(33% - 10px);
			padding: 5px 0px 5px 15px;
			display: flex;
			flex-direction: row;
		}

		.cellFrame {
			width: calc(33% - 7px);
			height: 100%;
			min-width: 200px;
			min-height: 160px;
			margin-right: 7px;
			margin-left: 0px;
			display: inline-block;
			border-radius: 10px;
			margin: 5px;
		}

		.canDraggable {
			/*cursor: move;*/
		}

		.alarmcell {
			animation: change_color 0.4s infinite alternate;
		}

		@keyframes change_color {
			0% {
				background-color: white;
			}

			100% {
				background-color: red;
			}
		}

		.redcell {
			background-color: red;
		}

		.greencell {
			background-color: green;
		}

		.yellowcell {
			background-color: yellow;
		}

		.whitecell {
			background-color: white;
		}

		.titlediv {
			background-color: #304156;
			width: 100%;
			height: 25px;
			/*margin: 5px 5px 0px 5px;*/
			margin-top: 5px;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			white-space: nowrap; /* 保持文本在一行内显示 */
			overflow: hidden; /* 隐藏溢出的文本 */
			text-overflow: ellipsis;
		}

		.thumbdiv {
			background-color: #304156;
			background-size: 100% 100%;
			width: 100%;
			height: calc(100% - 55px);
			/*margin: 0px 5px 0px 5px;*/
		}

		.messagediv {
			background-color: #304156;
			width: 100%;
			height: 30px;
			/*margin: 0px 5px 0px 5px;*/
			border-bottom-left-radius: 10px;
			border-bottom-right-radius: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.coverdiv {
			width: calc(100% - 10px);
			height: calc(100% - 10px);
			border-radius: 10px;
			background-color: #304156;
			margin-left: 5px;
		}

		.thumbImg {
			width: 100%;
			height: 100%;
		}

		.titleLabel {
			font-family: 'Microsoft YaHei';
			font-size: 1.2vw;
			font-weight: bold;
			padding-left: 10px;
			line-height: 150%;
			color: darkmagenta;
			min-width: 40px;
			overflow: hidden;
		}

		.captionLabel {
			text-align: center;
			font-weight: bold;
			color: chocolate;
		}

		.iconSpan {
			background-repeat: no-repeat;
			background-size: 100% 100%;
			min-width: 16px;
			min-height: 16px;
			height: 90%;
			cursor: pointer;
			margin-right: 5px;
			/*flex-grow: 0;*/
		}

		.hideSpan {
			display: none !important;
		}

		.showSpan {
			display: inline-block;
		}

		.rpa0 {
			background-image: url(imgs/toolbar/rpa0.png);
			width: 20px !important;
			height: 17px;
		}

		.lock0 {
			background-image: url(imgs/lock0.png);
			width: 20px !important;
			height: 17px;
		}

		.work0 {
			background-image: url(imgs/Work00.png);
			width: 20px !important;
			height: 17px;
		}

		.fullScreenspan {
			background-image: url(imgs/FullScreen.png);
			width: 16px !important;
			height: 16px;
		}

			.fullScreenspan:hover {
				background-image: url(imgs/FullScreen_hover.png);
			}

		.unFullScreenspan {
			background-image: url(imgs/unFullScreen.png);
			width: 20px !important;
			height: 20px;
		}

			.unFullScreenspan:hover {
				background-image: url(imgs/unFullScreen_hover.png);
			}

		.keypadSpan {
			/*border-radius: 10px;
			text-align: center;
			line-height: 150%;
			font-size: 0.9vw;
			font-family: 'Microsoft YaHei';
			float: right;
			width: 10%;*/
			width: 20px !important;
			margin-right: 5px;
			height: 20px;
		}

		.remoteKeypadSpan {
			font-family: 'Microsoft YaHei';
			background-color: aquamarine;
			color: blueviolet;
		}

		.localKeypadSpan {
			font-family: 'Microsoft YaHei';
			background-color: orangered;
			color: white;
		}

		.unknowKeypadSpan {
			font-family: 'Microsoft YaHei';
			background-color: lightgray;
			color: black;
		}

		.lastAlarmSpan {
			background-image: url(imgs/alarm.png);
			width: 20px !important;
			height: 20px;
		}

		.showUpspan {
			background-image: url(imgs/ShowUp.png);
			width: 20px !important;
			height: 20px;
		}

		.footer {
			width: 100%;
			text-align: center;
			background-color: transparent;
			display: flex;
			height: 40px;
			flex-grow: 1;
			/*margin-top: 25px;*/
			position: fixed;
			bottom: 0;
		}

		.messagelabel {
			color: white;
			font-family: 'Microsoft YaHei';
			font-size: 16px;
			height: 100%;
			width: 100%;
			line-height: 27px;
			text-align: center;
			cursor: pointer;
		}

			.messagelabel:hover {
				color: #DDDDDD;
			}

		.footer .layui-laypage {
			flex-grow: 1;
			background-color: #fff; /*gray;*/
			/*padding-top: 6px*/
		}

		.layui-table-page {
			padding-top: 3px;
		}

		.kvmTitleLabel {
			width: 150px;
			text-align: right;
			font-size: 16px;
		}

		.kvmContentSpan {
			font-size: 16px;
			width: 150px;
			display: inline-block;
		}

		/* Tooltip 容器 */
		.tooltip {
			position: relative;
			display: inline-block;
		}

			/* Tooltip 文本 */
			.tooltip .tooltiptext {
				visibility: hidden;
				width: 120px;
				background-color: rgba(0, 0, 0, 0.6);
				color: #fff;
				text-align: center;
				padding: 5px 0;
				border-radius: 6px;
				font-size: 14px;
				/* 定位 */
				position: absolute;
				z-index: 1000;
			}

			/* 鼠标移动上去后显示提示框 */
			.tooltip:hover .tooltiptext {
				visibility: visible;
				z-index: 1000;
			}

		.statusSpan {
			color: white;
			border-radius: 10px;
			text-align: center;
			line-height: 20px;
			font-size: 0.9vw;
			font-family: 'Microsoft YaHei';
			min-width: 30px !important;
			height: 20px;
			margin: 0px 5px;
			overflow: hidden;
		}

		.tooltip .tooltiptext {
			right: -50px;
			top: 130%;
			z-index: 1000;
		}

		.alarmSpan {
			margin-left: 5px;
			background-image: url(imgs/alarm.png);
			max-height: 25px !important;
			height: 25px;
			width: 25px !important;
		}

			.alarmSpan:hover {
				background-image: url(imgs/alarm-hover.png);
			}

		.notepadSpan {
			margin-left: 5px;
			background-image: url(imgs/notepad.png);
			height: 20px;
			width: 20px !important;
		}

			.notepadSpan:hover {
				background-image: url(imgs/notepad-hover.png);
			}

		.kvmSpan {
			background-image: url(imgs/kvm.png);
			max-height: 25px !important;
			height: 25px;
			width: 25px !important;
		}

			.kvmSpan:hover {
				background-image: url(imgs/kvm-hover.png);
			}

		.msgSpan {
			border-radius: 10px;
			text-align: center;
			line-height: 25px;
			font-size: 0.9vw;
			font-family: 'Microsoft YaHei';
			width: 40px !important;
			height: 25px;
		}

		.logSpan {
			background-image: url(imgs/log.png);
			height: 25px !important;
			height: 25px;
			width: 25px !important;
		}

			.logSpan:hover {
				background-image: url(imgs/log-hover.png);
			}

		.switchScreenSpan {
			background-image: url(imgs/log.png);
			max-height: 25px !important;
			height: 25px;
			width: 25px !important;
			font-size: 0.7vw;
			text-align: center;
			line-height: 185%;
		}

		.subScreenSpan {
			background-image: url(imgs/computer2.png);
			color: white;
		}

		.mainScreenSpan {
			background-image: url(imgs/computer.png);
			color: blueviolet;
		}

		.recordSpan {
			background-image: url(imgs/Record.png);
			width: 20px !important;
			height: 17px;
		}

			.recordSpan:hover {
				background-image: url(imgs/Record-hover.png);
			}
	</style>
	<script type="text/javascript">
		window.webName = 'matrix';
		if (window.top == window.self)
			window.location.href = 'index.html';
		else
			window.top.matrix = window;
		if (!!window.parent)
			window.parent.matrix = window;
	</script>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/matrix.js?v=1"></script>
	<script type="text/javascript" src="js/echarts.js?v=1"></script>
	<script type="text/javascript" src="js/request.js"></script>
	<script type="text/javascript" id="document.ready">
		window.ldb('.page', window.webName);
		$(document).ready(function ()
		{
			try
			{
				if (!window["CS"] || !window["CS"]['AssemblyTitle'])
					document.title = lang('机台宫格');
				else
					document.title = lang('机台宫格 — ') + lang(window["CS"]['AssemblyTitle']);
			}
			catch (x) { }
			if (!!window.CS.onready)
				window.CS.onready.call(this, window.$);
		});
	</script>
	<script type="text/javascript">
		var favData = [];
		var laypage;
		var currpagecount = 0;

		var videoData = [];
		var matrixData = [];

		function onlyViewVideo(data, show)
		{
			if (show)
				onlyShowVideo(data)
			else
				onlyHidenVideo(data);
		}
		// 数组去重
		function uniqueArrayObjects(arr, prop)
		{
			var result = [];
			for (var i = 0; i < arr.length; i++)
			{
				var isDuplicate = false;
				for (var j = 0; j < result.length; j++)
				{
					if (result[j][prop] === arr[i][prop])
					{
						isDuplicate = true;
						break;
					}
				}
				if (!isDuplicate)
				{
					result.push(arr[i]);
				}
			}
			return result;
		}
		function onlyHidenVideo(data)
		{
			data.forEach(function (item, index, arr)
			{
				if (checkVideo(item.Type))
				{
					videoData = videoData.filter(function (subitem, index, arr) { return subitem.ID != item.ID });
				}
			});

			renderLayPage(favData.concat(videoData));
		}

		function onlyShowVideo(data)
		{
			videoData = []
			favData = []
			data.forEach(function (item, index, arr)
			{
				if (checkVideo(item.Type))
				{
					if (favData.length == 0)
						videoData.push(item);
					else
					{
						if (favData.filter(function (sub, index, arr) { return sub.ID == item.ID }).length == 0)
							videoData.push(item);
					}
				}
			});

			videoData = uniqueArrayObjects(videoData, 'id')
			//renderLayPage(favData.concat(videoData));
			for (var i = 0; i < videoData.length; i++)
			{
				videoData[i].Position = i;
			}
			renderLayPage(videoData)
		}

		function renderLayPage(data)
		{
			var gridcount = window.top.window.login.GridCount;
			matrixData = data;
			laypage.render({
				elem: 'footer'
				, count: data.length
				, limit: gridcount
				, prev: '上一页'
				, next: '下一页'
				, layout: ['count', 'prev', 'page', 'next', 'skip']
				, curr: currpagecount
				, jump: function (obj, first)
				{
					currpagecount = obj.curr;

					for (var i = 0; i < gridcount; i++)
					{
						removeCSS(i);
					}

					if (data.length == 0)
						return;

					matrixData = []; //更新当前页数据
					for (var i = (obj.curr - 1) * gridcount; i < data.length; i++)
					{
						if (i == obj.curr * gridcount)
							break;

						addCSS(i % gridcount, data[i]);
						matrixData.push(data[i]);
					}
				}
			});
			
			if (!!data.length)
			{
				for (var i = matrixData.length - 1; i >= 0; i--)
				{
					if (!!matrixData[i].SweepData)
						onMessageUrl(new Uri(matrixData[i].SweepData));
					if (!!matrixData[i].LockData)
						onMessageUrl(new Uri(matrixData[i].LockData));
					if (!!matrixData[i].PilotData)
						onMessageUrl(new Uri(matrixData[i].PilotData));
				}
			}
		}

		function operationVideo(data, is_show, is_fav)
		{
			UpdateToolPosition(data, is_show);
		}

		function UpdateToolPosition(data, is_show)
		{

			var gdata = { ids: '', multis: '' };

			var name = '';

			var gridcount = window.top.window.login.GridCount;
			var row = window.top.window.login.Row;

			if (is_show)
			{
				favData = favData.concat(data);

				for (var i = favData.length - data.length; i < favData.length; i++)
				{
					gdata.multis = ',' + i;
					gdata.ids = ',' + favData[i].ID;
					name += favData[i].Name + ',';
				}
			} else
			{
				for (var i = 0; i < data.length; i++)
				{
					gdata.multis += ',' + '&';
					gdata.ids = ',' + data[i].ID;
					name += data[i].Name + ',';

					favData = favData.filter(function (item, index, arr) { return item.ID != data[i].ID });
				}

				favData.forEach(function (item, index, arr)
				{
					gdata.multis += ',' + index;
					gdata.ids += ',' + favData[index].ID;
				});
			}
			AjaxApi.request({
				url: "Tool/UpdateToolPosition.do",
				data: gdata,
				dataType: 'text',
				success: function (result)
				{
					if (is_show)
						layer.msg('已显示 ' + name);
					else
						layer.msg('已隐藏 ' + name);

					getFavoritesTool();
				}
			})
		}

		function removeCSS(i)
		{
			$('#cell-' + i).data('video', null);
			$('#rpa-' + i).attr('name', '');
			$('#lock-' + i).attr('name', '');
			$('#open-' + i).attr('name', '');
			$('.titlediv:eq(' + i + ')').css('background-color', '#304156');
			$('.thumbdiv:eq(' + i + ')').css('background-image', '');
			$('.thumbImg:eq(' + i + ')').css('display', 'none');
			$('.thumbImg:eq(' + i + ')').attr('src', '');
			$('.messagediv:eq(' + i + ')').css('background-color', '#304156');
			$('#title-' + i).html('');
			$('#title-' + i).attr('title', '');
			$('#caption-' + i).html('');
			$('.thumbdiv:eq(' + i + ')').attr('name', '');
			$('.messagediv:eq(' + i + ')').children('span').removeClass('showSpan').addClass('hideSpan');
			$('.messagediv:eq(' + i + ')').children('span').attr('name', '');
			$('.messagediv:eq(' + i + ')').children('span').data('video', null);
			$('.titlediv:eq(' + i + ') .iconGroup').children('span').removeClass('showSpan').addClass('hideSpan');
			$('.titlediv:eq(' + i + ') .iconGroup').children('span').attr('name', '');
			$('.titlediv:eq(' + i + ') .iconGroup').children('span').data('video', null);
			$('.thumbdiv:eq(' + i + ')').data('video', null);
			$('.recordSpan:eq(' + i + ')').removeClass('showSpan').addClass('hideSpan');
			$('.keypadSpan:eq(' + i + ')').removeClass('localKeypadSpan').removeClass('remoteKeypadSpan').removeClass('showSpan').addClass('hideSpan');
			$('.keypadSpan:eq(' + i + ')').attr('name', '');
			$('.keypadSpan:eq(' + i + ')').attr('title', '-');
			$('.keypadSpan:eq(' + i + ')').attr('src', 'imgs/icon-none.png');
			$('.cellFrame:eq(' + i + ')').removeClass('canDraggable');
		}

		function addCSS(i, item)
		{
			if (!item)
				return;

			item.gridIndex = i;
			if (!item.url)
				item.url = new Uri(item.Url);
			$('#cell-' + i).data('video', item);
			$('#lock-' + i).attr('name', item.ID);
			$('#lock-' + i).data('video', item);
			onMessageUrl(new Uri(item.LockData));
			$('#rpa-' + i).attr('name', item.ID);
			$('#rpa-' + i).data('video', item);
			$('#work-' + i).attr('name', item.ID);
			$('#work-' + i).data('video', item);
			onMessageUrl(new Uri(item.SweepData));
			$('#open-' + i).attr('name', item.ID);
			$('.titlediv:eq(' + i + ')').css('background-color', 'lightskyblue');
			$('.messagediv:eq(' + i + ')').css('background-color', 'lightskyblue');
			$('.thumbdiv:eq(' + i + ')').attr('name', item.ID);
			$('.thumbdiv:eq(' + i + ')').data('video', item);
			$('.thumbdiv:eq(' + i + ')').css('background-image', 'url(imgs/noSign.jpg)');
			$('.thumbImg:eq(' + i + ')').attr('src', 'imgs/connecting.jpg');
			$('.thumbImg:eq(' + i + ')').css('display', 'block');
			$('.messagediv:eq(' + i + ')').children('span').removeClass('hideSpan').addClass('showSpan');
			$('.messagediv:eq(' + i + ')').children('span').attr('name', item.ID);
			$('.messagediv:eq(' + i + ')').children('span').data('video', item);
			$('.titlediv:eq(' + i + ') .iconGroup').children('span').removeClass('hideSpan').addClass('showSpan');
			$('.titlediv:eq(' + i + ') .iconGroup').children('span').attr('name', item.ID);
			$('.titlediv:eq(' + i + ') .iconGroup').children('span').data('video', item);
			$('#title-' + i).html(item.Name);
			$('#title-' + i).attr('title', item.UrlAuthority);

			if (!!item.SlaveVideo)
			{
				$('.switchScreenSpan:eq(' + i + ')').removeClass('hideSpan').addClass('showSpan');
				$('.switchScreenSpan:eq(' + i + ')').data('video', item);
			}
			else
			{
				$('.switchScreenSpan:eq(' + i + ')').removeClass('showSpan').addClass('hideSpan');
			}

			$('.recordSpan:eq(' + i + ')').removeClass('hideSpan').addClass('showSpan');
			$('.recordSpan:eq(' + i + ')').data('video', item);

			$('.keypadSpan:eq(' + i + ')').removeClass('hideSpan').addClass('showSpan');
			$('.keypadSpan:eq(' + i + ')').attr('name', item.ID + 'keypad')

			$('.lastAlarmSpan:eq(' + i + ')').removeClass('showSpan').addClass('hideSpan');
			$('.lastAlarmSpan:eq(' + i + ')').attr('name', item.ID + 'lastalarm');
			$('.kvmSpan:eq(' + i + ')').removeClass('showSpan').addClass('hideSpan');

			if (item.Type != CS.VideoData.TypeKVM)
			{
				$('.keypadSpan:eq(' + i + ')').removeClass('showSpan').addClass('hideSpan');
			}
			resetToolImg(i);
		}

		// 翻页重置当前toolbar img状态
		function resetToolImg(i)
		{
			$('#work-' + i).css('background-image', 'url(imgs/Work00.png)');
			$('#work-' + i).attr('title', lang('无人操作'));
			if (!!window.CS.showWorker)
				$('#caption-' + i).html('');

			$('#lock-' + i).css('background-image', 'url(imgs/lock0.png)');
			$('#lock-' + i).attr('title', lang('机台未锁定（点击可锁定）'));

			$('#rpa-' + i).css('background-image', 'url(imgs/toolbar/rpa0.png)');
			$('#rpa-' + i).attr('title', lang('启动RPA'));
			$('#rpa-' + i).data('rpa', null);
		}

		function showVideo(showData)
		{
			var gridcount = window.top.window.login.GridCount;
			var row = window.top.window.login.Row;

			if (showData.length > 0)
			{
				showData.forEach(function (item, index, arr)
				{
					favData.push(item);

					item.Multi = item.ID + item.Position;

					var index = favData.length - 1;

					if (index > gridcount)
					{
						return false;
					}

					addCSS(index, item);
				});
			}
		}

		function hideVideo(hideData)
		{
			var gridcount = window.top.window.login.GridCount;

			if (hideData.length > 0)
			{
				var deleteIndex = [];

				hideData.forEach(function (item, index, arr)
				{
					var arr_index = favData.indexOf(function (subItem, index, array)
					{
						return subItem.ID == item.name;
					});
					if (arr_index == -1)
						return false;

					deleteIndex.push(arr_index);

					if (arr_index > gridcount)
					{
						return false;
					}

					removeCSS(arr_index);
				});

				deleteIndex.forEach(function (item, index, arr)
				{
					favData.splice(item - index, 1);
				});

				if (favData.length > 0)
				{
					favData.forEach(function (item, index, arr)
					{
						if (index > gridcount)
						{
							return false;
						}

						addCSS(index, item);
					});

					if (favData.length < gridcount)
					{
						for (var i = favData.length; i < gridcount; i++)
						{
							removeCSS(i);
						}
					}
				}
			}
		}

		//search: string, id, ip
		//val: string "0": remote "1": local "2": none control
		function onMessageUrl(host, path, search, scheme, url)
		{
			if (!arguments.length)
				return;

			if (!!host.isEmpty)
			{
				host.toLower();
				url = host.Href;
				scheme = host.scheme;
				search = host.Search;
				path = host.Path;
				host = host.Host;
			}

			if (!scheme)
				return;
			
			for (var i = matrixData.length - 1; i >= 0; i--)
			{
				var v = matrixData[i];
				if (host.length == 32) //id
				{
					if (v.id != host && v.MultiID != host)
						continue;
				}
				else
				{
					if (v.Url.indexOf(host) < 0)
						continue;
				}
				if (scheme == 'lock')
				{
					if (!path || path == '' || path == '/')
					{
						$('#lock-' + i).css('background-image', 'url(imgs/lock0.png)');
						$('#lock-' + i).attr('title', lang('机台未锁定（点击可锁定）'));
						continue;
					}

					$('#lock-' + i).css('background-image', 'url(imgs/lock1.png)');
					$('#lock-' + i).attr('title', lang('机台已锁定（点击可解锁）：') + path.substring(path.lastIndexOf('/') + 1, path.length));
					continue;
				}

				if (scheme == 'sweep')
				{
					if (!window.doRPA)
					{
						$('#rpa-' + i).remove();
					}
					else
					{
						if (!!search && search.indexOf('T' + window.doRPA()) > 0)
						{
							$('#rpa-' + i).css('background-image', 'url(imgs/toolbar/rpa2.png)');
							$('#rpa-' + i).attr('title', lang('停止RPA'));
						}
						else
						{
							$('#rpa-' + i).css('background-image', 'url(imgs/toolbar/rpa0.png)');
							$('#rpa-' + i).attr('title', lang('启动RPA'));
						}
						$('#rpa-' + i).data('rpa', search);
					}

					if (!search)//Count=2&1AAD9DE9117271E8822C9AD175F04926=1729231516661/1/S1AAD9DE3EDD68EC99B6A53548DDB38D9/1/U1AACD3DBFA6994C785E984A7CE25FF7E/1/Tuser&1AAD9DCE02DAE0E8ABA3ECEE1CA52DA0=1729231516289/0/S1AAD9DA310F2B9BF8A7E8900B904927C/0/U1AACD3D4B400C591A17BB598B99200B3/0/Thsj
					{
						$('#work-' + i).css('background-image', 'url(imgs/Work00.png)');
						$('#work-' + i).attr('title', lang('无人操作'));
						if (!!window.CS.showWorker)
							$('#caption-' + i).html('');
						continue;
					}
					if (search.indexOf('/2/') > 0)// id=/2/ &&&&&&& id=/1/
					{
						if (search.indexOf('/1/') > 0)
						{
							$('#work-' + i).css('background-image', 'url(imgs/Work30.png)');// id=/2/&id=/1/
							$('#work-' + i).attr('title', lang('有人操作，有人等待'));
							if (!!window.CS.showWorker)
							{
								$('#caption-' + i).css('color', 'chocolate');
								$('#caption-' + i).html(search.replace(/.+\/1\/T([^\/&]+)([\/&].+)?/img, '$1'));
							}
							continue;
						}

						$('#work-' + i).css('background-image', 'url(imgs/Work20.png)');// id=/2/
						$('#work-' + i).attr('title', lang('有人操作'));
						if (!!window.CS.showWorker)
						{
							$('#caption-' + i).css('color', 'darkgreen');
							$('#caption-' + i).html(search.replace(/.+\/2\/T([^\/&]+)([\/&].+)?/img, '$1'));
						}
						continue;
					}

					if (search.indexOf('/1/') > 0)
					{
						$('#work-' + i).css('background-image', 'url(imgs/Work10.png)');// id=/1/
						$('#work-' + i).attr('title', lang('有人等待'));
						if (!!window.CS.showWorker)
						{
							$('#caption-' + i).css('color', 'red');
							$('#caption-' + i).html(search.replace(/.+\/1\/T([^\/&]+)([\/&].+)?/img, '$1'));
						}
						continue;
					}

					if (search.indexOf('/0/') > 0)
					{
						$('#work-' + i).css('background-image', 'url(imgs/Work-0.png)');// id=/0/
						$('#work-' + i).attr('title', lang('有人观看'));
						if (!!window.CS.showWorker)
						{
							$('#caption-' + i).css('color', 'gray');
							$('#caption-' + i).html(search.replace(/.+\/0\/T([^\/&]+)([\/&].+)?/img, '$1'));
						}
						continue;
					}

					$('#work-' + i).css('background-image', 'url(imgs/Work00.png)');
					$('#work-' + i).attr('title', lang('无人操作'));
					if (!!window.CS.showWorker)
						$('#caption-' + i).html('');
					continue;
				}

				if (scheme != 'pilot')
					continue;

				if (path == "0")
				{
					$('#keypad-' + i).attr('src', 'imgs/icon-far.png');
					$('#keypad-' + i).attr('title', lang('当前为远端'));
					continue;
				}

				if (path == "1")
				{
					$('#keypad-' + i).attr('src', 'imgs/icon-near.png');
					$('#keypad-' + i).attr('title', lang('当前为近端'));
					continue;
				}

				if (path == "2")
				{
					$('#keypad-' + i).attr('src', 'imgs/icon-none.png');
					$('#keypad-' + i).attr('title', lang('当前无人操作'));
					continue;
				}
			}
		}

		(function ()
		{
			window.frameWS = new WebSocket('ws://' + location.host + '/Frame/Matrix?timeout=5');
			window.frameWS.binaryType = 'blob';//'arraybuffer';//
			window.frameWS.onopen = function (e)
			{
				console.log(e.target.url + ' open.');
			};
			window.frameWS.onerror = function (e)
			{
				console.log(e.target.url + ' error.');
			};
			window.frameWS.onmessage = function (e)
			{
				if (arguments.length > 1 && e == window)
				{
					var us = {};
					var src = arguments[1];
					for (var i = 0; i < window.top.window.login.GridCount; i++)
					{
						var v = $('.thumbdiv:eq(' + i + ')').data('video');
						if (!!v && !!v.UrlAuthority && v.UrlAuthority.replace(/:.*$/img, '') == window.frameImg)
						{
							var t = $('.thumbImg:eq(' + i + ')').attr('src');
							if (!t)
							{
								$('.thumbImg:eq(' + i + ')').attr('src', src);
								continue;
							}

							if (t == src)
								continue;

							if (/^blob:/img.test(t))
								us[t] = src;
							$('.thumbImg:eq(' + i + ')').attr('src', src);
						}
					}
					for (var i in us)
					{
						if (us[i] == src)
							URL.revokeObjectURL(i);
					}
					return;
				}

				var data = e.data;
				if (!!data.indexOf && (!data.size || data.size < 8192))
				{
					if (data[0] == '{')
					{
						try
						{
							window.frameVideo = $.eval(data);
						}
						catch (x)
						{
							return;
						}

						window.ldb('#' + window.frameVideo.ID, data);
						for (var i = 0; i < window.top.window.login.GridCount; i++)
						{
							var v = $('.thumbdiv:eq(' + i + ')').data('video');
							if (!!v && v.ID == window.frameVideo.ID)
							{
								v.Url = window.frameVideo.Url;
								v.SweepData = window.frameVideo.SweepData;
								v.LockData = window.frameVideo.LockData;
							}
						}
						if (!!window.frameVideo.SweepData)
							onMessageUrl(new Uri(window.frameVideo.SweepData));
						if (!!window.frameVideo.LockData)
							onMessageUrl(new Uri(window.frameVideo.LockData));
						if (!!window.frameVideo.PilotData)
							onMessageUrl(new Uri(window.frameVideo.PilotData));
						return;
					}

					var i = data.indexOf('://');
					window.frameImg = data.substring(i + 3, data.indexOf('/', i + 3));
					if (data.indexOf('gone://') == 0)
					{
						data = 'imgs/404.jpg';
						arguments.callee.call(this, window, data, e);
						return;
					}

					if (data.indexOf('lost://') == 0)
					{
						data = 'imgs/noSign.jpg';
						arguments.callee.call(this, window, data, e);
						return;
					}

					if (data.indexOf('try://') == 0)
					{
						data = 'imgs/connecting.jpg';
						arguments.callee.call(this, window, data, e);
						return;
					}

					return;
				}

				data = URL.createObjectURL(data);
				arguments.callee.call(this, window, data, e);
			};
			window.frameWS.onclose = arguments.callee;
		}).call(window);

		setInterval(function ()
		{
			if (!window.top.window.login || !window.top.window.login.ID)
				return;

			if (!window.frameWS || window.frameWS.readyState != WebSocket.OPEN)
				return;

			var d;
			if (window.location.port == window.RCS.WebPoint)
				d = 'ws://' + window.location.host + '/Frame/JobWorker?JobWorker=' + window.RCS.ConfigData.JobWorker + '&ClientWorker=' + window.RCS.ConfigData.ClientWorker + '&User=' + window.top.window.login.ID;
			if (!!fullScreenVideo)
			{
				var v = fullScreenVideo;
				if (v.Master != undefined && !v.Master)
					v = fullScreenVideo.SlaveVideo;
				if (!window.CS['image' + v.url.Scheme])
					window.frameWS.send('image://' + v.UrlAuthority.replace(/:.*$/img, ''));
				else
					$('.thumbImg:eq(' + v.gridIndex + ')').attr('src', window.CS['image' + v.url.Scheme].replace('{ip}', v.UrlAuthority.replace(/:.*$/img, '')).replace('{now}', Date.now()));
				if (!!d && !!window.Data)
					window.Data('Job', d + '&' + v.ID + '=' + encodeURIComponent(v.Url));
				return;
			}

			if (window.top.window.login.GridCount == 1)
			{
				var v = $('#viewer-0').data('video');
				if (!window.CS['image' + v.url.Scheme])
					window.frameWS.send('image://' + v.UrlAuthority.replace(/:.*$/img, ''));
				else
					$('.thumbImg:eq(' + v.gridIndex + ')').attr('src', window.CS['image' + v.url.Scheme].replace('{ip}', v.UrlAuthority.replace(/:.*$/img, '')).replace('{now}', Date.now()));
				if (!!d && !!window.Data)
					window.Data('Job', d + '&' + v.ID + '=' + encodeURIComponent(v.Url));
				return;
			}

			var t = '';
			for (var i = 0; i < window.top.window.login.GridCount; i++)
			{
				var v = $('.thumbdiv:eq(' + i + ')').data('video');
				if (!v || !v.UrlAuthority)
					continue;

				if (!window.CS['thumb' + v.url.Scheme])
					t = t + 'thumb://' + v.UrlAuthority.replace(/:.*$/img, '') + ';';
				else
					$('.thumbImg:eq(' + i + ')').attr('src', window.CS['thumb' + v.url.Scheme].replace('{ip}', v.UrlAuthority.replace(/:.*$/img, '')).replace('{now}', Date.now()));
				if (!!d && !!window.Data)
					d = d + '&' + v.ID + '=' + encodeURIComponent(v.Url);
			}
			if (!!t)
				window.frameWS.send(t);
			if (!!d && !!window.Data)
				window.Data('Job', d);
		}, 1000);

		function play(page, data)
		{
			var gridcount = window.top.window.login.GridCount;
			var row = window.top.window.login.Row;

			for (var i = 0; i < data.length; i++)
			{
				var index = data[i].Position;

				if (gridcount * (page - 1) <= index && index < gridcount * page)
					addCSS(index % gridcount, data[i]);
			}
		}
		// 获取收藏夹设备
		function getFavoritesTool()
		{
			favData = window.top.window.favorites;
			if (!favData)
			{
				getIframeMessage();
			}
			if (favData) renderLayPage(window.top.window.favorites);
		}

		function initGrid()
		{
			AjaxApi.request({
				url: "User/GetUserGrid.do",
				data: { ID: window.top.window.login.ID },
				dataType: 'text',
				success: function (result)
				{
					var res = $.eval(result);
					if (!!res.data)
					{
						$.parseUrl(res.data.Theme, window.top.window.login);
						currpagecount = 0;
						var gridCount = 9;
						var columnCount = 3;
						var rowCount = 3;
						if (!!window.top.window.login.Grid)
						{
							window.top.window.login.Row = parseInt(window.top.window.login.Grid[0]);
							window.top.window.login.Column = parseInt(window.top.window.login.Grid[1]);
							window.top.window.login.GridCount = window.top.window.login.Row * window.top.window.login.Column;

							gridCount = window.top.window.login.GridCount;
							columnCount = window.top.window.login.Column;
							rowCount = window.top.window.login.Row;
						}
						else
						{
							window.top.window.login.Row = 3;
							window.top.window.login.Column = 3;
							window.top.window.login.GridCount = 9;
						}
						var grid = '';
						for (var i = 0; i < rowCount * columnCount; i++)
							grid = grid + '<div id="cell-' + i + '" class="layui-anim layui-anim-scale cellFrame whitecell" data-id="default">\
			<div class="coverdiv">\
				<div id="title-bar-' + i + '" class="titlediv">\
					<p id="title-' + i + '" class="titleLabel"></p>\
					<span class="iconSpan hideSpan statusSpan"></span>\
					<div class="iconGroup" style="display: flex; align-items: center;">\
					<label id="caption-' + i + '" class="captionLabel" style="flex-grow: 1;"></label>\
					<span class="iconSpan lastAlarmSpan hideSpan tooltip" onclick="onlastAlarm(this)" title="最近报警"></span>\
					<img id="keypad-' + i + '" class="iconSpan keypadSpan hideSpan" title="-" src="imgs/icon-none.png" />\
					<!-- <span class="iconSpan switchScreenSpan mainScreenSpan hideSpan tooltip" onclick="onSwitchMainSub(this)" title="切换为2屏">1</span> -->\
					<span class="iconSpan recordSpan hideSpan tooltip" onclick="onRecord(this)" title="录屏回放"></span>\
					<span id="rpa-' + i + '" class="iconSpan rpa0 hideSpan tooltip god-a-s" onclick="javascript: doRPA.call(this, $(this).attr(\'name\'), -1, 0)" title="RPA管理"></span>\
					<span id="lock-' + i + '" class="iconSpan lock0 hideSpan tooltip god-a-vl" onclick="javascript: doLockTool.call(this, $(this).attr(\'name\'), -1, 0)" title="锁定机台"></span>\
					<span id="work-' + i + '" class="iconSpan work0 hideSpan tooltip god-a-v" onclick="javascript: doWork.call(this, $(this).attr(\'name\'), -1, 0)" title="管理操作员"></span>\
					<span id="open-' + i + '" class="iconSpan showUpspan hideSpan tooltip" onclick="onShowBtnClick(this)" title="放大操作"></span>\
					<span class="iconSpan fullScreenspan hideSpan tooltip" onclick="onFullScreenClick(this)" title="放大查看"></span></div>\
				</div>\
				<div id="viewer-' + i + '" class="thumbdiv" ondblclick="onCellDblclick(this)">\
					<img id="img-' + i + '" class="thumbImg" alt=""/>\
				</div>\
				<div id="status-bar-' + i + '" class="messagediv">\
					<span class="iconSpan alarmSpan tooltip hideSpan god-e-mi" onclick="onAlarm(this)" title="机台报警"></span>\
					<span class="iconSpan notepadSpan tooltip hideSpan" onclick="onNotepad(this)" title="共享记事本"></span>\
					<span class="iconSpan logSpan tooltip hideSpan god-e-mi" onclick="onToolMsg(this)" title="机台信息"></span>\
					<span class="iconSpan kvmSpan tooltip hideSpan god-e-mi" onclick="onKVMMsg(this)" title="KVM信息"></span>\
					<span class="iconSpan msgSpan tooltip hideSpan god-e-mi" style="background-color:brown; color:aliceblue; border-radius: 10px;" title="DI信息">DI</span>\
					<span class="iconSpan msgSpan tooltip hideSpan god-e-mi" style="background-color:mediumpurple; color:yellow; border-radius: 10px;" title="DT信息">DT</span>\
					<span class="iconSpan msgSpan tooltip hideSpan god-e-mi" style="background-color:darkgoldenrod; color:palegreen; border-radius: 10px;" title="SC信息">SC</span>\
					<span class="iconSpan msgSpan tooltip hideSpan god-e-mi" style="background-color:mediumaquamarine; color:aliceblue; border-radius: 10px;" title="PI信息">PI</span>\
					<span class="iconSpan msgSpan tooltip hideSpan god-e-mi" style="background-color:indigo; color:aliceblue; border-radius: 10px;" title="IT信息">IT</span>\
					<span class="iconSpan msgSpan tooltip hideSpan god-e-mi" style="background-color:tomato; color:aliceblue; border-radius: 10px;" title="FD信息">FD</span>\
				</div>\
			</div>\
</div>';
						grid += '<div class="footer" id="footer"></div>';
						if (window.top.window.login.Column > 3)
						{
							$('#sortList').css('min-width', '1280px');
						} else
						{
							$('#sortList').css('min-width', '800px');
						}
						/*var singleNum = (window.innerHeight - 60) / rowCount;
						var cellFrameHeight = (singleNum < 160) ? 160 : singleNum;
						$(".mainFrame").css('height', parseInt(cellFrameHeight * rowCount)  + 'px');*/
						// $('.mainFrame').css('height', 'calc(100% - ' + parseInt(60 + gridCount + 10) + 'px) ');
						$('.mainFrame').html(grid);
						if (!!window.godCSS)
							window.godCSS.call();
						if (!!window.CS.localRecording)
							$('.recordSpan').remove();
						if (!window.Data)
							$('.showUpspan').remove();
						setLayout(rowCount, columnCount)
						getFavoritesTool();
						// cellDrag()
					}
				}
			})
		}
		// 拖拽完成后获取node排序结果
		function sortAccordingToArr(arr, arrObj)
		{
			var start = arrObj.length;
			var end = 0;

			// 找到需要更改顺序的数据范围
			for (var i = 0; i < arrObj.length; i++)
			{
				var e = arrObj[i];
				var index = arr.indexOf(e.ID);
				if (index !== -1)
				{
					if (i < start)
					{
						start = i;
					}
					if (i > end)
					{
						end = i;
					}
				}
			}

			// 保持开始和结束范围
			var first = arrObj.slice(0, start);
			var middle = arrObj.slice(start, end + 1);
			var last = arrObj.slice(end + 1);

			// 根据 arr 来排序 middle 中的数据
			middle.sort(function (a, b)
			{
				return arr.indexOf(a.ID) - arr.indexOf(b.ID);
			});

			// 重新设置 position
			var result = first.concat(middle, last);
			for (var i = 0; i < result.length; i++)
			{
				result[i].Position = i;
			}

			return result;
		}
		// 拖拽
		function cellDrag()
		{
			// 获取容器和所有cellFrame元素
			var container = document.getElementById('sortList');
			var items = container.querySelectorAll('.canDraggable');
			var currentDraggedItem = null;
			var placeholder = null;

			function dragStartHandler(e)
			{
				e.dataTransfer.setData('text', '');
				currentDraggedItem = this;
				items = container.querySelectorAll('.canDraggable');
				if (!placeholder)
				{
					placeholder = document.createElement('div');
					placeholder.classList.add('placeholder');
					container.appendChild(placeholder);
				}
			}
			// 拖拽开始时的事件处理
			for (var i = 0; i < items.length; i++)
			{
				items[i].addEventListener('dragstart', dragStartHandler, false);
			}

			// 拖拽进行中的事件处理
			function dragOverHandler(e)
			{
				e.preventDefault();
				if (!currentDraggedItem) return;
			}
			document.addEventListener('dragover', dragOverHandler, false);

			// 拖拽结束时的事件处理
			function dragEndHandler(e)
			{
				items = container.querySelectorAll('.canDraggable'); // 获取最新items
				var newData = videoData.concat(favData);
				newData = uniqueArrayObjects(newData, 'ID');
				var sortData = [];
				//获取拖动后的排序 重新排列宫格数据
				var dataIds = []; // 创建一个数组来存储所有的 data-id 值
				for (var i = 0; i < items.length; i++)
				{
					var dataId = items[i].getAttribute('data-id');
					dataIds.push(dataId);
				}
				// 根据arr数组中的id顺序对arrObj进行排序
				sortData = sortAccordingToArr(dataIds, newData);
				favData = sortData;
				renderLayPage(sortData);
				// console.log(dataIds, newData, sortData);
			}
			document.addEventListener('dragend', dragEndHandler, false);

			// 放置元素的逻辑
			function dragHandler(e)
			{
				e.preventDefault();
				if (!currentDraggedItem) return;
				var targetIndex = getDropTargetIndex(e.clientX, e.clientY);
				// 检查拖动元素是从后往前还是从前往后
				var isMovingForward = false; // 默认设置为 false

				// 使用 for 循环查找 currentDraggedItem 在 items 中的索引
				for (var i = 0; i < items.length; i++)
				{
					if (items[i] === currentDraggedItem)
					{
						isMovingForward = i < targetIndex;
						break;
					}
				}

				if (targetIndex !== -1)
				{
					var newPlaceholderPosition = (targetIndex === items.length - 1 || isMovingForward) ? 'afterend' : 'beforebegin';
					if (newPlaceholderPosition === 'beforebegin')
					{
						container.insertBefore(currentDraggedItem, items[targetIndex])
					} else
					{
						if (!items[targetIndex].nextElementSibling)
						{ // 如果目标元素是最后一个元素
							container.appendChild(currentDraggedItem); // 将当前拖动元素插入到容器的末尾
						} else
						{
							container.insertBefore(currentDraggedItem, items[targetIndex].nextElementSibling); // 否则就将当前拖动元素插入到目标元素的后面
						}
					}
				}
				currentDraggedItem = null;
				if (placeholder)
				{
					container.removeChild(placeholder);
					placeholder = null;
				}
				items = container.querySelectorAll('.canDraggable');
			}
			container.addEventListener('drop', dragHandler);

			function getDropTargetIndex(clientX, clientY)
			{
				var boundingRects = [];
				for (var i = 0; i < items.length; i++)
				{
					var rect = items[i].getBoundingClientRect();
					boundingRects.push({
						index: i,
						top: rect.top,
						left: rect.left,
						right: rect.left + rect.width, // 添加元素右边界
						bottom: rect.bottom
					});
				}

				for (var i = 0; i < boundingRects.length; i++)
				{
					var rect = boundingRects[i];
					// 检查鼠标是否在元素的垂直中心和水平中心之间
					if (clientY >= rect.top && clientY <= rect.bottom &&
						clientX >= rect.left && clientX <= rect.right)
					{
						return rect.index;
					}
				}

				// 如果不在已有元素中间，则根据鼠标位置确定插入到第一个或最后一个元素的位置
				var lastItem = boundingRects[boundingRects.length - 1];
				if (clientY >= lastItem.top && clientY <= lastItem.bottom)
				{
					return items.length - 1;
				}
				// 处理鼠标在第一个元素的左边时的情况
				var firstItem = boundingRects[0];
				if (clientX < firstItem.left)
				{
					return 0;
				}
				// 默认添加到末尾
				return items.length;
			}
		}

		// 设置row * col布局
		function setLayout(rows, cols)
		{
			var container = document.querySelector('#sortList');

			var items = container.querySelectorAll('.cellFrame');
			var totalItems = items.length;

			// 为每个子元素设置样式
			for (var i = 0; i < totalItems; i++)
			{
				var item = items[i];
				var colWidth = 100 / cols;
				var rowHeight = 100 / rows;
				item.style.width = 'calc(' + colWidth + '% - 10px)';
				if (rows > 4)
				{
					item.style.height = 'calc(' + rowHeight + '% - 0px)';
				} else
				{
					var minus = rows > 2 ? 15 : 20;
					item.style.height = 'calc(' + rowHeight + '% - ' + minus + 'px)';
				}
			}
		}

		function getIframeMessage()
		{
			window.addEventListener('message', function (event)
			{
				/*if (event.origin !== "http://127.0.0.1:7822") {
					return; // 只接受来自信任来源的消息
				}*/
				if (event.data === 'parentLoaded')
				{
					// 父页面加载完成后执行的代码
					initGrid()
				}
			});
		}

		layui.config({ base: 'lib/layui/' })
			.extend({ treetable: 'treetable/treetable' })
			.use(['element', 'layer', 'jquery', 'form', 'laypage', 'laydate', 'table', 'echarts', 'treetable'], function ()
			{
				$ = layui.$
					, laydate = layui.laydate
					, transfer = layui.transfer
					, form = layui.form
					, table = layui.table
					, layer = layui.layer
					, laydate = layui.laydate
					, element = layui.element
					, echarts = layui.echarts
					, treetable = layui.treetable;

				laypage = layui.laypage;

				initGrid();

				laydate.render({
					elem: '#alarmDate',
					type: 'datetime',
					range: true,
					done: function (value, date, endDate)
					{
						$('#alarmDate').data('date', { startdate: date, enddate: endDate });
					}
				});

				laydate.render({
					elem: '#recordingDate',
					format: "yyyy-MM-dd",
					type: 'date',
					range: true,
					done: function (value, date, endDate)
					{
						$('#recordingDate').data('date', { startdate: date, enddate: endDate });
					}
				});

				laydate.render({
					elem: '#msgDate',
					format: "yyyy-MM-dd",
					type: 'datetime',
					range: true,
					done: function (value, date, endDate)
					{
						$('#msgDate').data('date', { startdate: date, enddate: endDate });
					}
				});

				element.on('tab(tab)', function (data)
				{
					if (data.index == 0)
					{
						onKVMVideoInfoShow();
					} else if (data.index == 1)
					{
						onKVMDeviceInfoShow();
					} else if (data.index == 2)
					{
						onKVMResourceInfoShow();
					}
				});

				onKVMVideoInfoShow();

				form.on('submit(recordingSearch)', function (obj)
				{
					var date = $('#recordingDate').data('date');
					if (!!date)
					{
						var videoData = $('#recordingSearch').data('video');

						var start_date = new Date(date.startdate.year,
							date.startdate.month - 1,
							date.startdate.date,
							date.startdate.hours,
							date.startdate.minutes,
							date.startdate.seconds).getTime();
						var end_date = new Date(date.enddate.year,
							date.enddate.month - 1,
							date.enddate.date + 1,
							date.enddate.hours,
							date.enddate.minutes,
							date.enddate.seconds).getTime();

						var data = { start_date: start_date, end_date: end_date, id: videoData.ID }
						AjaxApi.request({
							url: "Log/GetToolRecording.do",
							data: data,
							dataType: 'text',
							success: function (result)
							{
								var res = $.eval(result);

								for (var i = 0; i < res.data.length; i++)
								{
									res.data[i].xuhao = i;
									var date = new Date(parseInt(res.data[i].StartTime));
									res.data[i].StartTime = date.toString('yyyy-mm-dd HH:nn:ss');
									res.data[i].FileName = res.data[i].Name;
								}

								table.reload('recordinglist', {
									data: res.data,
									limit: 10,
								});
							}
						})
					}

					return false;
				})

				$('#recordingReset').click(function ()
				{
					document.getElementById('toolrecording').reset();
					renderRecordingTable();
				})

				form.on('submit(alarmSearch)', function (obj)
				{
					var code = form.val('toolalarm').alarmcode;
					var date = $('#alarmDate').data('date');
					if (!!date)
					{
						var videoID = $('#alarmSearch').attr('name');

						var start_date = new Date(date.startdate.year,
							date.startdate.month - 1,
							date.startdate.date,
							date.startdate.hours,
							date.startdate.minutes,
							date.startdate.seconds, 0).getTime();
						var end_date = new Date(date.enddate.year,
							date.enddate.month - 1,
							date.enddate.date + 1,
							date.enddate.hours,
							date.enddate.minutes,
							date.enddate.seconds, 0).getTime();

						var data = { start_date: start_date, end_date: end_date, videoID: videoID, code: code }
						var ajax = {};
						ajax.dataType = 'text';
						ajax.url = 'Alarm/GetToolAlarm.do';
						ajax.data = data;
						ajax.success = function (result)
						{
							var res = $.eval(result);

							for (var i = 0; i < res.data.length; i++)
							{
								res.data[i].xuhao = i;
								var date = new Date(parseInt(res.data[i].Time));
								res.data[i].StartTime = date.toString('yyyy-mm-dd HH:nn:ss');
								res.data[i].FileName = res.data[i].Name;
							}

							table.reload('alarmlist', {
								data: res.data,
								limit: 10,
							});
						}
						$.ajax(ajax);
					}

					return false;
				})

				$('#alarmReset').click(function ()
				{
					document.getElementById('toolalarm').reset();
					renderAlarmTable();
				})

				renderRecordingTable();

				function renderRecordingTable()
				{
					table.render({
						elem: '#recordinglist',
						id: 'recordinglist',
						even: true,
						loading: false,
						limit: 10,
						limits: [5, 7, 10],
						cols: [[{ title: lang("序号"), field: "xuhao", align: 'center', width: 60 },
						{ title: lang("文件名"), field: "FileName", align: 'center', width: 160 },
						{ title: lang("文件路径"), field: "Data1", align: 'center', width: 382, hide: 1 },
						{ title: lang("录屏时间"), field: "StartTime", align: 'center', width: 256 },
						{ title: lang("操作"), field: "OP", align: 'center', toolbar: '#recordingOp', minWidth: 160 },
						]],
						page: true,
						sort: true,
						defaultToolbar: [],
						data: [],
					});
				}

				table.on('tool(recordinglist)', function (obj)
				{
					var data = obj.data;
					if (obj.event === 'down')
					{
						var src = '../file/' + data.Data1.replace('\\', '/');
						if (!window.hyperLinkElement)
							window.hyperLinkElement = window.document.createElement('A');
						window.hyperLinkElement.href = src;
						window.Data('Call', window.hyperLinkElement.href);
						return;
					}

					if (obj.event === 'view')
					{
						var src = '../file/' + data.Data1.replace('\\', '/');
						console.log(src)
						if (!!window.clientCef)
						{
							window.Data('PlayVideo', src);
							return;
						}

						var index1 = layer.open({
							title: data.Name + ' 机台录屏回放'
							, type: 1
							, resize: false
							, area: ['833px', '619px']
							, content: '<video src="' + src + '" style="height: 100%; width: 100%; background-color: #393D49;" controls="" autoplay=""></video>'
							, shadeClose: true
							, success: function ()
							{
							}
							, end: function ()
							{
								layer.close(index1);
							}
						});
					}
				});

				$('#msgReset').click(function ()
				{
					document.getElementById('toolmsg').reset();

				})

				form.on('submit(msgSearch)', function (obj)
				{
					console.log(444, obj.field);
					return
					table.reload('msglist', {
						data: [
							{ xuhao: '1', Name: 'Msg1', hot: '1', Level: '信息', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '2', Name: 'Msg1', hot: '2', Level: 'xx', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '3', Name: 'Msg1', hot: '3', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '4', Name: 'Msg1', hot: '4', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '5', Name: 'Msg1', hot: '5', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '6', Name: 'Msg1', hot: '6', Level: 'xx', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '7', Name: 'Msg1', hot: '7', Level: 'xx', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
							{ xuhao: '8', Name: 'Msg1', hot: '8', Level: 'x', Status: 'Set', lot: 'Msg', StartTime: '2023-1-1', EndTime: '2023-1-2' },
						]
					});

					return false;
				})
			});

		function renderAlarmTable()
		{
			table.render({
				elem: '#alarmlist',
				id: 'alarmlist',
				even: true,
				loading: false,
				page: true,
				limit: 10,
				limits: [5, 7, 10],
				cols: [[
					{ title: lang('序号'), field: 'xuhao', align: 'center' }
					, { title: lang("报警名称"), field: "Name", align: 'center', sort: true, minWidth: 110, }
					, { title: lang("报警代码"), field: "Data1", align: 'center', sort: true, minWidth: 110, }
					, { title: lang("报警等级"), field: "Data2", align: 'center', minWidth: 110, }
					, { title: lang("报警描述"), field: "Data", align: 'center', minWidth: 110, }
					, { title: lang("发生时间"), field: "StartTime", align: 'center', minWidth: 110, }
				]],
				page: true,
				sort: true,
				defaultToolbar: [],
				data: [],
			});
		}

		function onKVMVideoInfoShow()
		{
			var testData = { "pn": "AIRCON-T2HLC", "sn": "05150011142", "APP_v": "T-R2B.2.1.28", "buildtime": "2023-05-05 18:54:01\n", "BSP_v": "2.1.4\n", "cpu_T": "78.1℃", "cpurate": "19.5%", "memrate": "7.2%", "rootfsdiskrate": "86.0%", "APP_status": true, "userdatafsdiskrate": "5.0%", "appfsdiskrate": "35.0%", "video_input": true, "v_resolution": "1920x1080", "v_fps": "60", "v_interlaced": "逐行", "v_colorspace": "RGB", "audio_input": "HDMI IN", "audio_sample": "48000 16bit", "encod_status": true, "sub_encod_status": true, "encod_fps": "60", "sub_encod_fps": "15", "imag_encod_fps": "0", "interlock_state": "远端" };

			var kvmInfoData = testData;

			var titleData = ['视频信号输入：', '输入分辨率：', '刷新频率：',
				'扫描类型：', '颜色格式：', '音频源：',
				'音频采样率：', '主码流：',
				'子码流：', '主码流编码帧率：', '子码流编码帧率：', '抓图码流编码帧率：'];

			var contentData = [];
			contentData.push(kvmInfoData.video_input);
			contentData.push(kvmInfoData.v_resolution);
			contentData.push(kvmInfoData.v_fps);
			contentData.push(kvmInfoData.v_interlaced);
			contentData.push(kvmInfoData.v_colorspace);
			contentData.push(kvmInfoData.audio_input);
			contentData.push(kvmInfoData.audio_sample);
			contentData.push(kvmInfoData.encod_status);
			contentData.push(kvmInfoData.sub_encod_status);
			contentData.push(kvmInfoData.encod_fps);
			contentData.push(kvmInfoData.sub_encod_fps);
			contentData.push(kvmInfoData.imag_encod_fps);

			var n = $('#kvmVideoInfo'), dom = '';

			n.children().remove();

			for (var i = 0; i < contentData.length; i++)
			{
				var div = '<div style="display:flex; margin-bottom:10px;">';
				var label = '<label class="kvmTitleLabel">' + titleData[i] + '</label>';
				if (!!contentData[i] == false)
					contentData[i] = '';
				var span = '<div><span class="kvmContentSpan">' + contentData[i] + '</span></div>';
				dom += div + label + span + '</div>';
			}

			n.eq(0).append(dom);
		}

		function onKVMDeviceInfoShow()
		{
			var testData = {
				"pn": "AIRCON-T2HLC", "sn": "05150011142", "APP_v": "T-R2B.2.1.28", "buildtime": "2023-05-05 18:54:01\n", "BSP_v": "2.1.4\n",
				"cpu_T": "78.1℃", "cpurate": "19.5%", "memrate": "7.2%", "rootfsdiskrate": "86.0%", "APP_status": true, "userdatafsdiskrate": "5.0%", "appfsdiskrate": "35.0%", "video_input": true, "v_resolution": "1920x1080", "v_fps": "60", "v_interlaced": "逐行", "v_colorspace": "RGB", "audio_input": "HDMI IN", "audio_sample": "48000 16bit", "encod_status": true, "sub_encod_status": true, "encod_fps": "60", "sub_encod_fps": "15", "imag_encod_fps": "0", "interlock_state": "远端"
			};

			var kvmInfoData = testData;

			var titleData = ['设备型号：', '设备序列号：', '系统版本：', '更新时间：', 'BSP版本：',];

			var contentData = [];
			contentData.push(kvmInfoData.pn);
			contentData.push(kvmInfoData.v_resolution);
			contentData.push(kvmInfoData.sn);
			contentData.push(kvmInfoData.APP_v);
			contentData.push(kvmInfoData.BSP_v);

			var n = $('#kvmDeviceInfo'), dom = '';

			n.children().remove();

			for (var i = 0; i < contentData.length; i++)
			{
				var div = '<div style="display:flex; margin-bottom:10px;">';
				var label = '<label class="kvmTitleLabel">' + titleData[i] + '</label>';
				if (!!contentData[i] == false)
					contentData[i] = '';
				var span = '<div><span class="kvmContentSpan">' + contentData[i] + '</span></div>';
				dom += div + label + span + '</div>';
			}

			n.eq(0).append(dom);
		}

		function onKVMResourceInfoShow()
		{
			var option = {
				title: {
					text: 'KVM资源使用状态'
				},
				tooltip: {
					trigger: 'axis',
					formatter: '',
				},
				legend: {
					data: ['CPU使用率', '运行内存使用率', 'CPU温度', '磁盘使用率']
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true
				},
				toolbox: {
					feature: {
						saveAsImage: {}
					}
				},
				xAxis: {
					type: 'category',
					boundaryGap: !1,
					data: [0]
				},
				yAxis: {
					type: 'value',
					boundaryGap: !1,
				},
				color: ['#5470c6', '#91cc75', '#fac858', '#ee6666'],
				series: [
					{
						name: 'CPU使用率',
						type: 'line',
						areaStyle: {},
					},
					{
						name: '运行内存使用率',
						type: 'line',
						areaStyle: {},
					},
					{
						name: 'CPU温度',
						type: 'line',
						data: [70],
						areaStyle: {},
					},
					{
						name: '磁盘使用率',
						type: 'line',
						areaStyle: {},
					}
				]
			};
			myChart1 = echarts.init(document.getElementById('kvmResourceInfo'));
			myChart1.setOption(option);

			startKVMResource();
		}

		function startKVMResource()
		{
			setTimeout(function ()
			{
				if ($('#toolkvm').css('display') == 'none')
				{
					lastKVMRes = { date: [], cpu: [], cpu_t: [], mem: [], disk: [] };
					return;
				}
				try
				{
					var data = window.top.window.VideoList.filter(function (item, index, arr) { return item.ID == videoID })[0];

					if (!!data)
						data = $.eval(data.DeviceState);
					else
						return;

					lastKVMRes.date.push(new Date().toString('yyyy-mm-dd HH:nn:ss'));
					lastKVMRes.cpu.push(data.cpurate.replace('%', ''));
					lastKVMRes.cpu_t.push(data.cpu_T.slice(0, data.cpu_T.length - 1));
					lastKVMRes.mem.push(data.memrate.replace('%', ''));
					lastKVMRes.disk.push(data.appfsdiskrate.replace('%', ''));

					if (lastKVMRes.date.length > 20)
					{
						lastKVMRes.date = lastKVMRes.date.slice(1);
						lastKVMRes.cpu = lastKVMRes.cpu.slice(1);
						lastKVMRes.cpu_ = lastKVMRes.cpu_t.slice(1);
						lastKVMRes.mem = lastKVMRes.mem.slice(1);
						lastKVMRes.disk = lastKVMRes.disk.slice(1);
					}

					var option = {
						title: {
							text: 'KVM资源使用状态'
						},
						tooltip: {
							trigger: 'axis',
							formatter: '',
						},
						legend: {
							data: ['CPU使用率', '运行内存使用率', 'CPU温度', '磁盘使用率']
						},
						grid: {
							left: '3%',
							right: '4%',
							bottom: '3%',
							containLabel: true
						},
						toolbox: {
							feature: {
								saveAsImage: {}
							}
						},
						xAxis: {
							type: 'category',
							boundaryGap: !1,
							data: lastKVMRes.date
						},
						yAxis: {
							type: 'value',
							boundaryGap: !1,
						},
						color: ['#5470c6', '#91cc75', '#fac858', '#ee6666'],
						series: [
							{
								areaStyle: {},
								name: 'CPU使用率',
								type: 'line',
								data: lastKVMRes.cpu,
							},
							{
								name: '运行内存使用率',
								type: 'line',
								data: lastKVMRes.mem,
								areaStyle: {},
							},
							{
								name: 'CPU温度',
								type: 'line',
								data: lastKVMRes.cpu_t,
								areaStyle: {},
							},
							{
								name: '磁盘使用率',
								type: 'line',
								data: lastKVMRes.disk,
								areaStyle: {},
							}
						]
					};
					myChart1.setOption(option);

					startKVMResource();
				}
				catch (e)
				{
					startKVMResource();
				}
			}, 1000);
		}

		var videoID = '';
		var lastKVMRes = { date: [], cpu: [], cpu_t: [], mem: [], disk: [] };
		var myChart1 = {};
		function onKVMMsg(element)
		{
			videoID = element.id;
			layer.open({
				type: 1,
				title: 'KVM信息',
				id: 'messabelabel',
				content: $('#toolkvm'),
				resize: false,
				shadeClose: true,
				success: function ()
				{
				},
				end: function ()
				{
					$('#toolkvm')[0].reset();
				}
			});
		}

		function onNotepad(element)
		{
			var video = $(element).data('video');
			var notebook = function (v, callback)
			{
				var ajax = {};
				if (!window.CS || !window.CS.Settings || !window.CS.Settings.NFS)
					ajax.href = '../file/notebook/' + video.ID + '.txt';
				else
					ajax.href = '../file/(NFS)/notebook/' + video.ID + '.txt';
				ajax.src = ajax.href;
				ajax.url = ajax.href;
				if (!!v)
				{
					var noteContent = document.getElementById('notepad').value;
					ajax.data = noteContent;;
					ajax.type = 'POST';
				}
				ajax.success = function (result)
				{
					callback(result)
				};
				$.ajax(ajax);
			}
			var noteDialog = layer.open({
				title: lang('共享记事本')
				, type: 1
				, content: $('#form-notepad')
				, btn: [lang('确认'), lang('取消')]
				, btnAlign: 'c'
				, area: ['50%', '60%']
				, success: function ()
				{
					notebook(false, function (res)
					{
						document.getElementById('notepad').value = res;
					})
				}
				, btn1: function (value, index)
				{
					var noteContent = document.getElementById('notepad').value;
					if (noteContent.length != 0)
					{
						notebook(true, function ()
						{
							layer.msg(lang('保存成功'), { icon: 1 });
							layer.close(noteDialog);
						})
					}
				}
				, end: function ()
				{
					document.getElementById('notepad').value = '';
					layer.close(noteDialog);
				}
			});
		}

		function onAlarm(element)
		{
			var video = $(element).data('video');
			layer.open({
				type: 1,
				title: '机台报警',
				content: $('#toolalarm'),
				area: ['900px', '619px'],
				resize: false,
				shadeClose: true,
				success: function ()
				{
					renderAlarmTable();
					$('#alarmSearch').attr('name', video.ID);
				}
				, end: function ()
				{
					$('#toolalarm')[0].reset();
					$('#alarmSearch').attr('name', null);
					$('#alarmDate').data('date', null);
				}
			});
		}

		function onCellDblclick(element)
		{
			var video = $(element).data('video');
			if (!!video)
			{
				if (!window.top.window.webbrowser && !window.top.window.doOpenVideo(video.ID, video.Url))
					layer.msg('正在打开');
			}
		}

		function onShowBtnClick(element)
		{
			var video = $(element).data('video');
			if (!!video)
			{
				if (!window.top.window.webbrowser && !window.top.window.doOpenVideo(video.ID, video.Url))
					layer.msg('正在打开');
			}
		}

		function onToolMsg(element)
		{
			layer.open({
				type: 1,
				title: '机台信息',
				content: $('#toolmsg'),
				resize: false,
				shadeClose: true,
				area: ['900px', '619px'],
				success: function ()
				{
					table.render({
						elem: '#msglist'
						, even: true
						, loading: false
						, page: true
						, defaultToolbar: []
						, limit: 10
						, id: 'msglist'
						, cols: [[
							{ title: lang('序号'), field: 'xuhao', align: 'center' }
							, { title: lang("hot"), field: "hot", align: 'center', sort: true, minWidth: 110, }
							, { title: lang("lot"), field: "lot", align: 'center', sort: true, minWidth: 110, }
							, { title: lang("Msg1"), field: "Level", align: 'center', minWidth: 110, }
							, { title: lang("Msg2"), field: "Status", align: 'center', minWidth: 110, }
							, { title: lang("Msg2"), field: "Note", align: 'center', minWidth: 110, }
							, { title: lang("更新时间"), field: "StartTime", align: 'center', minWidth: 110, }
						]]
						, data: []
					});
				},
				end: function ()
				{
					$('#toolmsg')[0].reset();
				}
			});
		}

		var fullScreenVideo = undefined;

		function onFullScreenClick(element)
		{
			var video = $(element).data('video');
			var gridcount = window.top.window.login.GridCount;
			if (!!video)
			{
				video.FullScreen = !video.FullScreen;
				var pos = video.Position % gridcount;
				if (video.FullScreen)
				{
					fullScreenVideo = video;
					$('.cellFrame:eq(' + pos + ')').css('position', 'absolute');
					$('.cellFrame:eq(' + pos + ')').css('z-index', '323');
					$('.cellFrame:eq(' + pos + ')').css('top', '0');
					$('.cellFrame:eq(' + pos + ')').css('left', '0');
					$('.cellFrame:eq(' + pos + ')').css('width', 'calc(100% - 10px)');
					$('.cellFrame:eq(' + pos + ')').css('height', 'calc(100% - 1px)');
					$('.cellFrame:eq(' + pos + ')').css('min-width', '1240px');
					var minHeight = window.innerHeight + 20 > 800 ? window.innerHeight + 20 : 800;
					$('.cellFrame:eq(' + pos + ')').css('min-height', minHeight + 'px');
					$('.cellFrame:eq(' + pos + ')').toggleClass('layui-anim');
					$('.cellFrame:eq(' + pos + ')').toggleClass('layui-anim-scale');
					$('.footer').css('display', 'none');

					$(element).removeClass('fullScreenspan').addClass('unFullScreenspan');
					$(element).attr('title', '缩小查看');

					layer.msg('正在放大 ' + video.Name + ' 画面');
				}
				else
				{
					fullScreenVideo = undefined;
					var rows = window.top.window.login.Row;
					var cols = window.top.window.login.Column;
					$('.cellFrame:eq(' + pos + ')').css('position', '');
					$('.cellFrame:eq(' + pos + ')').css('z-index', '');
					$('.cellFrame:eq(' + pos + ')').css('top', '');
					$('.cellFrame:eq(' + pos + ')').css('left', '');
					$('.cellFrame:eq(' + pos + ')').css('width', 'calc(' + 1.0 / cols * 100 + '% - 10px)');
					if (rows > 4)
					{
						$('.cellFrame:eq(' + pos + ')').css('height', 1.0 / rows * 100 + '%');
					} else
					{
						var minus = rows > 2 ? 15 : 20;
						$('.cellFrame:eq(' + pos + ')').css('height', 'calc(' + 1.0 / rows * 100 + '% - ' + minus + 'px)');
					}
					$('.cellFrame:eq(' + pos + ')').css('min-width', '');
					$('.cellFrame:eq(' + pos + ')').css('min-height', '');
					$('.cellFrame:eq(' + pos + ')').toggleClass('layui-anim');
					$('.cellFrame:eq(' + pos + ')').toggleClass('layui-anim-scale');
					$('.footer').css('display', 'block');

					$(element).removeClass('unFullScreenspan').addClass('fullScreenspan');
					$(element).attr('title', '放大查看');

					layer.msg('正在缩小 ' + video.Name + ' 画面');
				}
			}
		}

		function onSwitchMainSub(element)
		{
			var video = $(element).data('video');
			if (!!video)
			{
				video.Master = !video.Master;
				var gridcount = window.top.window.login.GridCount;
				var i = parseInt(video.Position % gridcount);
				if (video.Master)
				{
					$('.switchScreenSpan:eq(' + i + ')').removeClass('subScreenSpan').addClass('mainScreenSpan');
					$('.switchScreenSpan:eq(' + i + ')').html('1');
					$('.switchScreenSpan:eq(' + i + ')').attr('title', '切换为2屏');
					$('.thumbImg:eq(' + i + ')').attr('src', '/frame/' + video.UrlAuthority + '/thumb?Timeout=5&t=' + Date.now());
					$('.showUpspan:eq(' + i + ')').data('video', video);
					$('.thumbdiv:eq(' + i + ')').data('video', video);
					$('.thumbdiv:eq(' + i + ')').attr('name', video.ID);
					$('.messagediv:eq(' + i + ')').children('span').attr('name', video.ID);

					layer.msg('机台 ' + video.Name + '已切为1屏');
				}
				else
				{
					$('.switchScreenSpan:eq(' + i + ')').removeClass('mainScreenSpan').addClass('subScreenSpan');
					$('.switchScreenSpan:eq(' + i + ')').html('2');
					$('.switchScreenSpan:eq(' + i + ')').attr('title', '切换为1屏');
					$('.thumbImg:eq(' + i + ')').attr('src', '/frame/' + video.SlaveVideo.UrlAuthority + '/thumb?Timeout=5&t=' + Date.now());
					$('.showUpspan:eq(' + i + ')').data('video', video.SlaveVideo);
					$('.thumbdiv:eq(' + i + ')').data('video', video.SlaveVideo);
					$('.thumbdiv:eq(' + i + ')').attr('name', video.SlaveVideo.ID);
					$('.messagediv:eq(' + i + ')').children('span').attr('name', video.SlaveVideo.ID);

					layer.msg('机台 ' + video.Name + '已切为2屏');
				}
			}
		}

		var recordIndex;
		function onRecord(element)
		{
			var video = $(element).data('video');

			recordIndex = layer.open({
				title: video.Name + ' 机台录屏回放'
				, type: 1
				, resize: false
				, id: 'edit'
				, area: ['833px', '619px']
				, content: $('#toolrecording')
				, shadeClose: true
				, success: function ()
				{
					$('#recordingSearch').data('video', video);
				}
				, end: function ()
				{
					$('#toolrecording')[0].reset();
					$('#recordingSearch').data('video', null);
					$('#recordingDate').data('date', null);
					table.render({
						elem: '#recordinglist',
						id: 'recordinglist',
						even: true,
						loading: false,
						limit: 10,
						limits: [5, 7, 10],
						cols: [[{ title: lang("序号"), field: "xuhao", align: 'center' },
						{ title: lang("文件名"), field: "Name", align: 'center' },
						{ title: lang("文件路径"), field: "Data1", align: 'center' },
						{ title: lang("录屏时间"), field: "StartTime", align: 'center' },
						{ title: lang("操作"), field: "Name", align: 'center', toolbar: '#recordingOp', minWidth: 130 },
						]],
						page: true,
						sort: true,
						defaultToolbar: [],
						data: [],
					});
				}
			});
		}

		function changeToolStatus(str)
		{
			var n = str.split(",");

			for (var i = 0; i < favData.length; i++)
			{
				if (favData[i].Name == n[1])
				{
					var location = favData[i].Position;

					if (!!n[2])
					{
						$(".cellFrame:eq(" + location + ")").removeClass('whitecell').removeClass("greencell").removeClass("yellowcell").removeClass("redcell");
						$(".cellFrame:eq(" + location + ")").addClass(n[2]);
					}

					break;
				}
			}
		}

		var lastAlarmList = new Map();
		function onlastAlarm(elem)
		{
			var video = $(elem).data('video');
			var lastAlarm = [];
			lastAlarmList.forEach(function (value, key)
			{
				if (video.Name.indexOf(key) > -1)
				{
					lastAlarm = value;
					return;;
				}
			})
			if (!!lastAlarm)
			{
				layer.open({
					title: video.Name + ' 近期报警记录'
					, type: 1
					, resize: false
					, area: ['900px', '619px']
					, content: $('#toollastalarm')
					, shadeClose: false
					, success: function ()
					{
						table.render({
							elem: '#lastalarmtable',
							even: true,
							loading: false,
							cols: [[
								{ title: lang("报警名称"), field: "alarmname", align: 'center', sort: true, minWidth: 110, }
								, { title: lang("报警代码"), field: "alarmcode", align: 'center', sort: true, minWidth: 110, }
								, { title: lang("报警等级"), field: "alarmlevel", align: 'center', minWidth: 110, }
								, { title: lang("报警描述"), field: "alarmnote", align: 'center', minWidth: 110, }
								, { title: lang("发生时间"), field: "alarmtime", align: 'center', minWidth: 110, }
							]],
							sort: true,
							defaultToolbar: [],
							data: lastAlarm,
						});
					}
					, end: function ()
					{
						$('#toollastalarm').hide();
					}
				});
			}
		}

		function toolAlarm(str)
		{
			var n = str.split(",");

			for (var i = 0; i < favData.length; i++)
			{
				if (favData[i].Name == n[1])
				{
					var location = favData[i].Position;
					var name = favData[i].Name;

					if (n[5] == 'on')
					{
						$(".cellFrame:eq(" + location + ")").addClass('alarmcell');
						layer.msg("机台" + name + "发生报警：" + str);

						if (n[4] == '4')
						{
							layer.msg(lang('机台 ' + name + ' 发生 code为 :' + n[3] + '内容为: ' + n[6] + ' 的报警'));
						}
						else
						{
							layer.alert(lang('机台 ' + name + ' 发生 code为 :' + n[3] + '内容为: ' + n[6] + ' 的报警'), { btn: [lang('确定')], title: lang('提示') });
						}
					} else
					{
						$(".cellFrame:eq(" + location + ")").removeClass('alarmcell');
						layer.msg(lang('机台 ' + name + ' 消除 code为 :' + n[3] + '内容为: ' + n[5] + ' 的报警'));
					}

					break;
				}
			}
		}

		//a: object
		function alarmOccur(a)
		{
			var favs = favData.filter(function (f) { return f.Name.indexOf(a.toolname) > -1 });
			if (favs.length > 0)
			{
				var name = a.toolname,
					code = a.alarmcode,
					note = a.alarmnote,
					level = a.alarmlevel,
					lastAlarm = lastAlarmList.get(name);
				if (a.alarmset == 'on')
				{
					layer.msg("机台" + name + "发生报警：" + a.alarmnote);

					if (level == '0')
					{
						layer.msg(lang('机台 ' + name + ' 发生 code为 :' + code + '内容为: ' + note + ' 的报警'));
					}
					else
					{
						layer.alert(lang('机台 ' + name + ' 发生 code为 :' + code + '内容为: ' + note + ' 的报警'), { btn: [lang('确定')], title: lang('提示') });
					}

					if (!!lastAlarm == false)
						lastAlarm = [];

					a.alarmtime = new Date().toString('yyyy-mm-dd HH:nn:ss');
					lastAlarm.push(a);
					lastAlarmList.set(name, lastAlarm);
				}
				else
				{
					layer.msg(lang('机台 ' + name + ' 消除 code为 :' + code + '内容为: ' + note + ' 的报警'));

					var index = lastAlarm.findIndex(function (last) { return last.toolname == name && last.alarmcode == a.alarmcode; });
					if (index > -1)
						lastAlarm.splice(index, 1);
					lastAlarmList.set(name, lastAlarm);
				}

				favs.forEach(function (fav)
				{
					var location = fav.Position;
					if (lastAlarm.length == 0)
					{
						$(".lastAlarmSpan:eq(" + location + ")").removeClass('showSpan').addClass('hideSpan');
						$(".cellFrame:eq(" + location + ")").removeClass('alarmcell');
					}
					else
					{
						$(".lastAlarmSpan:eq(" + location + ")").removeClass('hideSpan').addClass('showSpan');
						$(".cellFrame:eq(" + location + ")").addClass('alarmcell');
					}
				})
			}
		}

		//s: object
		function changedStatus(s)
		{
			var favs = favData.filter(function (f) { return f.Name.indexOf(s.toolname) > -1 });
			if (favs.length > 0)
			{
				favs.forEach(function (fav)
				{
					var location = fav.Position;
					$(".statusSpan:eq(" + location + ")").removeClass('hideSpan');
					switch (s.status)
					{
						case 'Down':
							$(".statusSpan:eq(" + location + ")").css('color', 'white');
							$(".statusSpan:eq(" + location + ")").css('background-color', 'red');
							$(".statusSpan:eq(" + location + ")").html('Down');
							break;
						case 'IDLE':
							$(".statusSpan:eq(" + location + ")").css('color', 'black');
							$(".statusSpan:eq(" + location + ")").css('background-color', 'yellow');
							$(".statusSpan:eq(" + location + ")").html('IDLE');
							break;
						case 'RUN':
							$(".statusSpan:eq(" + location + ")").css('color', 'white');
							$(".statusSpan:eq(" + location + ")").css('background-color', 'green');
							$(".statusSpan:eq(" + location + ")").html('RUN');
							break;
					}
				});
			}
		}

		function checkVideo(type)
		{
			if (type == CS.VideoData.TypeKVM ||
				type == CS.VideoData.TypeCCTV ||
				type == CS.VideoData.TypeTouch)
				return true;
			else
				return false;
		}

		function toolShowThreshold(str)
		{
			var n = str.split(",");

			for (var i = 0; i < favData.length; i++)
			{
				if (favData[i].UrlAuthority == n[0])
				{
					var name = favData[i].Name;
					var content = n[1];

					layer.msg("机台" + name + "发生报警：" + content);

					break;
				}
			}
		}
	</script>
</head>
<body class="mainBody">
	<div class="mainFrame" id="sortList">
	</div>
	<!--<div class="footer" id="footer"></div>-->

	<form id="toolalarm" class="layui-form" lay-filter="toolalarm" style="display: none;">
		<div class="layui-field-box" style="text-align:center;">
			<div class="layui-form-item">
				<div class="layui-inline">
					<div class="layui-inline">
						<label class="layui-form-label">日期范围</label>
					</div>
					<div class="layui-inline">
						<input style="width:300px;" class="layui-input" type="text" autocomplete="off" name="alarmDate" id="alarmDate" placeholder="-">
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">报警代码</label>
					</div>
					<div class="layui-inline">
						<input style="width:100px;" class="layui-input" type="number" autocomplete="off" name="alarmcode" id="alarmcode">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="alarmSearch" id="alarmSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn" id="alarmReset"><span>重置</span></button>
					</div>
				</div>
			</div>

			<table class="layui-table" id="alarmlist" lay-filter="alarmlist"></table>
		</div>
	</form>

	<form id="toolmsg" class="layui-form" lay-filter="form-toolmsg" style="display: none;">
		<div class="layui-field-box" style="text-align:center;">
			<div class="layui-form-item">
				<div class="layui-inline">
					<div class="layui-inline">
						<label class="layui-form-label">日期范围</label>
					</div>
					<div class="layui-inline">
						<input style="width:300px;" class="layui-input" type="text" autocomplete="off" name="msgDate" id="msgDate" placeholder="-">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="msgSearch" id="msgSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn" id="msgReset"><span>重置</span></button>
					</div>
				</div>
			</div>

			<table class="layui-table" id="msglist" lay-filter="msgtable"></table>
		</div>
	</form>

	<form id="toolkvm" class="layui-form" lay-filter="form-toolkvm" style="display:none;">
		<div class="layui-tab" style="height: calc(100% - 31px); min-width: inherit; padding: 10px !important;" lay-filter="tab">
			<ul class="layui-tab-title" style="font-size: 12px;">
				<li class="layui-this">视频信息</li>
				<li>设备信息</li>
				<li>资源使用状态</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item  layui-show" style="overflow-y: auto; overflow-x:auto;">
					<fieldset class="layui-elem-field">
						<legend><span>视频信息</span></legend>
						<blockquote class="layui-elem-quote layui-quote-nm" id="kvmVideoInfo">
						</blockquote>
					</fieldset>
				</div>
				<div class="layui-tab-item" style="overflow-y: auto; overflow-x: auto;">
					<fieldset class="layui-elem-field">
						<legend><span>设备信息</span></legend>
						<blockquote class="layui-elem-quote layui-quote-nm" id="kvmDeviceInfo">
						</blockquote>
					</fieldset>
				</div>
				<div class="layui-tab-item" style="overflow-y: auto; overflow-x: auto;">
					<fieldset class="layui-elem-field">
						<legend><span>资源使用状态</span></legend>
						<div class="layui-elem-quote layui-quote-nm">
							<div id="kvmResourceInfo" style="width:800px;height:300px;">
							</div>
						</div>
					</fieldset>
				</div>
			</div>
		</div>
	</form>

	<form id="toolrecording" class="layui-form" lay-filter="form-toolrecording" style="display:none;">
		<div class="layui-field-box">
			<div class="layui-form-item">
				<div class="layui-inline" style="left: 20%;">
					<div class="layui-inline">
						<label class="layui-form-label">日期范围</label>
					</div>
					<div class="layui-inline">
						<input class="layui-input" type="text" autocomplete="off" name="recordingDate" id="recordingDate" placeholder="-">
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="recordingSearch" id="recordingSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn" id="recordingReset"><span>重置</span></button>
					</div>
				</div>
			</div>

			<table class="layui-table" id="recordinglist" lay-filter="recordinglist"></table>
			<script type="text/html" id="recordingOp">
				<a class="layui-btn layui-btn-xs m_UserBC" name="eventView" lay-event="view">{{lang('观看')}}</a>
				<a class="layui-btn layui-btn-xs m_UserBC" name="eventDown" lay-event="down">{{lang('下载')}}</a>
			</script>
		</div>
	</form>

	<div id="toollastalarm">
		<table id="lastalarmtable"></table>
	</div>

	<form class="layui-form" lay-filter="form-notepad" id="form-notepad" style="display: none; width: 100%; height: 100%;">
		<textarea id="notepad" style="width: calc(100% - 45px); height: calc(100% - 45px); min-width: 400px; min-height: 200px; margin: 10px; padding: 10px; border-color: lightgray" placeholder="请输入..."></textarea>
	</form>

	<iframe id="frm-hide" style="position: absolute; top: 0px; left: 0px; width: 1px; height: 1px; border: none;" width="1" height="1" frameborder="0" scrolling="no"></iframe>
	<!--<a href="javascript://" onclick="javascript: location.reload();" style="color: #EEEEFF;">reload</a>-->
</body>
</html>
