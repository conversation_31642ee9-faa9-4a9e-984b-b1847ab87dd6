﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>AlarmList</title>
    <link href="lib/layui/css/layui.css" rel="stylesheet" />
    <link href="css/common.css" rel="stylesheet" />
    <link rel="stylesheet" href="/user/loginMenu.docss" />
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script type="text/javascript" src="lib/layui/layui.js"></script>
    <script type="text/javascript" src="lib/pickgold.js"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="/Config/List.do?Type=STOREHOUSE&js=storehouse"></script>
    <script src="lib/layui/excel.js"></script>
    <script src="js/xm-select.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
    <script src="js/request.js"></script>
    <style>
        .field-float {
            float: left;
            min-height: 55px;
            padding: 7px;
            margin: 0px 5px 10px 5px;
        }

        #statusform .edit {
            width: 150px;
        }
    </style>
    <script type="text/javascript">
        layui.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'transfer', 'laydate', 'excel', 'upload'], function () {
            $ = layui.$
                , laydate = layui.laydate
                , form = layui.form
                , table = layui.table
                , layer = layui.layer
                , element = layui.element
                , excel = layui.excel
                , laypage = layui.laypage
                , upload = layui.upload;

            form.on('submit(alarmform)', function (data) {
                var form_data = form.val("alarmform");
                form_data['type'] = "Alarm";
                form_data['mytype'] = "gyf";
                form_data['alarmset'] = form_data['alarmset'] ?? 'of';
				form_data['$:type'] = "GYF";
                var d = JSON.stringify(form_data);
                AjaxApi.request({
                    url: "/Alarm/SendMSG.do",
                    type: 'post',
                    data: d,
                    dataType: 'text',
                    success: function (result) { }
                })

                return false;
            });

            form.on('submit(statusform)', function (data) {
                var form_data = form.val("statusform");
                form_data['type'] = "Status";
                form_data['mytype'] = "gyf";
				form_data['$:type'] = "GYF";
                var d = JSON.stringify(form_data);
                AjaxApi.request({
                    url: "/Alarm/SendMSG.do",
                    type: 'post',
                    data: d,
                    dataType: 'text',
                    success: function (result) { }
                })
                return false;
            });
        });
    </script>
</head>
<body style="position: absolute; width: 100%; height: calc(100% - 40px); top: 40px; left: 20px; ">
    <fieldset class="layui-elem-field field-float">
        <legend>
            <span>报警</span>
        </legend>

        <div class="layui-field-box">
            <form class="layui-form" lay-filter="alarmform" id="alarmform" style="padding-top: 20px; padding-right: 20px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">机台名称</label>
                    </div>
                    <div class="layui-inline edit">
                        <input type="text" class="layui-input" name="toolname" autocomplete="off" placeholder="请输入机台名称"
                               oninput="cleanSpelChar(this)">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">报警名称</label>
                    </div>
                    <div class="layui-inline edit">
                        <input type="text" class="layui-input" name="alarmname" autocomplete="off" placeholder="请输入报警名称"
                               oninput="cleanSpelChar(this)">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">报警代码</label>
                    </div>
                    <div class="layui-inline edit">
                        <input type="number" class="layui-input" min="0" name="alarmcode" placeholder="请输入报警代码"
                               oninput="cleanSpelChar(this)">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">报警等级</label>
                    </div>
                    <div class="layui-inline edit">
                        <input type="text" class="layui-input" name="alarmlevel" autocomplete="off" placeholder="请输入报警名称"
                               oninput="cleanSpelChar(this)">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">报警发生</label>
                    </div>
                    <div class="layui-inline edit">
                        <input style="height:20px;" type="checkbox" class="layui-input" name="alarmset"
                               lay-skin="switch" lay-text="开启|关闭">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">报警描述</label>
                    </div>
                    <div class="layui-inline edit">
                        <textarea type="text" class="layui-textarea" style="height: 200px; width:200px; white-space: pre-wrap;overflow:auto;" name="alarmnote" autocomplete="off" placeholder="请输入少于200字的描述"
                                  oninput="limitTextAreaCount(this)"></textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;"></label>
                    </div>
                    <div class="layui-inline edit">
                        <button type="submit" class="layui-btn">发送</button>
                    </div>
                </div>
            </form>
        </div>
    </fieldset>

    <fieldset class="layui-elem-field field-float">
        <legend>
            <span>状态</span>
        </legend>

        <div class="layui-field-box">
            <form class="layui-form" lay-filter="statusform" id="statusform" style="padding-top: 20px; padding-right: 20px;">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">机台名称</label>
                    </div>
                    <div class="layui-inline edit">
                        <input type="text" class="layui-input" name="toolname" autocomplete="off" placeholder="请输入机台名称"
                               oninput="cleanSpelChar(this)">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;">机台状态</label>
                    </div>
                    <div class="layui-inline edit">
                        <input type="radio" name="status" value="Down" title="Down">
                        <input type="radio" name="status" value="IDLE" title="IDLE">
                        <input type="radio" name="status" value="RUN" title="RUN">
                        <input type="radio" name="status" value="UNKOWN" title="UNKOWN">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width:119px;"></label>
                    </div>
                    <div class="layui-inline edit">
                        <button type="submit" class="layui-btn">发送</button>
                    </div>
                </div>
            </form>
        </div>
    </fieldset>
</body>
</html>