﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>Layout布局</title>
    <link rel="stylesheet" href="lib/layui/css/layui.css" />
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/layout-index.css" />
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script type="text/javascript" src="js/layout/go2.3.16.js"></script>
    <script type="text/javascript" src="js/layout/Figures.js"></script>
    <script type="text/javascript" src="lib/layui/layui.js"></script>
    <script type="text/javascript" src="lib/pickgold.js"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
    <script type="text/javascript" src="js/layout/loading.js"></script>
    <script type="text/javascript" src="js/request.js"></script>
    <style>
		#returnLayoutList {
			cursor: pointer;
            font-size: 14px;
		}

		#returnLayoutList:hover {
			text-decoration: underline;
		}
    </style>
    <script type="text/javascript">
        window.webName = 'layout';
        if (window.top == window.self)
            window.location.href = 'index.html';
        else
            window.top.layout = window;
        if (!!window.parent)
            window.parent.layout = window;

        function getQueryVariable(variable)
        {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++)
            {
                var pair = vars[i].split("=");
                if (pair[0] == variable) { return pair[1]; }
            }
            return (false);
        }
        var layoutId = '';
        $(document).on("ready", function ()
        {
            $('#layout-name').html(decodeURI(getQueryVariable('name')));
            layoutId = getQueryVariable('id');

			$('#returnLayoutList').on('click', function ()
			{
				window.location.href = 'LayoutList.html';
            })
        })

        window.addEventListener("beforeunload", function ()
        {
            var models = myDiagram.model.toJson();
            localStorage.setItem('layoutNode', models);
        })
    </script>
</head>
<body>
    <fieldset class="layui-elem-field layui-field-title">
		<legend>
			<span id="returnLayoutList">返回</span>
			<span id="layout-name">Layout</span>
		</legend>
    </fieldset>
    <div class="layout">
        <div id="paletteWrap">
            <div class="palette-title">组件</div>
			<div id="myPaletteDiv">
				<canvas tabindex="0"></canvas>
			</div>
        </div>

        <div id="myDiagramDiv">
            <div id="loading"></div>
            <canvas tabindex="0"></canvas>
        </div>
        <div id="contextMenu">
            <div class="menu-item">暂无数据</div>
        </div>
        <div id="tooltipInfo"
             style="position: absolute; z-index: 9999; display: none">
            <div class="header">
                <p>Status</p>
                <p id="tooltip-status">(Run/Idle/Down/Maintenance)</p>
            </div>
            <div class="li-item li-item-green">
                <span>PPID</span>
                <span id="tooltip-ppid">` + 1111 + `</span>
            </div>
            <div class="li-item">
                <span>Lot ID</span>
                <span id="tooltip-lotid">` + 2222 + `</span>
            </div>
            <div class="li-item li-item-green">
                <span>Glass ID</span>
                <span id="tooltip-glassid">` + 3333 + `</span>
            </div>
            <div class="li-item">
                <span>Alarm</span>
                <span id="tooltip-alarm">` + 4444 + `</span>
            </div>
            <div class="footer" id="tooltip-footer">PC & CCTV & Touch</div>
        </div>
        <div style="width: var(--right-width); margin-left: 2px">
            <div id="nodeInfo">
                <div class="nodeInfo-title">节点信息</div>
                <form id="nodeInfo-form" class="layui-form" lay-filter="edit-equip" onsubmit="javascript: return false;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">设备名称：</label>
                        <div class="layui-inline edit" style="width: calc(100% - 120px)">
                            <select name="equipName" id="equipname" lay-search=""></select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">填充色：</label>
                        <div class="layui-input-inline" style="width: 65px">
                            <input type="text" name="fill" value="" placeholder="请选择颜色" class="layui-input" id="fillColor" />
                        </div>
                        <div class="layui-inline" style="left: -11px">
                            <div id="fillColor-form"></div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">线条颜色：</label>
                        <div class="layui-input-inline" style="width: 65px">
                            <input type="text" name="strokeColor" value="" placeholder="请选择颜色" class="layui-input" id="strokeColor" />
                        </div>
                        <div class="layui-inline" style="left: -11px">
                            <div id="strokeColor-form"></div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">文本宽度：</label>
                        <div class="layui-inline edit" style="width: calc(100% - 120px)">
                            <input class="layui-input" type="number" min="10" name="textWidth" id="quipname" />
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">文本对齐：</label>
                        <div class="layui-inline edit" style="width: calc(100% - 120px)">
                            <select name="textAlign" id="text-align">
                                <option value="TopLeft">左上</option>
                                <option value="TopCenter">中上</option>
                                <option value="TopRight">右上</option>
                                <option value="Left">左</option>
                                <option value="Center">居中</option>
                                <option value="Right">右</option>
                                <option value="BottomLeft">左下</option>
                                <option value="BottomCenter">中下</option>
                                <option value="BottomRight">右下</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn" onclick="updateNode()">确定</button>
                        </div>
                    </div>
                </form>
            </div>
            <div id="myOverviewDiv">
                <canvas tabindex="0" width="150" height="150"></canvas>
            </div>
            <div id="statusBtn">
                <div>
                    <span class="statusName">RUN：</span> <span class="btn btnNornmal"></span>
                </div>
                <div>
                    <span class="statusName">DOWN：</span> <span class="btn btnFault"></span>
                </div>
                <div>
                    <span class="statusName">IDLE：</span> <span class="btn btnIDLE"></span>
                </div>
                <div>
                    <span class="statusName">Offline：</span> <span class="btn btnOffline"></span>
                </div>
            </div>
        </div>
    </div>
    <!-- 搜索设备 -->
    <form id="search-equip" class="layui-form" lay-filter="search-equip" style="display: none; padding: 10px;">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width:80px;">设备名称</label>
            </div>
            <div class="layui-inline">
                <input class="layui-input" type="text" name="equipName" autocomplete="off" placeholder="请输入设备名称" oninput="cleanSpelChar(this)">
            </div>
        </div>
    </form>
</body>
</html>

<script type="text/javascript" src="js/layout/config.js"></script>
<script type="text/javascript" src="js/layout/init.js"></script>
<script>
    var whLoading = new whLoading({
        id: 'loading',
        isShow: true,
    });
    var toolDatas = [];
    var equipMap = new Map();

    layui.use(['element', 'layer', 'jquery', 'form', "colorpicker"], function ()
    {
        $ = layui.$
            , form = layui.form
            , layer = layui.layer
            , colorpicker = layui.colorpicker
        lang.call(this, $);

        getAllEquip();
        colorpicker.render({
            elem: "#fillColor-form",
            done: function (color)
            {
                $("#fill-color").val(color);
            },
        });
        colorpicker.render({
            elem: "#strokeColor-form",
            done: function (color)
            {
                $("#stroke-color").val(color);
            },
        });

        form.render();
    })

    function getAllEquip()
    {
        AjaxApi.request({
            url: "../Config/list.do",
            data: { Type: 'Machine' },
            dataType: 'text',
            success: function (result)
            {
                var res = $.eval(result);
                toolDatas = res;
                setEquipOp();
            }
        })
    }
    var equipData = [
        { id: "1A3FAEE16D0D44899D8A1A765D95A24A", name: "YPCDH11PC" },
        { id: "1A3FAF0F13303F8186479898AF7B64B1", name: "YPCDH11CCTV" },
        { id: "1A3FAF3636A55321A750CBB4DEFC74FC", name: "YPCDH11Touch" },
        { id: "panel-5", name: "设备" },
        { id: "1A3F8022AB29CA13965594FCOBAA24D6", name: "YEOXT11PC" },
        { id: "1A3F8022A878C3B4ACC4D6F8A9528DB5", name: "YEOXT11PC#2" },
        { id: "1A3F804BE92548949E2F37DB7BED183A", name: "YEOXT11CCTV" },
        { id: "panel-9", name: "设备" },
        { id: "panel-910", name: "设备10" },
        { id: "panel-911", name: "设备11" },
        { id: "panel-912", name: "设备12" },
    ]
    equipData = window.top.window.toolDatas;
    function setEquipOp()
    {
        $("#equipname").html('');
        var op = '';
        for (var i = 0; i < toolDatas.length; i++)
        {
            op += "<option value='" + toolDatas[i].ID + "'>" + toolDatas[i].Name + "</option>";
            equipMap.set(toolDatas[i].ID, toolDatas[i]);
        }
        $("#equipname").html(op);
    }
    // console.log(111, window.top.window.toolDatas)
    // 设备更新当前状态颜色
    function changedColor(d)
    {
        for (var i = 1; i < panelData.length; i++)
        {
            var data = panelData[i];
            var name = data.title;
            if (name.indexOf(d.toolname) >= 0)
            {
                switch (d.status)
                {
                    case 'Down':
                        $('#header-' + data.id).css('background-color', '#dc3545');
                        break;
                    case 'IDLE':
                        $('#header-' + data.id).css('background-color', '#ffc107');
                        break;
                    case 'RUN':
                        $('#header-' + data.id).css('background-color', '#28a745');
                        break;
                }
            }
        }
    }
    // 搜索当前设备
    function searchEquip()
    {
        var index1 = layer.open({
            title: lang('搜索设备')
            , type: 1
            , resize: false
            , id: 'search'
            , content: $('#search-equip')
            , btn: [lang('确认'), lang('取消')]
            , btnAlign: 'c'
            , success: function ()
            {
                $(document).on('keydown', function (event)
                {
                    if (event.keyCode == 13)
                    {
                        $("*").blur();
                    }
                });
            }
            , btn1: function (value, index)
            {
                var data = form.val("search-equip");
                if (data.equipName.trim().length == 0)
                {
                    layer.alert(lang('设备名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
                    return false;
                }
                var result = [];
                newDatas.forEach(function (val)
                {
                    if (val.title.indexOf(data.equipName) !== -1)
                    {
                        result.push(val)
                    }
                })
                if (result.length > 0)
                {
                    var dropContainer = document.getElementsByClassName("container-wrapper")[0];
                    dropContainer.scrollTop = result[0].y - dropContainer.offsetHeight / 2;
                    dropContainer.scrollLeft = result[0].x - dropContainer.offsetWidth / 2;
                    layer.close(index1);
                } else
                {
                    layer.msg(lang('暂无该设备'), { icon: 3 });
                }

            }
            , end: function ()
            {
                layer.close(index1);
            }
        });
    }

    // 判断layout布局是否有变化
    function hasDataChanged(arr1, arr2)
    {
        var hasChanged = false;
        if (arr1.length !== arr2.length)
        {
            hasChanged = true;
        } else
        {
            for (let i = 0; i < arr1.length; i++)
            {
                if (arr1[i].id !== arr2[i].id) hasChanged = true;
                if (arr1[i].width !== arr2[i].width) hasChanged = true;
                if (arr1[i].height !== arr2[i].height) hasChanged = true;
                if (arr1[i].x !== arr2[i].x) hasChanged = true;
                if (arr1[i].y !== arr2[i].y) hasChanged = true;
            }
        }
        return hasChanged;
    }
    // 页面离开时判断
    window.onbeforeunload = function (event)
    {
        /*var oldDatas = JSON.parse(JSON.stringify(panelData));
        if (hasDataChanged(oldDatas, newDatas)) {
            event.preventDefault();
            event.returnValue = '';
            console.log('有变化')
            return
        }
        console.log('没有变化')*/
    }
</script>

<script type="text/javascript" src="js/layout/index.js"></script>