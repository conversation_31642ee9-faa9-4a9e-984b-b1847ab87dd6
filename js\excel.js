﻿// obj.data 导出的数据 obj.headerColumnLength 表头长度 obj.cellColumnLength 单元格长度 obj.setCellLengthStyle 单元格内容长度
function getDIYStyleExcel(tableId)
{
	var newObj = {};
	var headers = $('div[lay-id=' + tableId + '] .layui-table-box table').get(0);
	var hrs = Array.from(headers.querySelectorAll('tr'));
	var titles = {};
	for (var i = 0; i < hrs.length; i++)
	{
		var hths = Array.from(hrs[i].querySelectorAll('th'));
		for (var j = 0; j < hths.length; j++)
		{
			var clazz = $(hths[j]).attr('class');
			var fieldName = $(hths[j]).attr('data-field');
			if (fieldName.indexOf('radio') <= -1 && fieldName.indexOf('checked') <= -1 && clazz.indexOf('layui-table-col-special') <= -1 && clazz.indexOf('layui-hide') <= -1)
			{
				titles[fieldName] = hths[j].innerText;
			}
		}
	}
	//根据传入tableID获取table内容
	var bodys = $("div[lay-id=" + tableId + "] .layui-table-box table").get(1);
	var btrs = Array.from(bodys.querySelectorAll("tr"))
	var bodysArr = new Array();
	for (var j = 0; j < btrs.length; j++)
	{
		var contents = {};
		var btds = Array.from(btrs[j].querySelectorAll("td"));
		for (var i = 0; i < btds.length; i++)
		{
			for (var key in titles)
			{
				//修改:默认字段data-field+i,兼容部分数据表格中不存在data-field值的问题
				var field = $(btds[i]).attr('data-field');
				if (field === key)
					contents[field] = btds[i].innerText;//根据表头字段获取table内容字段
			}
		}
		bodysArr.push(contents)
	}
	//将标题行置顶添加到数组
	bodysArr.unshift(titles);
	//获取列长度 以及 内容长度
	var str = 'A';//记录列名初始位置
	var excelContentLengths = {};//记录列的单元格长度
	var columnsLength = 0;//记录列的数量
	for (var i = 0; i < bodysArr.length; i++)//遍历行
	{
		var item = bodysArr[i], c = 0, o = {};
		for (var v in item)//遍历列
		{
			var n = String.fromCharCode(str.charCodeAt() + c);
			c++;
			if (i > 0)
			{
				for (var iii in excelContentLengths)//遍历属性的值
				{
					if (iii == n)
						excelContentLengths[iii] = excelContentLengths[iii] > naturalLen(item[v]) ? excelContentLengths[iii] : naturalLen(item[v]);
				}
				continue;
			}

			excelContentLengths[n] = naturalLen(item[v]);//添加属性
		}
		columnsLength = c;
	}
	newObj.data = bodysArr;
	var cl = ''
	if (columnsLength > 26)
		cl = 'A' + String.fromCharCode(str.charCodeAt() + columnsLength - 26 - 1);
	else
		cl = String.fromCharCode(str.charCodeAt() + columnsLength - 1);
	newObj.headerColumnLength = str + '1:' + cl + '1';
	newObj.cellColumnLength = str + '2:' + cl + bodysArr.length;
	newObj.setCellLengthStyle = excelContentLengths;
	return newObj;
}

function exportDIYStyleExcel(tableId, excel,fileName)
{
	var data = getDIYStyleExcel(tableId);
	//设置列宽
	var colConf = excel.makeColConfig(data.setCellLengthStyle, 80);
	//表头
	excel.setExportCellStyle(data.data, data.headerColumnLength, {
		s: {
			fill: {
				bgColor: { indexed: 64 }, fgColor: { rgb: "BBFFEE" }
			},
			alignment: {
				horizontal: 'center',
				vertical: 'center'
			},
			border: {
				top: { style: "thin" },
				bottom: { style: "thin" },
				left: { style: "thin" },
				right: { style: "thin" }
			},
			font: { sz: '16', bold: true }
		}
	}, function (cell, newCell, row, config, currentRow, currentCol, fieldKey)
		{
			return newCell;
		});

	//表内容数据
	excel.setExportCellStyle(data.data, data.cellColumnLength, {
		s: {
			alignment: {
				horizontal: 'center',
				vertical: 'center'
			},
			border: {
				top: { style: "thin" },
				bottom: { style: "thin" },
				left: { style: "thin" },
				right: { style: "thin" }
			},
			font: { sz: '14', bold: false }
		}
	}, function (cell, newCell, row, config, currentRow, currentCol, fieldKey)
		{
			return newCell;
		});
	excel.exportExcel({ sheet1: data.data }, fileName, 'xlsx', {
		extend: {
			'!cols': colConf
		}
	});
}

function exportDIYStyleExcel2(tableId, excel, fileName,url,params) {
	var data = getDIYStyleExcel(tableId);
	//设置列宽
	var colConf = excel.makeColConfig(data.setCellLengthStyle, 80);
	//表头
	excel.setExportCellStyle(data.data, data.headerColumnLength, {
		s: {
			fill: {
				bgColor: { indexed: 64 }, fgColor: { rgb: "BBFFEE" }
			},
			alignment: {
				horizontal: 'center',
				vertical: 'center'
			},
			border: {
				top: { style: "thin" },
				bottom: { style: "thin" },
				left: { style: "thin" },
				right: { style: "thin" }
			},
			font: { sz: '16', bold: true }
		}
	}, function (cell, newCell, row, config, currentRow, currentCol, fieldKey) {
			return newCell;
		});

	//表内容数据
	excel.setExportCellStyle(data.data, data.cellColumnLength, {
		s: {
			alignment: {
				horizontal: 'center',
				vertical: 'center'
			},
			border: {
				top: { style: "thin" },
				bottom: { style: "thin" },
				left: { style: "thin" },
				right: { style: "thin" }
			},
			font: { sz: '14', bold: false }
		}
	}, function (cell, newCell, row, config, currentRow, currentCol, fieldKey) {
			return newCell;
		});
	excel.exportExcel2({ sheet1: data.data }, fileName, 'xlsx', {
		extend: {
			'!cols': colConf
		}
	}, url, params);
}
