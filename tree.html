﻿<!--
    **** vue虚拟滚动树表格 暂时不使用 不删除相关文件 ****
-->
<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>tree</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="js/tree/css/<EMAIL>" />
    <link rel="stylesheet" href="js/tree/main.css" />
    <!-- 引入vue -->
    <script src="js/tree/vue2.js"></script>
    <!-- 引入组件 -->
    <script src="js/tree/xe-utils.js"></script>
    <script src="js/tree/<EMAIL>"></script>
    <script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
    <script type="text/javascript" src="lib/pickgold.js?v=4"></script>
    <script type="text/javascript" src="local.config.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="user/loginMenu.dojs"></script>
    <script type="text/javascript" src="user/GetMenuJson.dojs"></script>
    <script type="text/javascript" src="js/index.js?v=1"></script>
    <script type="text/javascript" src="/myconfig.dojs?user=1"></script>
    <script src="js/request.js"></script>
</head>

<body style="background-color: #1f2d3d; margin: 0;">
    <div id="app">
        <ul class="tab-title">
            <li @click="tabClick(0)" :class="{active:cur==0}">区域</li>
            <li @click="tabClick(1)" :class="{active:cur==1}">机台组</li>
            <li @click="tabClick(2)" :class="{active:cur==2}">收藏夹</li>
        </ul>
        <div>
            <input class="inputValue" type="text" v-model="inputValue" placeholder="请输入机台名称" @input="searchData" />
            <div style="height: calc(100vh - 50px)">
                <vxe-table show-overflow
                           border
                           height="100%"
                           ref="xTable"
                           size="small"
                           :tree-config="{transform: true, rowField: 'id', parentField: 'pid', indent: 10, expandAll: true,}"
                           :row-config="{height: 30, isHover: true}"
                           :scroll-y="{enabled: true, gt: 30}"
                           :data="tableData"
                           :checkbox-config="checkConfig"
                           @checkbox-all="selectChangeAll"
                           @checkbox-change="selectChangeEvent">
                    <vxe-column type="checkbox" width="32"></vxe-column>
                    <vxe-column field="Name" :title="titlename" tree-node>
                        <template v-slot="scope">
                            <span v-if="scope.row.pid == 0 && !checkVideo(scope.row.Type)" style="color: red">{{ scope.row.Name }}</span>
                            <span v-else-if="!checkVideo(scope.row.Type)" style="color: green">{{ scope.row.Name }}</span>
                            <span v-else>{{ scope.row.Name }}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="date" title="操作" align="center" width="54">
                        <template #default="scope">
                            <vxe-button v-if="cur !== 2 && !scope.row.fav" mode="text" icon="vxe-icon-star" @click="AddToFav(scope.row)"></vxe-button>

                            <vxe-button v-if="cur !== 2 && scope.row.fav" mode="text" icon="vxe-icon-star-fill" @click="DeleteFromFav(scope.row)"></vxe-button>

                            <vxe-button v-if="cur == 2" mode="text" status="danger" icon="vxe-icon-delete" @click="DeleteFromFav(scope.row)"></vxe-button>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>
        </div>
    </div>
</body>

</html>
<script type="text/javascript" src="js/tree/main.js"></script>