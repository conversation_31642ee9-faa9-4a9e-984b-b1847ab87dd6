﻿/*!
 * toolBar.js v25.4.18
 *
 * Copyright 2030 Cowin
 * Author: <PERSON>
 * Editor: <PERSON>
 *
 * Date: 2025-04-18
 */
window.toolStart = [];
window.startTime = Date.now();
window.toolReady = function ()
{
	if (window.clientType == window.CLIENT_TYPE.Browser)//网页大画面
	{
		window.SID = window.top.opener.window.login.Alive;
		window.login = window.top.opener.window.login;
		window.video = window.parent.window.video;
		window.videoID = window.parent.window.videoID;
		window.videoIP = window.parent.window.videoIP;
		if (!window.videoIP)
		{
			window.videoIP = window.parent.window.videoUrl;
			if (!window.videoIP && !!window.video)
				window.videoIP = window.video.Url;
			if (!!window.videoIP)
				window.videoIP = window.videoIP.replace(/^[^\/]+\/\/([^\/]+)(\/.*)?/, '$1');
		}
		window.videoUrl = window.parent.window.videoUrl;
		window.videoRoot = window.parent.window.videoRoot;
		window.scheme = window.parent.window.scheme;
	}
	else if (window.clientType == window.CLIENT_TYPE.Edge)//在 Edge 客户端
	{
		window.SID = window.Data('User', null);
		window.videoID = window.Data("Video", null);
		window.videoIP = window.Data('VideoIP', null);
		window.videoUrl = window.Data('VideoUrl', null);
		window.videoRoot = window.Data('Primary', null);
		window.scheme = window.Data('Scheme', null);

		if (!!window.CS.noUserTitle || !window.login.Title)
			window.shell.ControlText('StatusUser', window.login.Name);
		else
			window.shell.ControlText('StatusUser', window.login.Title);
		window.shell.ControlText('StatusBar', '加载成功！');
	}
	else if (window.clientType == window.CLIENT_TYPE.CefIE)//在 Chrome 或 IE 客户端
	{
		window.SID = window.Data('User', null);
		window.videoID = window.Data("Video", null);
		window.videoIP = window.Data('VideoIP', null);
		window.videoUrl = window.Data('VideoUrl', null);
		window.videoRoot = window.Data('Primary', null);
		window.scheme = window.Data('Scheme', null);

		window.shell.ControlText('StatusUser', !window.login ? 'Debug' : !window.login.Title ? window.login.Name : window.login.Title);
		window.shell.ControlText('StatusBar', '加载成功！');
	}
	else
	{
		window.location.href = 'index.html';
	}
	if (!!window.login && !!window.login.Alive)
		window.SID = window.login.Alive;
	if (!!window.videoUrl)
	{
		if (!!window.Data)
		{
			var t = window.Data('VideoName', null);
			if (!!t)
				document.title = lang('机台： ') + t;
		}
		window.videoUri = new Uri(window.videoUrl);
	}

	window.search = window.Data('OpenVideo', '');
	if (!!window.search)
	{
		window.search = $.parseUrl(window.search);
		window.formIndex = window.search['OpenVideo'];
		window.formIndex = window.formIndex.replace(/^.*,(.+),.*$/, '$1');
		window.formIndex = window.search[window.formIndex];
		if (!window.formIndex)
		{
			window.Data('BALLOON', '3,OpenVideo,Index not input.,OpenVideo = ' + window.Data('OpenVideo', ''))
			window.Data('close');
			return;
		}

		//
	}
	window.toolState = {};
	if (!window.ownerSetted)
	{
		window.Value('Owner', 1);
		window.ownerSetted = 1;
	}
	if (!window.videoID)
		return;

	var ajax = {};
	ajax.url = '/video.do?User=0&Video=' + window.videoID;
	ajax.success = function (result)
	{
		if (!result)
			return;

		var vd = $.eval(result);
		console.log(result);
		if (!vd)
			return;

		if (vd.ID == window.videoID)
		{
			if (!window.video)
				window.video = vd;
			else
				for (var k in vd)
					window.video[k] = vd[k];
			if (!!window.Data)
				window.Data('.VideoData', result);
			window.ldb('(v)' + window.videoID + '.VideoData', result);
			window.ldb('#' + vd.ID, result);
			return;
		}

		if (!vd.slice)
			return;

		var vl = vd.length
		if (!vl)
			return;

		for (vl--; vl >= 0; vl--)
		{
			if (vd[vl].ID == window.videoID)
			{
				if (!window.video)
					window.video = vd[vl];
				else
					for (var k in vd[vl])
						window.video[k] = vd[vl][k];
				if (!!window.Data)
					window.Data('.VideoData', result);
				window.ldb('(v)' + window.videoID + '.VideoData', result);
				window.ldb('#' + vd.ID, JSON.stringify(vd[vl]));
				break;
			}
		}
		if (vd[0]['$:type'] == 'Auth')
		{
			window.video.auth = vd[0];
			window.video.authData = vd[0].Data;
			if (!!window.video.authData && !!window.video.authData.length)
			{
				if (window.video.authData == '*' || window.video.authData.indexOf(CS.VideoMenu.$Work) >= '0')
					window.video.authWork = 2;
				else if (window.video.authData == '*' || window.video.authData.indexOf(CS.VideoMenu.$Part) >= '0')
					window.video.authWork = 1;
			}
		}
		if (!!window.video && !!window.video.Data && window.video.Data.indexOf('AreaDenial') >= 0)
		{
			window.video.DataJson = $.parseUrl(window.video.Data);
			if (!!window.video.DataJson.AreaDenial)
			{
				window.video.AreaDenial = window.video.DataJson.AreaDenial;
				if (!!window.Data && (!window.video.authWork || window.video.authWork <= 1))
					window.Data('AreaDenial', window.video.AreaDenial);
			}
		}
	}
	ajax.error = function (q)
	{
		if (q.status == 400)
			window.Data('Close', lang('资源不存在，请与系统管理员联系！'));
		else if (q.status == 401)
			window.Data('Close', lang('您没有此项操作的权限，请与系统管理员联系！'));
	};
	$.ajax(ajax);
	ajax.success(window.ldb('#' + window.videoID));
	if (!window.videoRoot)
		window.videoRoot = window.videoID;
};
window.toolReady.call(window);

window.doIdle = function (data)
{
	if (!arguments.length)
		return;

	if (!data || isNaN(data) || data <= 0 || data >= 86400)
	{
		window.Value('Idle', 0);
		$('#tb-idle').data('.idle', 0);
		$('#tb-idle').hide();
		console.log('.idle', 0);
		return;
	}

	if (!$('#tb-idle').data('.idle'))
	{
		var v = $.eval(window.Data('Idle', data));
		window.Value('TH', dpi(80));
		window.Value('TW', dpi(680));
		if (v.Work > window.CS.ExtraData.STATUS_WAITING)
			$('#td-idle').text(lang('由于您长时间未操作，系统将释放操作权限。'));
		else
			$('#td-idle').text(lang('由于您长时间未操作，系统将关闭当前窗口。'));
		$('#tb-idle').show();
		$('#tb-idle').data('.idle', data);
	}
	$('#td-idle-time').text(data);
	console.log('.idle', data);
};
window.toolStart.push(window.doIdle);

window.doWork = function (data)
{
	if (!arguments.length)
		return;

	var j = $.eval(!window.Data ? "{}" : window.Data('MEMORY', ''));
	if (!j || !j.LockData)
		$('#img-work').attr('src', 'imgs/Work' + data + '0.png');
	else
		$('#img-work').attr('src', 'imgs/Work' + data + '1.png');
	if (data > window.CS.ExtraData.STATUS_WAITING)
		window.Data('Status', lang('开始操作……'));
	else if (data > window.CS.ExtraData.STATUS_NORMAL)
		window.Data('Status', lang('正在排队！'));
	else
		window.Data('Status', lang('停止操作！'));
	$('#img-work').data('Work', data);
};
window.toolStart.push(window.doWork);

window.doMouse = function (data)
{
	if (window.scheme != window.VideoData.UriSchemeMC &&
		window.scheme != window.VideoData.UriSchemeKX &&
		window.scheme != window.VideoData.UriSchemeVNC &&
		window.scheme != window.VideoData.UriSchemeVNCL &&
		window.scheme != window.VideoData.UriSchemeVNCR &&
		window.scheme != window.VideoData.UriSchemeATEN)
	{
		$('#a-mouse').remove();
		return;
	}

	if (!arguments.length)
	{
		if (!window.Data)
			return;

		data = window.Data('.mouse', '');
		if (!data)
			return;
	}
	else
	{
		window.Data('.mouse', data);
	}
	if (data == 'None')
		$('#img-mouse').attr('src', 'imgs/Mouse0.png');
	else
		$('#img-mouse').attr('src', 'imgs/Mouse1.png');
};
window.doCursor = window.doMouse;
window.toolStart.push(window.doMouse);

window.doRecord = function (data)
{
	if (window.scheme != window.VideoData.UriSchemeMC)
	{
		$('#a-record').remove();
		return;
	}

	if (!arguments.length)
	{
		if (!window.Data)
			return;

		data = window.Data('.record', '');
		if (!data)
			return;
	}
	else
	{
		data = window.Data('.record', data);
	}
	if (!data)//stop
	{
		if (!$('#img-record').data('Recording'))
			return '0';

		$('#img-record').data('Recording', 0);
		$('#img-record').attr('src', 'imgs/Record0.png');
		data = '未录屏';
	}
	else
	{
		if (!!$('#img-record').data('Recording'))
			return '1';

		if (!$('#img-record').length && !window.toolReady)
		{
			window.doRecord = function () { return '0'; };
			return '0';
		}

		$('#img-record').data('Recording', Date.now());
		$('#img-record').attr('src', 'imgs/Record1.png');
	}
	$('#img-record').attr('title', data.lang());
}
window.toolStart.push(window.doRecord);

window.doCamera = function (data)
{
	if (window.scheme != window.VideoData.UriSchemeMC)
	{
		$('#a-camera').remove();
		return;
	}

	if (!arguments.length)
		return;

	if (!data || isNaN(data))//stop
	{
		if (!$('#img-camera').data('Camera'))
			return '0';

		$('#img-camera').data('Camera', 0);
		$('#img-camera').attr('src', 'imgs/Camera0.png');
		return;
	}

	if (!!$('#img-camera').data('Camera'))
		return '1';

	if (!$('#img-camera').length && !window.toolReady)
	{
		window.doCamera = function () { return '0'; };
		return '0';
	}

	$('#img-camera').data('Camera', Date.now());
	$('#img-camera').attr('src', 'imgs/Camera1.png');
}
window.toolStart.push(window.doCamera);

window.doCodec = function (data)
{
	if (window.scheme != window.VideoData.UriSchemeMC)
	{
		$('#a-codec').remove();
		return;
	}

	if (!arguments.length)
	{
		if (!window.Data)
			return;

		data = window.Data('.codec', '');
		if (!data)
			return;
	}
	else
	{
		window.Data('.codec', data);
	}
	switch (data)
	{
		case 0:
			$('#a-codec').remove();
			break;
		case 1:
			$('#img-codec').attr('src', 'imgs/Decode1.png');
			break;
		default:
			$('#img-codec').attr('src', 'imgs/Decode2.png');
			break;
	}
};
window.doDecode = window.doCodec;
window.toolStart.push(window.doCodec);

window.doSide = function ()
{
	if (!arguments.length)
	{
		if (window.scheme == window.VideoData.UriSchemeMC)
		{
			if (!!$('#a-side').length)
			{
				var t = window.CS.sideUrl;//[customer].js
				if (!t)
					t = 'Side.html?' + t;
				else if (!t.length)
					t = null;
				else if (t.indexOf('?') > 0)
					t = window.CS.sideUrl + '&' + t;
				else
					t = window.CS.sideUrl + '?' + t;
				if (!t)
					$('#a-side').remove();
				else
					window.side = window.open(t, 'Side' + window.videoID);
			}
		}
		else if (window.scheme == window.VideoData.UriSchemeHK ||
			window.scheme == window.VideoData.UriSchemeONVIF)
		{
			if (!window.sideCCTV)
				$('#a-side').remove();
			else if (!!$('#a-side').length)
				window.side = window.open('sideCCTV.html?' + t, 'Side' + window.videoID);
		}
		else
		{
			$('#a-side').remove();
		}
		return;
	}

	if (!arguments[0])
		$('#a-side').remove();
};
window.toolStart.push(window.doSide);

window.doZoom = function (data)
{
	if (!arguments.length)
		return;

	switch (data)
	{
		case 0:
			$('#a-zoom').remove();
			break;
		case 1:
			$('#span-zoom').text(lang('自适应'));
			break;
		case 2:
			$('#span-zoom').text(lang('等比缩放'));
			break;
		case 3:
			$('#span-zoom').text(lang('原始尺寸'));
			break;
		default:
			$('#span-zoom').text('[ ' + data + ' ]');
			break;
	}
};
window.toolStart.push(window.doZoom);

window.doFull = function (data)
{
	if (!arguments.length)
	{
		if (!window.Data)
			return;

		data = window.Data('.full', '');
		if (!data)
			return;
	}
	else
	{
		window.Data('.full', data);
	}
	$('#img-full').data('value', data);
	if (isNaN(data) || data % 2 == 0)
	{
		$('#img-full').attr('src', 'imgs/FullScreen0.png');
		$('#span-full').text(lang('全屏显示'));
		return;
	}

	data = $('#p-end-0').offset().left - 2;
	if ($('#p-end-0').data('TW') != data)
	{
		window.Value('TW', data);
		$('#p-end-0').data('TW', data);
	}
	$('#img-full').attr('src', 'imgs/FullScreen1.png');
	$('#span-full').text(lang('退出全屏'));
};
window.doFullScreen = window.doFull;
window.toolStart.push(window.doFull);

window.doPTZ = function (data)
{
	if (!arguments.length)
		return;

	if (!arguments.length)
		data = storeToolState('#img-ptz0', 'PTZ', 8);
	else if (!!data)
		data = storeToolState('#img-ptz0', 'PTZ', 8, data);
	window.Value('PTZ', data);
	$('.ptz').css('color', 'black');
	$('#a-ptz' + data).css('color', 'red');
};
window.toolStart.push(window.doPTZ);

window.doPilot = function (data)
{
	if (!arguments.length)
	{
		if (!window.Data)
			return;

		data = window.Data('.pilot', '');
		if (!data)
			return;
	}
	else
	{
		window.Data('.pilot', data);
	}
	if (!!data)
	{
		$('#img-pilot').attr('src', 'imgs/Pilot1.png');
		$('#span-pilot').text(lang('近端'));
		$('#span-pilot').css('color', 'red');
	}
	else
	{
		$('#img-pilot').attr('src', 'imgs/Pilot0.png');
		$('#span-pilot').text(lang('远端'));
		$('#span-pilot').css('color', 'green');
	}
	$('#img-pilot').data('value', data);
};
window.toolStart.push(window.doPilot);

window.doOnvif = function ()
{
	if (!arguments.length)
		return;

	var vd = window.video;
	if (!vd || !vd.Url || !vd.DeviceState)
		return;

	try
	{
		var j = $.eval(vd.DeviceState);//{ "$:type": "", "Manufacturer": "HIKVISION", "Model": "DS-2CD2126FWDV3-IS", "FirmwareVersion": "V5.7.12 build 220819", "SerialNumber": "DS-2CD2126FWDV3-IS20221222AACHL11293452", "HardwareId": "88", "SnapshotUri": "http://10.171.120.121/onvif-http/snapshot?Profile_1", "StreamUri": "rtsp://10.171.120.121:554/Streaming/Channels/101?transportmode=unicast&profile=Profile_1", "ErrorCode": "200", "$:h": 1 }
		window.onvif = j;
		if (!j.StreamUri)
			return;

		var re = /^[^\:\/]+(\:\/\/[^\/]+@).+$/img;
		if (re.test(vd.Url) && !re.test(j.StreamUri))
			j.StreamUri = j.StreamUri.replace('://', vd.Url.replace(re, '$1'));
		if (window.clientType > window.CLIENT_TYPE.Browser)
			window.Data('Stream', j.StreamUri);
		window.doOnvif = false;
	}
	catch (x) { }
};
window.toolStart.push(window.doOnvif);

window.doText = function (data)
{
	if (!arguments.length)
		return;

	if (!!data)
		document.title = data;
};
window.toolStart.push(window.doText);

window.doSweepData = function (data)
{
	if (!arguments.length)
		return;
};
window.toolStart.push(window.doSweepData);

window.doPilotData = function (data)
{
	if (!arguments.length)
		return;
};
window.toolStart.push(window.doPilotData);

window.toolReady = function ()
{
	var t = location.search.toLocaleLowerCase();
	if (t.indexOf('?dev=') >= 0 || t.indexOf('&dev=') >= 0 ||
		t.indexOf('?debug=') >= 0 || t.indexOf('&debug=') >= 0)
	{
		window['is-debug'] = true;
		$('#devTool').show();
	}
	window.toolReady = null;
	delete window.toolReady;
	if (!window.toolbar)
		return;

	if (!window.scheme)
	{
		$('.tools').hide();
		return;
	}

	$('.loading').remove();
	if (!!window.CS.onready)
		window.CS.onready.call(this, window.$);
	window.videoUri.toLower();
	if (!window.Data)
		return;

	var j = window.toolStart;
	window.toolStart = Date.now();
	for (var i = 0; i < j.length; i++)
		j[i].call(window);
	j = $.eval(window.Data('View', null));
	window.Value('TH', dpi(50));
	window.Value('TW', dpi(680));
	window.Value('Active', 1);
	//Window Arrange
	$('#a-arrange').remove();
	//Window Bring
	$('#a-bring').remove();
	//Codec
	if (window.scheme != window.VideoData.UriSchemeMC)
		$('#a-codec').remove();
	//Record
	if (!!window.videoUrl && !window.videoID)
		$('#a-record').remove();
	//SyncMouse
	if (window.scheme != window.VideoData.UriSchemeMC || !window.CS.hasPilot)// || window.scheme == window.VideoData.UriSchemeATEN
		$('#a-sync-mouse').remove();
	//Lock
	if (!window.videoID)
		$('#a-lock').remove();
	//RPA
	if (!window.doRPA || !window.videoID)
		$('#a-rpa').remove();

	setInterval(function ()
	{
		var i = $('#img-full').data('value');
		if (!window.BGColor)
			window.BGColor = 1;
		else
			window.BGColor++;
		if (!i || i % 2 == 0)
			document.body.style.backgroundColor = ((window.BGColor % 2 == 0) ? '#87CEEB' : '#87CEEC');
		else
			document.body.style.backgroundColor = '#' + window.Data('BACKCOLOR', (window.BGColor % 2 == 0) ? '#87CEEB' : '#87CEEC');
	}, 384);
	document.body.style.backgroundColor = t = window.Data('BACKCOLOR', '#87CEEB');

	if (window.scheme == window.VideoData.UriSchemeHK ||
		window.scheme == window.VideoData.UriSchemeONVIF)
		window.Data('View', 'SDK');//
	j.TW = $('#p-end-0').offset().left - 2
	window.Value('TW', j.TW);
	$('#p-end-0').data('TW', j.TW);
};
