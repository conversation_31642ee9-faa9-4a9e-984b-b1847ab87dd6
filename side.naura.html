﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title>机台操作</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css" />
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="../user/loginMenu.docss" />
	<style type="text/css">
		body::-webkit-scrollbar {
			width: 4px;
			border-radius: 4px;
			background-color: lightskyblue;
			color: lightskyblue;
		}

		body::-webkit-scrollbar-thumb {
			background-color: deepskyblue;
		}

		th, td {
			border-bottom: 1px dashed bisque;
			padding: 4px;
		}

		.video-info {
			white-space: nowrap;
			width: 100%;
		}

			.video-info th {
				text-align: right;
			}

		.device-info {
			white-space: nowrap;
			width: 100%;
		}

			.device-info th {
				text-align: right;
				color: #000000d9;
			}

		.layui-collapse {
			margin-left: 5px;
		}

		.layui-colla-title {
			background-color: transparent;
			color: #000000d9;
			font-size: 17px;
		}

		h3 {
			text-align: center;
			text-decoration: underline;
			font-weight: bold;
		}

		#div-button {
			text-align: center;
			font-weight: bold;
		}

			#div-button div {
				width: 90%;
				height: 50px;
				line-height: 50px;
				border: 1px solid darkgreen;
				margin: 8px auto;
			}

		#lot-table tbody {
			display: block;
			height: 195px;
			overflow-y: scroll;
		}

			#lot-table thead,
			#lot-table tbody tr {
				display: table;
				width: 100%;
				table-layout: fixed;
			}

		#lot-table table thead {
			width: 100%;
		}

			#lot-table table thead th {
				background: #ccc;
			}

		#lot-table .lot-empty {
			height: 100%;
			line-height: 195px;
			color: #696969;
		}
	</style>
	<script type="text/javascript">
		window.webName = 'side';
	</script>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/request.js"></script>
	<script type="text/javascript" id="window.doWS(data)">
		window.doWS = function (data)
		{
			if (!data)
				return false;

			var t = data['$:type'];
			if (t == 'Video')
			{
				if (data.ID == window.videoID)
				{
					if (data.status > 200)
						return;

					if (!window.video)
						window.video = data;
					else
						for (var k in data)
							window.video[k] = data[k];
					window.doLiving.call(window, window.video);
				}
				else if (!!window.video && window.video.MultiID == data.ID)
				{
					if (data.status > 200)
						return;

					window.video['SweepData'] = data['SweepData'];
					window.video['PilotData'] = data['PilotData'];
					window.doLiving.call(window, window.video);
				}
				return true;
			}

			if (!data.indexOf)
				return;

			var i = data.indexOf('://');
			if (i <= 0)
				return false;

			t = data.substring(0, i);
			t = t.toLowerCase();
			if (t == 'extra')
			{
				if (!!window.extraData && !!window.extraData.onmessage)
					window.extraData.onmessage.call(window.extraData, data);
				return true;
			}

			if (t == 'pilot')
			{
				if (!!window.video && window.video.PilotData != data)
				{
					window.video.PilotData = data;
					window.doLiving.call(window, window.video);
				}
				return true;
			}

			if (t == 'lock')
			{
				if (data.split('/').length > 4)
				{
					if (!!window.video && window.video.LockData != data)
					{
						window.video.LockData = data;
						window.doLiving.call(window, window.video);
					}
				}
				else
				{
					if (!!window.video && !!window.video.LockData)
					{
						window.video.LockData = null;
						window.doLiving.call(window, window.video);
					}
				}
				return true;
			}

			if (t == 'sweep')
			{
				if (!!window.video && window.video.SweepData != data)
				{
					window.video.SweepData = data;
					window.doLiving.call(window, window.video);
				}
				return true;
			}

			if (t == 'oust')
				return false;

			return false;
		};
		window.doWs = window.doWS;
	</script>
	<script type="text/javascript" id="window.doTransmit(data)">
		window.doTransmit = function (data)
		{
			try
			{
				for (var i in data)
					$('#d_' + i).text(data[i]);
			}
			catch (x) { }
		};
	</script>
	<script type="text/javascript" id="window.doLiving(json, data, edit)">
		window.doLiving = function (json, data, edit)
		{
			if (data == 'doLiving')
			{
				if (!window.Data)
					return;

				var t = window.Data('.VideoData', '');
				if (!t)
					return;

				json = $.eval(t);
				if (!json)
					return;

				if (!window.video)
					window.video = json;
				else
					for (var k in json)
						window.video[k] = json[k];
				t = window.Data('.SweepData', '');
				if (!!t && t.indexOf('://') > 0)
					json.SweepData = t;
			}
			else if (!json)
			{
				if (!window.opener)
				{
					window.doLiving.call(this, json, 'doLiving', arguments.callee);
					return;
				}

				if (!!window.opener.window && !!window.opener.window.video)
				{
					json = window.opener.window.video;
					if (!json.ID)
						return;

					edit = window.opener.window.lastEdit;
				}
				else
				{
					return;
				}
			}

			if (!!json.ID)
			{
				var su = [[], [], [], []];
				window.videoData = json;
				$('#video_name').css('background-color', json.ID == json.videoRoot ? 'skyblue' : 'slateblue');
				$('#video_name').html(json.Name);
				$('#v_Name').html(json.Name);
				$('#v_Url').html(json.Url);
				requestLot.call(window);
				window.videoDevice = json.StateJson;
				data = json.SweepData;
				if (!!data)
				{
					edit = data.indexOf('?');
					if (edit > 0)
					{
						data = $.parseUrl(data.substring(edit + 1));
						if (!!data)
						{
							for (var k in data._values)
							{
								var t = data._values[k];
								if (!t.indexOf)
									su[3].push(t);
								else if (t.indexOf('/2/') > 0)
									su[2].push(t);
								else if (t.indexOf('/1/') > 0)
									su[1].push(t);
								else if (t.indexOf('/0/') > 0)
									su[0].push(t);
							}
						}
					}
				}
				if (!su[2].length)
				{
					$('#sweeper').html('No');//current worker
				}
				else
				{
					data = su[2][0];
					edit = data.lastIndexOf('/');
					if (edit > 0)
						data = data.substring(edit + 2, data.length);
					else
						data = 'No';
					$('#sweeper').html(data);//current worker
				}
				userList(su);
				$('#sweepers').html(su[1].length + ' / ' + su[0].length);//waiting's count
				json = window.videoDevice;
			}
			try
			{
				for (var i in json)
					$('#d_' + i).text(json[i]);
			}
			catch (x) { }
		}
	</script>
	<script type="text/javascript">
		$(window.document).ready(function ()
		{
			window.doLiving.call(window);
		});
		setInterval(function ()
		{
			if (window.iw == window.innerWidth)
				return;

			window.iw = window.innerWidth;
			if (window.iw > 50)
			{
				$('#div-information').show();
				$('#div-button').hide();
			}
			else
			{
				$('#div-information').hide();
				$('#div-button').show();
			}
		}, 200);

		window.onResize = function ()
		{

		};
	</script>
	<script type="text/javascript">
		function requestLot()
		{
			if (!!window.QueryID)
				return;

			var host = window.location.hostname
			if (host == '127.0.0.1')
				host = '************';//************
			else
				host = '*************';
			window.QueryID = 'Rcm_GetMachineLotList';
			window.QueryVersion = 'C0001';
			AjaxApi.request({
				//type: 'get',
				type: "put",
				url: "/oicapi/oicserver/query/all/" + window.QueryID + ":" + window.QueryVersion, //http://" + host + ":2025
				data: JSON.stringify({
					parameters: {
						FACTORYNAME: 'N8',
						MACHINENAME: window.videoData.Eqp//'AACE001'//
					}
				}),
				//url: "lot.json", // 假设本地 JSON 文件名为 data.json
				dataType: 'json', // 期待返回 JSON 格式的数据
				success: function (result)
				{
					var data = result.result;
					$('#tableTbody').empty();
					if (!data || data.length == 0)
					{
						var text = '<div class="lot-empty">暂无数据</div>'
						$('#tableTbody').append(text);
						return;
					}
					for (var i = 0; i < data.length; i++)
					{
						var text = '<tr><td style="color: darkslateblue;">' + data[i].LOTNAME + '</td></tr>'
						$('#tableTbody').append(text);
					}
				},
				error: function ()
				{
					var text = '<div class="lot-empty">暂无数据</div>'
					$('#tableTbody').append(text);
				}
			});
		}
		// RequestLot();

		function userList(arr)
		{
			$('#userList').empty();
			var newArr = arr[0].concat(arr[1]).concat(arr[2]);
			var Sweepers = [];
			for (var i = 0; i < newArr.length; i++)
			{
				var textArr = newArr[i].split('/');
				Sweepers.push({
					Name: textArr[6].substring(1),
					Value: Number(textArr[5])
				})
			}
			var trText = '';
			for (var i = Sweepers.length - 1; i >= 0; --i)
			{
				trText += '<tr><td>' + Sweepers[i].Name + '</td><td>'
				if (Sweepers[i].Value > window.CS.ExtraData.STATUS_WAITING)
				{
					trText += '操作中</td></tr>';
				} else if (Sweepers[i].Value == window.CS.ExtraData.STATUS_WAITING)
				{
					trText += '排队中</td></tr>';
				} else
				{
					trText += '观看中</td></tr>';
				}

			}
			$('#userList').append(trText);
		}
	</script>
</head>
<body style="background-color: skyblue;" onresize="javascript: onResize.call(window);">
    <form class="layui-form">
		<div class="layui-form-item" id="div-information">
			<h1 id="video_name" style="padding: 10px; text-align: center; width: 100%; position: fixed; top: 0px; background-color: skyblue; white-space: nowrap;">--</h1>
			<table id="lot-table" cellpadding="0" cellspacing="1" border="0" style="width: 100%; text-align: center; margin-top: 50px;">
				<thead>
					<tr>
						<th style="background-color: darkblue; color: white;">LotName</th>
						<!--<th style="background-color: darkblue; color: white;">Eha</th>
				<th style="background-color: darkblue; color: white;">Max</th>
				<th style="background-color: darkblue; color: white;">Flow</th>
				<th style="background-color: darkblue; color: white;">Pr</th>-->
					</tr>
				</thead>
				<tbody id="tableTbody"></tbody>
			</table>
			<hr />
			<table cellpadding="0" cellspacing="0" border="0" class="video-info">
				<caption><h3 style="text-align:left; padding-left: 100px;">设备状态</h3></caption>
				<tr>
					<th>设备名称：</th>
					<td id="v_Name">-</td>
				</tr>
				<tr>
					<th>当前操作人：</th>
					<td id="sweeper">-</td>
				</tr>
				<tr>
					<th>当前排队人数：</th>
					<td id="sweepers">-</td>
				</tr>
				<tr>
					<th>设备地址：</th>
					<td id="v_Url">-</td>
				</tr>
			</table>
			<table cellpadding="0" cellspacing="1" border="0" style="width: 100%; text-align: center;">
				<thead>
					<tr>
						<th style="background-color: darkblue; color: white;">Name</th>
						<th style="background-color: darkblue; color: white;">Status</th>
					</tr>
				</thead>
				<tbody id="userList"></tbody>
			</table>
			<hr />
			<div class="layui-collapse" lay-filter="test">
				<div class="layui-colla-item">
					<h3 class="layui-colla-title">设备信息<i class="layui-icon layui-colla-icon"></i></h3>
					<div class="layui-colla-content">
						<table cellpadding="0" cellspacing="0" border="0" class="device-info">
							<tr>
								<th>产品编号：</th>
								<td id="d_pn">-</td>
							</tr>
							<tr>
								<th>产品序列号：</th>
								<td id="d_sn">-</td>
							</tr>
							<tr>
								<th>固件版本：</th>
								<td id="d_APP_v">-</td>
							</tr>
							<tr>
								<th>编译时间：</th>
								<td id="d_buildtime">-</td>
							</tr>
							<tr>
								<th>BSP 版本：</th>
								<td id="d_BSP_v">-</td>
							</tr>
							<tr>
								<th>CPU 温度：</th>
								<td id="d_cpu_T">-</td>
							</tr>
							<tr>
								<th>CPU 占用率：</th>
								<td id="d_cpurate">-</td>
							</tr>
							<tr>
								<th>内存占用率：</th>
								<td id="d_memrate">-</td>
							</tr>
							<tr>
								<th>硬盘占用率：</th>
								<td id="d_rootfsdiskrate">-</td>
							</tr>
							<tr>
								<th>应用状态：</th>
								<td id="d_APP_status">-</td>
							</tr>
							<tr>
								<th>用户空间占用率：</th>
								<td id="d_userdatafsdiskrate">-</td>
							</tr>
							<tr>
								<th>应用空间占用率：</th>
								<td id="d_appfsdiskrate">-</td>
							</tr>
							<tr>
								<th>视频输入状态：</th>
								<td id="d_video_input">-</td>
							</tr>
							<tr>
								<th>视频分辨率：</th>
								<td id="d_v_resolution">-</td>
							</tr>
							<tr>
								<th>视频帧率：</th>
								<td id="d_v_fps">-</td>
							</tr>
							<tr>
								<th>视频扫描方式：</th>
								<td id="d_v_interlaced">-</td>
							</tr>
							<tr>
								<th>视频图像格式：</th>
								<td id="d_v_colorspace">-</td>
							</tr>
							<tr>
								<th>音频输入：</th>
								<td id="d_audio_input">-</td>
							</tr>
							<tr>
								<th>音频采样：</th>
								<td id="d_audio_sample">-</td>
							</tr>
							<tr>
								<th>主编码器状态：</th>
								<td id="d_encod_status">-</td>
							</tr>
							<tr>
								<th>辅编码器状态：</th>
								<td id="d_sub_encod_status">-</td>
							</tr>
							<tr>
								<th>编码帧率：</th>
								<td id="d_encod_fps">-</td>
							</tr>
							<tr>
								<th>Key Pad 状态：</th>
								<td id="d_interlock_state">-</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
		</div>
        <div class="layui-form-item" id="div-button">
            <div style="background-color: darkblue; color: yellow;">DO</div>
            <div style="background-color: darkgreen; color: yellow;">XO</div>
            <div style="background-color: darkgoldenrod; color: yellow;">3</div>
            <div style="background-color: darkkhaki; color: red;">14</div>
            <div style="background-color: aliceblue; color: blue;">87</div>
            <div style="background-color: darkcyan; color: yellow;">EO</div>
            <div style="background-color: darkblue; color: yellow;">55</div>
            <div style="background-color: aqua; color: black;">23</div>
            <div style="background-color: darkcyan; color: yellow;">FHN</div>
            <div style="background-color: khaki; color: orange;">49</div>
            <div style="background-color: cyan; color: darkred;">30</div>
            <div style="background-color: darkmagenta; color: aquamarine;">ESX</div>
        </div>
        <div id="devTool" style="color: red; position: absolute; display: none; right: 0; bottom: 0px; z-index: 999;">
            <span onclick="javascript: this.innerHTML = navigator.userAgent;"></span>
            |
            <a href="javascript: void(0);" onclick="javascript: top.window.Data('Dev', location.href);">Developer Tool</a>
            |
            <a href="javascript: window.location.reload();">Reload</a>
        </div>
    </form>
</body>
</html>