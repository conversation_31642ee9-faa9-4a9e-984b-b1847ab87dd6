﻿
* {
	margin: 0px;
	padding: 0px;
}

body {
	margin-top: 2px;
	background-color: rgb(165,165,165);
}
/*以下是背景色*/
input:focus, select:focus, button:focus, a:focus {
	/*outline: rgb(0,136,255) solid 2px;*/
	box-shadow: 0px 0px 0px 2px rgba(148,27,128,0.5);
}
/*以上是背景色*/


/* 以下 是 管理页面样式 */

.area {
	margin-top: 65px;
	margin-left: 20px;
}

.center {
	text-align: center;
	margin: 0px auto;
}
/* 以上 是 管理页面样式 */
/*页面背景色*/
.layui-layer-btn,
.layui-table-tool,
.layui-table,
.layui-card,
.layui-textarea,
.layui-card input,
.layui-elem-field,
.layui-table-box .layui-table-header,
.rootedit .layui-elem-field,
.layui-layer-page .layui-layer-content {
	background-color: rgb(165,165,165);
}
.cowin-click-color {
	background-color: rgb(165,165,165);
}
	.layui-elem-field .layui-textarea {
		background-color: #333;
		color: #fff;
	}

.update .layui-input {
	background-color: #fff;
}
/*分配页面*/
.layui-transfer,
.layui-transfer-header,
.layui-transfer-search,
.layui-transfer-search input,
.layui-transfer-data {
	background-color: rgb(165,165,165);
}

.setuser .layui-border-box,
.setuser .layui-border-box * {
	text-align: center;
}

.update .layui-form-checkbox[lay-skin=primary] span {
	margin-left: -90px;
}

/*.layui-form-radio > i:hover, .layui-form-radioed > i {
	color: rgb(148,27,128);
}*/

.cowin-menu .layui-checkbox-disbaled[lay-skin=primary] span {
	color: rgb(116,116,116);
}

.refresh .layui-table td[data-edit] {
	color: #fff;
}
.refresh .layui-form {
	padding: 5px 0 0 0;
	border-bottom: 1px solid #fff;
	border-left: none;
	border-right: none;
	border-top: 1px solid #fff;
}
/*群组分配*/
.rootedit .layui-elem-field .layui-field-box {
	padding: 10px;
	text-align: left;
}
/*tree*/
.rootedit .layui-field-box .auth-status {
	margin-left: -70px;
}

.auth-skin-default .auth-single .auth-skin:before {
	border-left: 0px;
}

.auth-skin-default .auth-single .auth-skin::after {
	border-top: 0px;
}
/*字体颜色*/
.layui-table-box .layui-table,
.layui-table-view .layui-table-page span,
.layui-form-label,
.layui-layer .layui-layer-title,
.rootedit .layui-elem-field,
.layui-field-box .layui-form-item span,
.layui-form-checkbox[lay-skin=primary] span {
	color: #333;
	font-size: 14px;
}

/*表头背景色*/
.layui-table thead tr {
	text-align: center;
	background-color: rgb(145,145,145);
}
/*鼠标移动的背景色*/
.layui-table tbody tr:hover,
.layui-table[lay-even] tr:nth-child(even):hover {
	color: #fff;
	background-color: rgb(148,27,128);
}
/*分页栏*/
.layui-table-view .layui-table-page {
	text-align:center;
	border: none;
}

.update .layui-table-page {
	position:absolute;
	height: 40px;
}

.layui-table-view {
	border-top: 0px;
}
.restart {
	margin:0px;
	padding:0px;
	border:none;
	height:0px;
}
.restart .layui-progress {
	text-align: center
}
	.restart .layui-table-view .layui-table-page{
		bottom:0px;
	}
	/*table 奇行的背景色*/
	.layui-table[lay-even] tr:nth-child(even) {
		background-color: #B9B9B9;
	}
.tool .cowin-inline1 {
		padding:10px 0;
	}
.cowin-inline3 {
	display: inline-block;
	margin-left: 70px;
}
/*弹框边框*/
.layui-layer .layui-layer-title {
	border: none;
	background-color: rgb(34,34,34);
}

.layui-layer-page .layui-layer-content {
	border-top: 0px;
	border-left: 5px solid #333;
	border-right: 5px solid #333;
}

.title {
	color: white;
	margin-left: -14px;
}

.layui-layer-btn {
	border: 5px solid #333;
}

.layui-layer-btn {
	border-top: none;
}

.layui-layer-hui .layui-layer-content {
	background-color: transparent;
}
/*提示*/
.delcontent {
	margin-left: 60px;
	margin-bottom: 10px;
}

.cowin-space1 {
	margin-left: 46px;
	margin-bottom: 10px;
}
.cowin-space2 {
	margin-left: 36px;
	margin-bottom: 10px;
}
.cowin-top {
	height: 19px;
}

.cowin-form-top {
	height: 50px;
}

.cowin-form-top0 {
	margin-top: 30px;
}

.cowin-form-top1 {
	height: 27px;
}

.cowin-form-top2 {
	height: 75px;
}
.cowin-form-top3 {
	height: 13px;
}
.cowin-form-right0 {
	margin-right:15px;
}
.cowin-form-right {
	margin-right: 10px;
}
.cowin-table-height1{
	height:600px;
} 
.cowin-user {
	margin-left: 22px;
}
/*单元格内容居中*/
.layui-table-cell {
	text-align: center;
}

.layui-form {
	padding: 5px 0 0 0;
	border-bottom: none;
	border-left: none;
	border-right: none;
}

.user .layui-form {
	text-align: center;
}

/*机台管理*/
.tool .layui-form-item {
	text-align: center;
}

.tool .layui-form-item,
.tool .layui-input {
	margin: 0px;
	padding: 0px;
}

.tool .layui-form-item {
	margin-left: 70px;
}

.cowin-inline {
	position: absolute;
	width: 100%;
	right: 0;
	bottom: 0;
	border: 3px solid #333;
}

.cowin-btn-item {
	position: absolute;
	width: 100%;
	right: 0;
	bottom: 20px;
}
/*条件居中*/
.layui-card .layui-elem-field,
.layui-card .layui-elem-field legend {
	text-align: center;
}

.update .layui-card .layui-elem-field {
	border-left: none;
	border-right: none;
	margin: 0px;
}
/*批量升级*/
.layui-elem-field .layui-textarea {
	height: 180px;
}

.layui-col-space5 {
	margin-top: -10px;
	padding: 0px;
}

.layui-form .layui-border-box .layui-table-view {
	margin-top: 10px;
}
.cowin-left-space{
	margin-left:40px;
}
.restart .layui-form-item {
	margin-bottom:-20px;
}
.restart .layui-table-view {
	margin-top:-6px;
}
/*批量升级 边框*/
.layui-col-md12 {
	border-top: none;
}
.update .layui-elem-field{
	border-left:0px;
	border-right:0px;
}
/*按钮样式*/
.layui-btn-danger {
	background-color: #ff5722;
}

.layui-btn-primary {
	background-color: #fff;
}

.cowin-font-yellow {
	color: yellow;
}

.cowin-bgc-yellow {
	float: left;
}

.cowin-left {
	display: inline;
	margin-left: 77px;
}

.cowin-green {
	color: green;
}

.cowin-red {
	color: red;
}
/*参数设定*/
.params {
	padding-left: 15px;
	padding-right: 15px;
}

.cowin-params {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	margin-bottom: 25px;
	text-align: center;
}

/*登录页面*/
div.cowin-w {
	position: relative;
	width: 800px;
	height: 500px;
	background-color: pink;
	margin: 50px auto;
}

.cowin-div {
	position: absolute;
	float: left;
	top: 50%;
	width: 300px;
	height: 200px;
	transform: translate(-50%,-50%);
}

div.dotta {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%,-50%);
	width: 8px;
	height: 8px;
	background-color: #fff;
	border-radius: 50%;
}

.city div[class^="pulse"] {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8px;
	height: 8px;
	transform: translate(-50%,-50%);
	box-shadow: 0 0 0 4px #09f;
	border-radius: 50%;
	animation: pulse 1.2s linear infinite;
}

.city div[class="pulse2"] {
	animation-delay: 0.4s;
}

.city div[class="pulse3"] {
	animation-delay: 0.8s;
}

div.cowin-login-left {
	left: 50%;
	margin-left: 200px;
}

div.cowin-login-right {
	perspective: 1000000px;
	right: 50%;
	width: 200px;
	border: 1px solid blue;
	margin-right: -66px;
	text-align: center;
	transform-style: preserve-3d;
	animation: rotate 10s linear infinite forwards;
}

	div.cowin-login-right:hover {
		animation-play-state: paused;
	}

	div.cowin-login-right img {
		position: absolute;
		width: 100px;
		height: 100px;
	}

.cowin-login-right img:nth-child(1) {
	transform: rotateY(60deg) translateZ(150px);
}

.cowin-login-right img:nth-child(2) {
	transform: rotateY(120deg) translateZ(150px);
}

.cowin-login-right img:nth-child(3) {
	transform: rotateY(180deg) translateZ(150px);
}

.cowin-login-right img:nth-child(4) {
	transform: rotateY(240deg) translateZ(150px);
}

.cowin-login-right img:nth-child(5) {
	transform: rotateY(300deg) translateZ(150px);
}

.cowin-login-right img:nth-child(6) {
	transform: rotateY(360deg) translateZ(150px);
}
/*table底边框*/
.layui-table-box {
	border-bottom: 1px solid rgb(241,241,241);
}
/*无数据时样式*/
.layui-table-body .layui-none {
	color: red;
}
@keyframes rotate {
	0% {
		transform: rotateY(0deg);
	}

	100% {
		transform: rotateY(360deg);
	}
}

@keyframes pulse {
	0% {
	}

	70% {
		width: 40px;
		height: 40px;
		opacity: 1;
	}

	100% {
		width: 70px;
		height: 70px;
		opacity: 0;
	}
}

@keyframes deg {
	0% {
	}

	25% {
		transform: rotate(90deg);
	}

	50% {
		transform: rotate(180deg);
	}

	75% {
		transform: rotate(270deg);
	}

	100% {
		transform: rotate(360deg);
	}
}