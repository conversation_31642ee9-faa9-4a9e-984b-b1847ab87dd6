﻿<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
		  content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>iRCM RPA Process页面</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css">
	<link href="css/common.css" rel="stylesheet" />
	<link rel="stylesheet" href="/user/loginMenu.docss" />
	<link rel="stylesheet" href="lib/layui/treetable/treetable.css" />
	<style>
		#addkvm .layui-inline {
			margin-right: 0px;
		}

		.edit {
			width: 200px;
		}

		.borderStyle {
			border: 1px solid red;
		}

		@media screen and (max-width: 450px) {
			.layui-form-item .layui-inline {
				display: inline-block;
				margin-bottom: 0px !important;
			}
		}

		.layui-form-switch {
			margin-top: -3px;
		}

		.txMark {
		}

		.txMarkHide {
			display: none;
		}
	</style>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script src="lib/layui/excel.js"></script>
	<script type="text/javascript" src="local.config.js"></script>
	<script src="js/common.js"></script>
	<script type="text/javascript" src="user/loginMenu.dojs"></script>
	<script type="text/javascript" src="js/layui-templet.js"></script>
	<script type="text/javascript">
		layui.config({ base: 'lib/layui/' })
			.extend({ treetable: 'treetable/treetable' })
			.use(['element', 'table', 'layer', 'jquery', 'form', 'laypage', 'excel', 'upload', 'treetable'], function () {
				$ = layui.$
					, form = layui.form
					, table = layui.table
					, layer = layui.layer
					, laypage = layui.laypage
					, upload = layui.upload
					, excel = layui.excel;

				lang.call(this, $);
				toolData = null;
				//table.init('tooltable', []);
				limitPageCount = 17;
				currentFirstIndex = 1;
				col = toolColumnsName();
				treetable = layui.treetable;
				////创建Table的列名
				//tableIni = table.render({
				//             elem: '#toollist'
				//	, even: true
				//	, sort: true
				//	, loading: false
				//             //, toolbar: '#tooltoolbar'
				//	, defaultToolbar: ['filter']
				//             , id: "toollist"
				//	, title: lang('Unit数据表')
				//	, limit: limitPageCount
				//	, cols: [col]
				//	,height: 'full-185'
				//	, data: []
				//});

				RenderTreeTable([]);
				if (window.deny.ToolOptionSet)
					$('#divBC').hide();
				pageCount = 1;
				FillDataTable();
				//头工具栏事件
				table.on('toolbar(tooltable)', function (obj) {
					var checkStatus = table.checkStatus(obj.config.id);
					switch (obj.event) {
						case 'getCheckData':
							var data = checkStatus.data;
							Delete(data);
							break;
						case 'isAdd':
							refreshAreaAndGroup('', '', '_pc');
							index = layer.open({
								title: lang('增加 PC')
								, type: 1
								, resize: false
								, id: "isAdd"
								, content: $('#addkvm')
								, btn: [lang('确认'), lang('取消')]
								, btnAlign: 'c'
								, success: function () {
								}
								, btn1: function (index) {
									var data = form.val("form-addkvm");
									if (data.name.trim().length == 0) {
										layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.ip.length == 0) {
										layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.toolgroupname == '不选' && data.areaname == '不选') {
										layer.alert(lang('请选择一个区域或一个机台组！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.toolgroupname == '不选')
										data.toolgroupname = "";

									if (data.areaname == "不选")
										data.areaname = "";

									data['toolgroupname'] = data.toolgroupname;
									data['areaname'] = data.areaname;

									data.type = CS.VideoData.TypeKVM;

									var ajax = {};
									ajax.url = '/Equipment/AddEquipment.do';
									ajax.data = data;
									ajax.dataType = 'text';
									ajax.cache = false;
									ajax.success = function (result) {
										var res = $.eval(result);
										if (res.code == "Success") {
											layer.alert(lang('保存成功,是否继续?'), {
												title: lang('增加 PC')
												, btn: [lang('继续'), lang('取消')]
												, btnAlign: 'c'
												, btn1: function (subIndex) {
													$('#addkvm')[0].reset();
													layer.close(subIndex);
													FillDataTable();
												}
												, btn2: function (subIndex) {
													$('#addkvm')[0].reset();
													layer.close(subIndex);
													layer.close(index);
													FillDataTable();
												}
											});
										}
										else {
											layer.alert(res.msg, { btn: [lang('确定')], title: lang("增加 PC"), });
										}
									};
									ajax.error = function (r) {
										layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加 PC"), });
									}
									$.ajax(ajax);
								}
								, end: function () {
									$('#addkvm')[0].reset();
									$('#addkvm').css('display', 'none');
								}
							});
							break;
						case 'ExportData':
							{
								exportExcelData();
								break;
							}
						case 'isAddCctv':
							refreshAreaAndGroup('', '', '_cctv');
							index = layer.open({
								title: lang('增加 CCTV')
								, type: 1
								, resize: false
								, id: "isAdd"
								, content: $('#addcctv')
								, btn: [lang('确认'), lang('取消')]
								, btnAlign: 'c'
								, success: function () {
								}
								, btn1: function (index) {
									var data = form.val("form-addcctv");
									if (data.name.trim().length == 0) {
										layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.ip.length == 0) {
										layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.toolgroupname == '不选' && data.areaname == '不选') {
										layer.alert(lang('请选择一个区域或一个机台组！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.toolgroupname == '不选')
										data.toolgroupname = "";

									if (data.areaname == "不选")
										data.areaname = "";

									data['toolgroupname'] = data.toolgroupname;
									data['areaname'] = data.areaname;

									data.type = CS.VideoData.TypeCCTV;

									var ajax = {};
									ajax.url = '/Equipment/AddEquipment.do';
									ajax.data = data;
									ajax.dataType = 'text';
									ajax.cache = false;
									ajax.success = function (result) {
										var res = $.eval(result);
										if (res.code == "Success") {
											layer.alert(lang('保存成功,是否继续?'), {
												title: lang('增加Unit')
												, btn: [lang('继续'), lang('取消')]
												, btnAlign: 'c'
												, btn1: function (subIndex) {
													$('#addcctv')[0].reset();
													layer.close(subIndex);
													FillDataTable();
												}
												, btn2: function (subIndex) {
													$('#addcctv')[0].reset();
													layer.close(subIndex);
													layer.close(index);
													FillDataTable();
												}
											});
										}
										else {
											layer.alert(res.msg, { btn: [lang('确定')], title: lang("增加 CCTV"), });
										}
									};
									ajax.error = function (r) {
										layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加 CCTV"), });
									}
									$.ajax(ajax);
								}
								, end: function () {
									$('#addcctv')[0].reset();
									$('#addcctv').css('display', 'none');
								}
							});
							break;
						case 'isAddTouch':
							refreshAreaAndGroup('', '', '_touch');
							index = layer.open({
								title: lang('增加 Touch Panel')
								, type: 1
								, resize: false
								, id: "isAdd"
								, content: $('#addtouch')
								, btn: [lang('确认'), lang('取消')]
								, btnAlign: 'c'
								, success: function () {
								}
								, btn1: function (index) {
									var data = form.val("form-addtouch");
									if (data.name.trim().length == 0) {
										layer.alert(lang('Device名称不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.ip.length == 0) {
										layer.alert(lang('DeviceIP不可为空！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.toolgroupname == '不选' && data.areaname == '不选') {
										layer.alert(lang('请选择一个区域或一个机台组！！！'), { btn: [lang('确定')], title: lang('提示') });
										return false;
									}

									if (data.toolgroupname == '不选')
										data.toolgroupname = "";

									if (data.areaname == "不选")
										data.areaname = "";

									data['toolgroupname'] = data.toolgroupname;
									data['areaname'] = data.areaname;

									data.type = CS.VideoData.TypeTouch;

									var ajax = {};
									ajax.url = '/Equipment/AddEquipment.do';
									ajax.data = data;
									ajax.dataType = 'text';
									ajax.cache = false;
									ajax.success = function (result) {
										var res = $.eval(result);
										if (res.code == "Success") {
											layer.alert(lang('保存成功,是否继续?'), {
												title: lang('增加Unit')
												, btn: [lang('继续'), lang('取消')]
												, btnAlign: 'c'
												, btn1: function (subIndex) {
													$('#addtouch')[0].reset();
													layer.close(subIndex);
													FillDataTable();
												}
												, btn2: function (subIndex) {
													$('#addtouch')[0].reset();
													layer.close(subIndex);
													layer.close(index);
													FillDataTable();
												}
											});
										}
										else {
											layer.alert(res.msg, { btn: [lang('确定')], title: lang("增加 Touch Panel"), });
										}
									};
									ajax.error = function (r) {
										layer.alert(lang('增加失败！'), { btn: [lang('确定')], title: lang("增加 Touch Panel"), });
									}
									$.ajax(ajax);
								}
								, end: function () {
									$('#addtouch')[0].reset();
									$('#addtouch').css('display', 'none');
								}
							});
							break;
						case 'BatchDelete':
							{
								col[col.length - 1].hide = false;
								table.reload('toollist', {
									cols: [col],
								})

								$('#normaldiv').hide();
								$('#batchdiv').show();

								break;
							}
						case 'CancelBatchDelete':
							{
								col[col.length - 1].hide = true;
								table.reload('toollist', {
									cols: [col],
								})

								$('#normaldiv').show();
								$('#batchdiv').hide();
								break;
							}
						case 'ConfirmBatchDelete':
							{
								BatchDelete();
								break;
							}
					};
				});

				function BatchDelete() {
					if (checkedItems.length == 0)
						return;

					layer.confirm(lang('是否进行批量删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
						var ajax = {};
						ajax.type = 'post';
						ajax.url = "/Equipment/DatchDeleteEquipment.do";
						ajax.data = JSON.stringify(checkedItems);
						ajax.cache = false;
						ajax.dataType = 'text';
						ajax.success = function (result) {
							var res = $.eval(result);
							if (res.code == "Success") {
								col[col.length - 1].hide = true;
								table.reload('toollist', {
									cols: [col],
								})

								$('#normaldiv').show();
								$('#batchdiv').hide();

								layer.alert(lang('批量删除成功'), { btn: [lang('确定')], title: lang('提示') });
							}
							else {
								layer.alert(lang('批量删除失败！'), { btn: [lang('确定')], title: lang("批量删除Unit"), });
							}
							FillDataTable();
						}
						$.ajax(ajax);
					});
				}

				/**
				 * 导出 Excel 数据
				 */
				function exportExcelData() {
					var ajax = {};
					ajax.url = "/tool/GetToolListAllExport.do";
					ajax.data = gData;
					ajax.cache = false;
					ajax.dataType = 'text';
					ajax.success = function (result) {
						var res = $.eval(result);

						col[col.length - 1].hide = true;
						//加载table数据
						table.reload('toollist', {
							data: refillData(res.data),
							limit: res.data.length,
							cols: [col],
							done: function (res, curr, count) {
								exportDIYStyleExcel('toollist', excel, lang('Unit设置.xlsx'));
							}
						});
					}
					$.ajax(ajax);
				}

				//监听行工具事件
				table.on('tool(tooltable)', function (obj) {
					var data = obj.data;
					if (obj.event === 'del') {
						Delete(data);
					}
					else if (obj.event === 'edit') {
						if (data.Role == '') {
							layer.alert(lang('此用户禁止修改！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}

						var elem = 'add' + data.Type.toLowerCase();

						var index1 = layer.open({
							title: lang('修改 ') + data.Type
							, type: 1
							, resize: false
							, id: 'edit'
							, content: $('#' + elem)
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {
								switch (data.Type) {
									case CS.VideoData.TypeKVM:
										refreshAreaAndGroup(data.areaname, data.unitname, '_pc');

										form.val('form-addkvm', {
											name: data.Name,
											mark: data.Landmark,
											ip: data.Url.replaceAll('/', '').split(':')[1],
											recording: data.Recording,
										});

										$('#addkvm input[name=name]').attr("disabled", true);
										$('#addkvm input[name=name]').addClass("layui-disabled");
										break;
									case CS.VideoData.TypeCCTV:
										refreshAreaAndGroup(data.areaname, data.unitname, '_cctv');

										form.val('form-addcctv', {
											name: data.Name,
											mark: data.Landmark,
											ip: data.Url.replaceAll('/', '').split(':')[2].split('@')[1],
											port: data.Url.replaceAll('/', '').split(':')[3],
											user: data.Url.replaceAll('/', '').split(':')[1],
											psd: data.Url.replaceAll('/', '').split(':')[2].split('@')[0],
										});

										$('#addcctv input[name=name]').attr("disabled", true);
										$('#addcctv input[name=name]').addClass("layui-disabled");
										break;
									case CS.VideoData.TypeTouch:
										refreshAreaAndGroup(data.areaname, data.unitname, '_touch');

										form.val('form-addtouch', {
											name: data.Name,
											mark: data.Landmark,
											ip: data.Url.replaceAll('/', '').split(':')[1],
											alias: data.Alias,
											recording: data.Recording,
										});

										$('#addtouch input[name=name]').attr("disabled", true);
										$('#addtouch input[name=name]').addClass("layui-disabled");
										break;
								}
							}
							, btn1: function (value, index) {
								var data1 = form.val("form-" + elem);

								data1["id"] = data.ID;
								data1['type'] = data.Type;

								if (data1.toolgroupname == '不选')
									data1.toolgroupname = "";

								if (data1.areaname == "不选")
									data1.areaname = "";

								data1['toolgroupname'] = data1.toolgroupname;
								data1['areaname'] = data1.areaname;

								var ajax = {};
								ajax.url = '/Equipment/UpdateEquipment.do';
								ajax.data = data1;
								ajax.dataType = 'text';
								ajax.cache = false;
								ajax.success = function (result) {
									var res = $.eval(result);
									if (res.code == "Success") {
										layer.msg(lang('修改成功'));
										layer.close(index1);
										FillDataTable();
									}
									else {
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
								$.ajax(ajax);
							}
							, end: function () {
								$('#' + elem)[0].reset();
								$('#' + elem).css('display', 'none');
								$('#' + elem + ' input[name=name]').attr("disabled", false);
								$('#' + elem + ' input[name=name]').removeClass("layui-disabled");
							}
						});
					}
					else if (obj.event === 'change') {
						//	img.src = 'http://ip:8001/mt/0/' + Date.now() + '.jpg';//1,2

						var index1 = layer.open({
							title: lang('修改 Recipe')
							, type: 1
							, resize: false
							, id: 'edit'
							, content: $('#changeRecipe')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () {

							}
							, btn1: function (value, index) {
								var recipe = form.val('form-changeRecipe');

								if (data.Type == 'CCTV')
									return;

								var ip = data.Url.toLowerCase().replace(/^.+:\/\/([^\/]+).*$/img, '$1');

								if (recipe.enable == undefined) {
									$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/0/' + Date.now() + '.jpg');
								}
								else {
									$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/' + recipe.recipe + '/' + Date.now() + '.jpg');
									//$('#recipe_img').attr('src', 'http://' + ip + ':8001/rpa/rpa/' + recipe.recipe + '/' + Date.now() + '.jpg');
								}

								layer.msg('已切换Recipe');

								layer.close(index1);
							}
							, end: function () {
							}
						});
					}
					else if (obj.event === 'addscript') {
						var index1 = layer.open({
							title: lang('添加脚本')
							, type: 1
							, resize: false
							, id: 'edit'
							, content: $('#addRpaScript')
							, btn: [lang('确认'), lang('取消')]
							, btnAlign: 'c'
							, success: function () { }
							, btn1: function (value, index) {
								var script = form.val('form-RpaScript');

								let a_data = {};
								a_data.toolid = data.ID;
								a_data.toolname = data.Name;
								a_data.script = script.script;
								a_data.note = script.note;

								var ajax = {};
								ajax.url = '/RPASetting/AddScript.do';
								ajax.data = a_data;
								ajax.dataType = 'text';
								ajax.cache = false;
								ajax.success = function (result) {
									var res = $.eval(result);
									if (res.code == "Success") {
										layer.msg(lang('修改成功'));
										layer.close(index1);
										FillDataTable();
									}
									else {
										layer.alert(lang('修改失败！'), { btn: [lang('确定')], title: lang('提示') });
									}
								}
								$.ajax(ajax);
							}
							, end: function () { }
						});
					}
					else if (obj.event === 'modifyScript') {
						//send message to index.html to pop up flowchar webpage
						//let toolname = table.cache.toollist.find(value => value.ID == data.Video).Name;
						//window.top.onFlowChartShow(data, toolname);

						window.open('http://**************:8080/', 'flowchart');
					}
					else if (obj.event === 'deleteScript') {
						layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
							let toolname = table.cache.toollist.find(value => value.ID == data.Video).Name;
							var ajax = {};
							ajax.url = '/RPASetting/DeleteScript.do';
							ajax.data = { id: data.ID, name: data.Name, toolname: toolname };
							ajax.dataType = 'text';
							ajax.success = function (result) {
								var res = $.eval(result);
								if (res.code == "Success") {
									layer.alert(lang('删除成功'), {
										btn: [lang('确定')],
										title: lang("删除脚本"),
									});
									FillDataTable();
								}
								else {
									layer.alert(lang('删除失败'), {
										btn: [lang('确定')],
										title: lang("删除脚本"),
									});
								}
							};
							ajax.error = function (r) {
								layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
							}
							$.ajax(ajax);
							layer.close(index);
						});
					}
					else if (obj.event == 'singleclick') {
						var checkCell = obj.tr.find('td[data-field="checked"]').find('.layui-form-checkbox');
						if (checkCell.length > 0) {
							checkCell.click();
						}
					}
				});

				var checkedItems = [];
				table.on('checkbox(tooltable)', function (obj) {
					if (obj.checked) {
						if (obj.type == 'all') {
							checkedItems = table.cache.toollist;
						} else {
							checkedItems.push(obj.data);
						}
					} else {
						if (obj.type == 'all') {
							checkedItems = null;
						} else {
							var i = 0;
							$.each(checkedItems, function (key, value) {
								if (value.id == obj.data.id) {
									checkedItems.splice(i, 1);
									return false;
								}
								i++;
							})
						}
					}
				});

				form.on('submit(toolSearch)', function (data) {
					FillDataTable();
					return false;
				});
				form.on('select(selRoleVideo)', function (data) {
					FillDataTable();
				})
				$('button[type=button]').click(function () {
					document.getElementById('form-select').reset();
					FillDataTable();
				})
				function toolColumnsName() {
					var columnsArray = [];
					var columnsNames = [
						{ title: lang("序号"), field: "xuhao" }
						, { title: lang("名称"), field: "Name" }
						, { title: lang("类型"), field: "Type" }
						, { title: lang("当前执行状态"), field: "" }
						//, { title: lang("所属区域"), field: "areaname" }
						//, { title: lang("所属机台组"), field: "unitname" }
						//, { title: lang("Url"), field: "Url" }
						//, { title: lang("地标"), field: "Landmark" }
						//, { title: lang("最后心跳"), field: "Pong", templet: templet }
						//, { title: lang("最后操作者"), field: "WatcherName" }
						, { title: lang('描述'), field: 'Note' }
					];

					for (var i = 0; i < columnsNames.length; i++) {
						var o = {
							title: columnsNames[i].title,
							field: columnsNames[i].field,
							align: 'center',
							event: 'singleclick',
							templet: columnsNames[i].templet,
							hide: columnsNames[i].hide
						};
						columnsArray.push(o);
					}

					var columnsLast = {
						minWidth: 70,
						title: lang("操作"),
						//toolbar: "#operation",
						event: 'singleclick',
						align: 'center',
						hide: !window.allow.ToolOptionSet,
						templet: function (item) {
							if (item.Type == 'Script') {
								return '<a id="' + item.ID + '" class="layui-btn layui-btn-xs layui-icon layui-icon-play" title="启动脚本" name="eventEdit" lay-event="ScriptOperate"></a>' +
									'<a id="' + item.ID + '" class="layui-btn layui-btn-xs layui-icon layui-icon-pause layui-bg-blue" title="暂停脚本" name="eventEdit" lay-event="ScriptOperate"></a>' +
									'<a id="' + item.ID + '" class="layui-btn layui-btn-xs layui-icon layui-icon-logout layui-bg-cyan" title="停止脚本" name="eventEdit" lay-event="ScriptOperate"></a>' +
									'<a class="layui-btn layui-bg-orange layui-btn-xs m_UserBC" name="eventEdit" lay-event="modifyScript">编辑脚本</a>' +
									'<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventEdit" lay-event="deleteScript">删除脚本</a>';
							}
							else {
								return '<a id="' + item.ID + '" class="layui-btn layui-btn-xs layui-icon layui-icon-pause layui-bg-blue" title="暂停脚本" name="eventEdit" lay-event="ScriptOperate"></a>' +
									'<a id="' + item.ID + '" class="layui-btn layui-btn-xs layui-icon layui-icon-logout layui-bg-cyan" title="停止脚本" name="eventEdit" lay-event="ScriptOperate"></a>' +
									'<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="addscript">添加脚本</a>';
							}
						}
					}
					columnsArray.push(columnsLast);

					var columnsLastAgain = {
						title: lang("选中"),
						type: 'checkbox',
						event: 'singleclick',
						align: 'center',
						hide: true,
					}
					columnsArray.push(columnsLastAgain);

					return columnsArray;
				}
				function Delete(data) {
					layer.confirm(lang('是否删除？'), { btn: [lang('确定'), lang('取消')], title: lang('提示') }, function (index) {
						var ajax = {};
						ajax.url = '/Equipment/DeleteEquipment.do';
						ajax.data = { id: data.ID, type: data.Type, name: data.Name };
						ajax.dataType = 'text';
						ajax.success = function (result) {
							var res = $.eval(result);
							if (res.code == "Success") {
								layer.alert(lang('删除成功'), {
									btn: [lang('确定')],
									title: lang("删除Device"),
								});
								FillDataTable();
							}
							else {
								layer.alert(lang('删除失败'), {
									btn: [lang('确定')],
									title: lang("删除Device"),
								});
							}
						};
						ajax.error = function (r) {
							layer.alert(lang(r.responseText), { btn: [lang('确定')], title: lang('提示') });
						}
						$.ajax(ajax);
						layer.close(index);
					});
				}

				form.on('select()', function (data) {
					$(".txMark").addClass("txMarkHide");

					if (data.value == 'NQVM') {
						$('#pc_record').html('<input type="radio" name="recording" value="W" title="观看时录屏"><input type="radio" name="recording" value="C" title="操作时录屏"><input type="radio" name="recording" value="A" title="始终录屏">');
					}
					else if (data.value == 'KVM') {
						$('#pc_record').html('<input type="radio" name="recording" value="N" title="手动录屏" checked=""><input type="radio" name="recording" value="W" title="观看时录屏"><input type="radio" name="recording" value="C" title="操作时录屏"><input type="radio" name="recording" value="A" title="始终录屏">');
					}
					else if (data.value == "NQVM_") {
						$('#pc_record').html('<input type="radio" name="recording" value="W" title="观看时录屏"><input type="radio" name="recording" value="C" title="操作时录屏"><input type="radio" name="recording" value="A" title="始终录屏">');
						$(".txMark").removeClass("txMarkHide");
					}

					form.render();
				});
			});

		var col = null;

		//选择导入文件
		function initupload() {
			upload.render({
				elem: '#LAY-excel-upload' //绑定元素
				, auto: false //选择文件后不自动上传
				, accept: 'file'
				, choose: function (obj) {// 选择文件回调
					var files = obj.pushFile()
					var fileArr = Object.values(files)// 注意这里的数据需要是数组，所以需要转换一下
					// 用完就清理掉，避免多次选中相同文件时出现问题
					for (var index in files) {
						if (files.hasOwnProperty(index)) {
							delete files[index]
						}
					}
					$('#LAY-excel-upload').next().val('');
					init(fileArr);
				}
			});
		}
		//导入数据
		function init(files) {
			excel.importExcel(files, {}, function (data) {
				data = excel.filterImportData(data, {
					'Name': 'A'
					, 'Area': 'B'
					, 'Unit': 'C'
					, 'LandMark': 'D'
					, 'Url': 'E'
					, 'Data': 'F'
				});

				if (data[0].Sheet1.length > 0) {
					data[0].Sheet1.splice(0, 1);
					if (data[0].Sheet1.length > 200) {
						layer.alert(lang('导入数据一次最多200行'), { btn: [lang('确定')], title: lang('提示') });
						return;
					}
					for (var i = 0; i < data[0].Sheet1.length; i++) {
						if ($.trim(data[0].Sheet1[i].Name + '') == '') {
							layer.alert(lang('Unit名称不可为空！'), { btn: [lang('确定')], title: lang('提示') });
							return;
						}

						switch (data[0].Sheet1[i].Data) {
							case '始终录屏':
								data[0].Sheet1[i].Data = '&Recording=A';
								break;
							case '操作时录屏':
								data[0].Sheet1[i].Data = '&Recording=C';
								break;
							case '观看时录屏':
								data[0].Sheet1[i].Data = '&Recording=W';
								break;
							case '手动录屏':
								data[0].Sheet1[i].Data = '&Recording=N';
								break;
						}

					}
				}
				$.ajax({
					type: "post",
					url: "../Tool/ImportData.do",
					data: JSON.stringify(data[0].Sheet1),
					dataType: 'text',
					cache: false,
					success: function (result) {
						var res = $.eval(result);
						var names = '';
						if (!!res.msg) {
							var msgArr = res.msg.split('|');
							if (msgArr.length > 1) {
								res.msg = msgArr[0];
								names = msgArr[1];
							}
						}
						if (res.code == "Success") {
							layer.alert(lang('导入成功'), { btn: [lang('确定')], title: lang('提示') });
						}
						else if (res.msg == "Exist") {
							layer.alert(names + lang(' Unit已存在！'), { btn: [lang('确定')], title: lang("增加Unit"), });
						}
						else {
							layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加Unit"), });
						}
						FillDataTable();
					}
				});
			});
		}
		var username = '';
		var gData = '';
		function initRoleSelect() {
			if (configData == null)
				return;
			for (var con = 0; con < configData.length; con++) {
				var key = configData[con].Name;
				var value = key.split('/');
				if (value.length > 1) {
					$("#selRole").append("<option value='" + key + "'>" + value[1] + "</option>");
				}
			}
			form.render('select');
		}
		function FillDataTable() {
			var data = form.val('form-select');
			if (data.type == '-')
				delete data.type;

			gData = { pageCount: 0, limitCount: limitPageCount, namelike: data.name, type: data.type };
			$.ajax({
				title: ''
				, url: "/Equipment/GetEquipment.do"
				, data: gData
				, cache: false
				, dataType: 'text'
				, success: function (result) {
					var res = $.eval(result);
					res.data = res.data.filter(value => value.Type != CS.VideoData.TypeCCTV);
					var res_data = refillData(res.data);
					var res_count = res_data.length;
					res_data.forEach(value => value.Video = "");

					new Promise((resolve) => {
						var ajax = {};
						ajax.url = "/RPASetting/GetAllScript.do";
						ajax.data = {};
						ajax.dataType = 'text';
						ajax.cache = false;
						ajax.success = function (result) {
							let table_data = $.eval(result);
							if (table_data.data.length != 0) {
								table_data.data.forEach(value => value.Type = "Script");
								res_data = res_data.concat(table_data.data);
							}

							res_data.forEach(value => $.parseUrl(value.Data, value));

							resolve(res_data);
						};
						$.ajax(ajax);
					}).then((table_data) => {
						laypage.render({
							elem: 'footer'
							, count: res_count
							, theme: '#FF5722'
							, limit: limitPageCount
							, prev: lang('上一页')
							, next: lang('下一页')
							, layout: ['count', 'prev', 'page', 'next', 'skip']
							, jump: function (obj, first) {
								pageCount = obj.curr;
								if (first) {
									col[col.length - 1].hide = true;
									//加载table数据
									RenderTreeTable(table_data);
								}
								else {
									setTable({ pageCount: pageCount - 1, limitCount: limitPageCount, namelike: data.name, type: data.type });
								}
								laypageEN();//翻页翻译成英文方法
							}
						});
					});
				}
			})
		}

		function setTable(data) {
			$.ajax({
				title: ''
				, url: "/Equipment/GetEquipment.do"
				, data: data
				, cache: false
				, dataType: 'text'
				, success: function (result) {
					var res = $.eval(result);
					var res_data = refillData(res.data);
					var res_count = res.count;
					var gdata = { ids: '' };
					for (var i = 0; i < res_data.length; i++) {
						if (!res_data[i].Watcher)
							continue;
						else if (gdata.ids.indexOf(res_data[i].Watcher) >= 0)
							continue;

						gdata.ids += ',' + res_data[i].Watcher;
					}

					$.ajax({
						url: "/User/GetUserListByID.do"
						, data: gdata
						, cache: false
						, dataType: 'text'
						, success: function (result) {
							var res = $.eval(result);

							if (!!res.data && !!res.data.length)
								res_data.forEach((item, index, arr) => {
									var res_item = res.data.filter((sub, index, arr) => item.Watcher == sub.ID);
									if (res_item.length > 0) {
										item.WatcherName = res_item[0].Name;
									}
								});

							col[col.length - 1].hide = true;
							//加载table数据
							treetable.reload('toollist', {
								data: res_data,
								page: false,
								cols: [col],
								done: function (res, curr, count) {
									if (window.deny.ToolOptionSet)
										$('#divBC').hide();
								}
							});
						}
					});
				}
			})
		}
		function show() {
			if ($('#pwd').val().length == 0)
				$($('p')[0]).css("visibility", "visible");
			else
				$($('p')[0]).css("visibility", "hidden");
		}

		var arealistData = [];
		var toolgrouplistData = [];

		function refreshAreaAndGroup(area, unit, id) {
			var ajax = {};
			ajax.url = "/Tool/GetAreaAndGroupListAll.do";
			ajax.data = {};
			ajax.dataType = 'text';
			ajax.cache = false;
			ajax.success = function (result) {
				var res = $.eval(result);

				if (res.arealist.length == 0 && res.toolgrouplist.length == 0) {
					//layer.alert(lang('导入失败！'), { btn: [lang('确定')], title: lang("增加Unit"), });
				}
				else {
					arealistData = res.arealist;
					toolgrouplistData = res.toolgrouplist;

					findRoot();

					if (id !== '')
						setAreaAndGroup(area, unit, id);
				}
			};
			$.ajax(ajax);
		}

		function findRoot() {
			for (var i = 0; i < arealistData.length; i++) {
				var item = arealistData[i];
				if (arealistData.filter(value => value.Parent == item.ID).length > 0) {
					arealistData.splice(i, 1);
					i--;
				}
			}

			for (var i = 0; i < toolgrouplistData.length; i++) {
				var item = toolgrouplistData[i];
				if (toolgrouplistData.filter(value => value.Parent == item.ID).length > 0) {
					toolgrouplistData.splice(i, 1);
					i--;
				}
			}
		}

		function setAreaAndGroup(area, unit, id) {
			var arealist = arealistData;
			var toolgrouplist = toolgrouplistData;

			$("#areaname" + id).empty();
			$("#toolgroupname" + id).empty();

			$("#areaname" + id).append("<option>不选</option>");

			//var rootarealist = arealist.filter((item, index, arr) => item.Parent === '');
			//setArea(area, id, rootarealist);

			for (var i = 0; i < arealist.length; i++) {
				var value = '';
				var name = arealist[i].Name;
				if (arealist[i].Parent.length != 0)
					value = arealist[i].Parent + arealist[i].ID;
				else
					value = arealist[i].ID + arealist[i].ID;

				var option = '';
				if (!!area && !!area.length && area == name)
					option = "<option selected value='" + value + "'>" + name + "</option>";
				else
					option = "<option value='" + value + "'>" + name + "</option>";

				$("#areaname" + id).append(option);
			}

			$("#toolgroupname" + id).append("<option>不选</option>");
			for (var i = 0; i < toolgrouplist.length; i++) {
				var value = '';
				var name = toolgrouplist[i].Name;
				if (toolgrouplist[i].Parent.length != 0)
					value = toolgrouplist[i].Parent + toolgrouplist[i].ID;
				else
					value = toolgrouplist[i].ID + toolgrouplist[i].ID;

				var option = '';
				if (!!unit && !!unit.length && unit == name)
					option = "<option selected value='" + value + "'>" + name + "</option>";
				else
					option = "<option value='" + value + "'>" + name + "</option>";

				$("#toolgroupname" + id).append(option);
			}

			form.render('select');
		}

		function setArea(area, id, arr) {
			for (var rI = 0; rI < arr.length; rI++) {
				var root = arr[rI];
				var sublist = arealistData.filter(item => item.Parent == root.ID);

				if (sublist.length != 0) {
					$("#areaname" + id).append('<optgroup label="' + root.Name + '">');
					setArea(area, id, sublist);
				}
				else {
					var value = '';
					var name = root.Name;
					if (root.Parent.length != 0)
						value = root.Parent + root.ID;
					else
						value = root.ID + root.ID;

					var option = '';
					if (!!area && !!area.length && area == name)
						option = "<option selected value='" + value + "'>" + name + "</option>";
					else
						option = "<option value='" + value + "'>" + name + "</option>";

					$("#areaname" + id).append(option);
				}
			}
		}

		function refillData(data) {
			for (var i = 0; i < data.length; i++) {
				//$.parseUrl(data[i].Data, data[i]);
				//$.parseUrl(data[i].Data9, data[i]);

				data[i].xuhao = i + 1;

				//switch (data[i].Recording) {
				//	case 'A':
				//		data[i].RecordingName = '始终录屏';
				//		break;
				//	case 'C':
				//		data[i].RecordingName = '操作时录屏';
				//		break;
				//	case 'W':
				//		data[i].RecordingName = '观看时录屏';
				//		break;
				//	case 'N':
				//		data[i].RecordingName = '手动录屏';
				//		break;
				//}
			}

			return data;
		}

		function RenderTreeTable(data) {
			if (!!treetable) {
				treetable.render({
					treeColIndex: 1,
					treeSpid: '',
					treeIdName: 'ID',
					treePidName: 'Video',
					treeDefaultClose: true,
					treeLinkage: false,
					elem: '#toollist',
					id: 'toollist',
					//toolbar: '#areatoolbar',
					cols: [col],
					page: false,
					sort: true,
					defaultToolbar: [],
					data: data,
					height: 'full-185',
					done: function (res, curr, count) {
						initupload();
					}
				});

				//if (window.deny.AreaOptionSet)
				//	$('#divBC').hide();
			}
		}
	</script>
</head>
<body style="background-color: white; overflow-x: auto;">
	<form class="layui-form" lay-filter="form-select" id="form-select" style="min-width: 800px; min-height: 600px; margin: 5px; ">
		<fieldset class="layui-elem-field">
			<legend><span>RPA 流程设置</span></legend>
			<div class="layui-field-box" style="text-align:center;">
				<div class="layui-form-item">
					<div class="layui-inline">
						<img id="recipe_img" style="width:1px; height:1px;" />
						<lable class="layui-form-label" style="width:85px;">Device名称：</lable>
					</div>
					<div class="layui-inline">
						<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
							  oninput="cleanSpelChar(this)">
					</div>
					<div class="layui-inline">
						<lable class="layui-form-label" style="width:85px;">Device类型：</lable>
					</div>
					<div class="layui-inline">
						<select name="type" id="type" lay-search="">
							<option value="-" selected="selected">-</option>
							<option value="KVM">PC</option>
							<option value="Touch">TouchPanel</option>
						</select>
					</div>
					<div class="layui-inline">
						<button type="submit" class="layui-btn" lay-filter="toolSearch" lay-submit=""><span>查询</span></button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn"><span>重置</span></button>
					</div>
				</div>
			</div>
		</fieldset>
		<table class="layui-hide" id="toollist" lay-filter="tooltable"></table>
		<div class="layui-inline" style="display:none;">
			<table class="layui-hide" id="userListEx" lay-filter="userVideoEx"></table>
		</div>
		<script type="text/html" id="tooltoolbar">
			<div class="layui-btn-container" style="float:left;">
				<div id="normaldiv" class="layui-inline">
					<div id="divBC" class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAdd">{{lang('增加 PC')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAddCctv">{{lang('增加 CCTV')}}</button>
						<button type="button" class="layui-btn layui-btn-sm m_UserBC" lay-event="isAddTouch">{{lang('增加 Touch Panel')}}</button>
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="BatchDelete" id="batchdeleteBtn">{{lang('批量删除')}}</button>
					</div>
				</div>
				<div class="layui-inline" id="batchdiv" style="display:none" name="batchdiv">
					<div class="layui-inline">
						<button type="button" class="layui-btn-danger layui-btn layui-btn-sm" lay-event="ConfirmBatchDelete" id="batchdeleteConfirmBtn">{{lang('批量删除确定')}}</button>
					</div>
					<div class="layui-inline">
						<button type="button" class="layui-btn layui-btn-sm" lay-event="CancelBatchDelete" id="batchdeleteCancelBtn">{{lang('取消')}}</button>
					</div>
				</div>
			</div>
		</script>
		<script type="text/html" id="operation">
			<!--<a class="layui-btn layui-btn-xs m_UserBC" name="eventChange" lay-event="change">{{lang('切换Recipe')}}</a>-->
			<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="addscript">{{lang('添加脚本')}}</a>
			<!--<a class="layui-btn layui-btn-xs m_UserBC" name="eventEdit" lay-event="edit">{{lang('修改')}}</a>-->
			<!--<a class="layui-btn layui-btn-danger layui-btn-xs m_UserBC" name="eventDel" lay-event="del">{{lang('删除')}}</a>-->
		</script>
		<script type="text/html" id="checkedOne">
			<input type="checkbox" name="like1[write]" lay-skin="primary">
		</script>
		<div id="footer" style="text-align:center;"></div>
	</form>

	<form id="addkvm" class="layui-form" lay-filter="form-addkvm" style="display: none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device类型</label>
			</div>
			<div class="layui-inline edit">
				<select name="scheme" id="scheme" lay-search="">
					<option value="MC" selected="selected">MC</option>
					<option value="NQVM">RCS</option>
					<option value="NQVM_">OF</option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaname" id="areaname_pc" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupname" id="toolgroupname_pc" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device地标位置</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">DeviceIP地址</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item txMark txMarkHide">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device 标号</label>
			</div>

			<div class="layui-inline">
				<input type="text" class="layui-input" name="signal" autocomplete="off" placeholder="请输入Device标号"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device录屏模式</label>
			</div>
			<div id="pc_record" class="layui-inline edit" lay-filter="pcrecord">
				<input type="radio" name="recording" value="N" title="手动录屏" checked="">
				<input type="radio" name="recording" value="W" title="观看时录屏">
				<input type="radio" name="recording" value="C" title="操作时录屏">
				<input type="radio" name="recording" value="A" title="始终录屏">
			</div>
		</div>
	</form>

	<form id="addcctv" class="layui-form" lay-filter="form-addcctv" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device类型</label>
			</div>
			<div class="layui-inline edit">
				<select name="scheme" id="scheme" lay-search="">
					<option value="TPLink" selected="selected">TPLink</option>
					<option value="HK">HK</option>
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaname" id="areaname_cctv" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupname" id="toolgroupname_cctv" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device地标位置</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">DeviceIP/Port地址</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
					   oninput="cleanNoIPADRESS(this)">

				<input type="number" style="margin-top: 10px;" class="layui-input" name="port" autocomplete="off" placeholder="请输入Device端口地址"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">用户名/密码</label>
			</div>
			<div class="layui-inline">
				<input type="text" class="layui-input" name="user" autocomplete="off" placeholder="请输入用户名"
					   oninput="cleanNoIPADRESS(this)">
				<input type="text" style="margin-top: 10px;" class="layui-input" name="psd" autocomplete="off" placeholder="请输入密码"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>
	</form>

	<form id="addtouch" class="layui-form" lay-filter="form-addtouch" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="name" autocomplete="off" placeholder="请输入Device名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的区域</label>
			</div>
			<div class="layui-inline edit">
				<select name="areaname" id="areaname_touch" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device属于的组</label>
			</div>
			<div class="layui-inline edit">
				<select name="toolgroupname" id="toolgroupname_touch" lay-search="">
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device地标位置</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="mark" autocomplete="off" placeholder="请输入Device地标位置"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device IP 地址</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="ip" autocomplete="off" placeholder="请输入DeviceIP地址"
					   oninput="cleanNoIPADRESS(this)">
			</div>
		</div>

		<!--<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label" style="width:119px;">Touch IP 地址</label>
		</div>
		<div class="layui-inline edit">
			<input type="text" class="layui-input" name="alias" autocomplete="off" placeholder="请输入DeviceTouch IP地址"
				   oninput="cleanNoIPADRESS(this)">
		</div>
	</div>-->

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Device录屏模式</label>
			</div>

			<div class="layui-inline edit">
				<input type="radio" name="recording" value="N" title="手动录屏" checked="">
				<input type="radio" name="recording" value="W" title="观看时录屏">
				<input type="radio" name="recording" value="C" title="操作时录屏">
				<input type="radio" name="recording" value="A" title="始终录屏">
			</div>
		</div>
	</form>

	<form id="changeRecipe" class="layui-form" lay-filter="form-changeRecipe" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">是否启用RPA</label>
			</div>
			<div class="layui-inline edit">
				<input type="checkbox" name="enable" lay-skin="switch" lay-text="启用|关闭" title="">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">Recipe Type</label>
			</div>
			<div class="layui-inline edit">
				<select name="recipe" id="scheme" lay-search="">
					<option value="1" selected="selected">Recipe1</option>
					<option value="2">Recipe2</option>
					<option value="3">Recipe3</option>
					<option value="4">Recipe4</option>
					<option value="5">Recipe5</option>
				</select>
			</div>
		</div>
	</form>

	<form id="addRpaScript" class="layui-form" lay-filter="form-RpaScript" style="display:none; padding-top: 20px; padding-right: 20px;">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">脚本名称</label>
			</div>
			<div class="layui-inline edit">
				<input type="text" class="layui-input" name="script" autocomplete="off" placeholder="请输入脚本名称"
					   oninput="cleanSpelChar(this)">
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label" style="width:119px;">脚本描述</label>
			</div>
			<div class="layui-inline edit">
				<textarea type="text" class="layui-textarea" style="height: 200px; width:200px; white-space: pre-wrap;overflow:auto;" name="note" autocomplete="off" placeholder="请输入少于200字的描述"
						  oninput="limitTextAreaCount(this)"></textarea>
			</div>
		</div>
	</form>
</body>
</html>