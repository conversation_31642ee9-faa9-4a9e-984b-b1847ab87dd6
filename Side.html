﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<title>机台信息</title>
	<link rel="stylesheet" href="lib/layui/css/layui.css" />
	<link rel="stylesheet" href="css/common.css" />
	<link rel="stylesheet" href="../user/loginMenu.docss" />
	<style type="text/css">
		body::-webkit-scrollbar {
			width: 4px;
			border-radius: 4px;
			background-color: lightskyblue;
			color: lightskyblue;
		}

		body::-webkit-scrollbar-thumb {
			background-color: deepskyblue;
		}

		th, td {
			border-bottom: 1px dashed bisque;
			padding: 4px;
		}

		.video-info {
			white-space: nowrap;
			width: 100%;
		}

			.video-info th {
				text-align: right;
			}

		.device-info {
			white-space: nowrap;
			width: 100%;
		}

			.device-info th {
				text-align: right;
				color: #000000d9;
			}

		.layui-collapse {
			margin-left: 5px;
		}

		.layui-colla-title {
			background-color: transparent;
			color: #000000d9;
			font-size: 17px;
		}

		h3 {
			text-align: center;
			text-decoration: underline;
			font-weight: bold;
		}

		#div-button {
			text-align: center;
			font-weight: bold;
		}

			#div-button div {
				width: 90%;
				height: 50px;
				line-height: 50px;
				border: 1px solid darkgreen;
				margin: 8px auto;
			}
	</style>
	<script type="text/javascript">
		window.webName = 'side';
	</script>
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js?v=4"></script>
	<script type="text/javascript" src="../local.config.js"></script>
	<script type="text/javascript" src="js/common.js"></script>
	<script type="text/javascript" src="../user/loginMenu.dojs"></script>
	<script type="text/javascript">
		window.doText = function (data)
		{
			if (!window.Data)
				return false;

			var j = window.Data('Video', '');
			if (!j || !j.ID)
			{
				j = window.Data('VideoName', '');
				if (!!j)
				{
					$('#video_name').html(j);
					$('#v_Name').html(j);
				}
				j = window.Data('VideoUrl', '');
				if (!!j)
					$('#v_Url').html(j);
				return false;
			}

			j = window.ldb('#' + j);
			if (!j)
				return false;

			try
			{
				j = $.eval(j);
			}
			catch (x)
			{
				console.error(x);
				return false;
			}

			if (!j.Url)
				return false;

			j.Uri = new Uri(j.Url);
			j.Uri.upper();
			if (j.Uri.SCHEME == window.VideoData.UriSchemeMC)
				do_KVM_MC.call(j, j.Uri);
			else
				$('#div-mc-info').remove();
			if (j.SweepData)
				window.doSweepData.call(j, j.SweepData);
			$('#video_name').html(j.Name);
			$('#v_Name').html(j.Name);
			$('#v_Url').html(j.Url);
			window.video = j;
		};

		window.doSweepData = function (data)
		{
			if (!data)
				return false;

			var qs = $.parseUrl(data);
			qs.us = [];
			for (var i in qs)
			{
				if (!i || !isNaN(i) || !qs[i] || !qs[i].substring)
					continue;

				var u = {};
				u.v = qs[i];
				u.vs = qs[i].split('/');
				if (u.vs.length < 7)
					continue;

				u.last = parseInt(u.vs[0]);
				u.work = parseInt(u.vs[1]);
				if (isNaN(u.last) || isNaN(u.work))
					continue;

				for (var ii = u.vs.length - 1; ii > 1; ii--)
				{
					data = u.vs[ii]
					if (data[0] == 'U')
						u.id = data.substring(1);
					else if (data[0] == 'S')
						u.sid = data.substring(1);
					else if (data[0] == 'T')
						u.name = data.substring(1);
				}
				u.eid = i;
				qs.us.push(u);
			}
			if (!qs.us.length)
				return;

			$('#userList').empty();
			qs.view = 0;
			qs.wait = 0;
			qs.work = 0;
			qs.html = '';
			for (var i = qs.us.length - 1; i >= 0; --i)
			{
				var u = qs.us[i];
				qs.html += '<tr><td>' + u.name + '</td><td>'
				if (u.work > window.CS.ExtraData.STATUS_WAITING)
				{
					qs.work++;
					qs.worker = u;
					qs.html = qs.html + lang('操作中') + '</td></tr>';
				}
				else if (u.work == window.CS.ExtraData.STATUS_WAITING)
				{
					qs.wait++;
					qs.html = qs.html + lang('排队中') + '</td></tr>';
				}
				else
				{
					qs.view++;
					qs.html = qs.html + lang('观看中') + '</td></tr>';
				}
			}
			$('#userList').append(qs.html);
			$('#sweeper').html(!qs.worker ? '-' : qs.worker.name);
			$('#sweepers').html('<span title="' + lang('排队人数') + '" style="color: darkorange;">' + qs.wait + '</span> / <span title="' + lang('观看人数') + '" style="color: darkgreen;">' + qs.view + '</span>');
			window.sweepData = qs;
		}

		window.do_KVM_MC = function (data)
		{
			if (!data)
				return data;

			if (!!data.Uri)
				data = data.Uri;
			if (!!data.SCHEME)
			{
				var ajax = {};
				ajax.url = '/http:/' + data.Host + '/cgi-bin/devinfo.cgi';
				ajax.success = arguments.callee;
				ajax.error = arguments.callee;
				$.ajax(ajax);
				return data;
			}

			try
			{
				if (!!data.substring)
					data = $.eval(data);
				for (var i in data)
					$('#d_' + i).text(data[i]);
				window.kvm = data;
			}
			catch (x)
			{
				console.error(x);
			}
			return data;
		};

		setInterval(function ()
		{
			if (window.iw == window.innerWidth)
				return;

			window.iw = window.innerWidth;
			if (window.iw > 64)
			{
				$('#div-information').show();
				$('#div-button').hide();
			}
			else
			{
				$('#div-information').hide();
				$('#div-button').show();
			}
		}, 200);

		$(document).ready(function () { window.doText.call(window, window); });
	</script>
</head>
<body style="background-color: skyblue;">
	<form class="layui-form">
		<div class="layui-form-item" id="div-information">
			<h1 id="video_name" style="padding: 10px; text-align: center; width: 100%; position: fixed; top: 0px; background-color: skyblue; white-space: nowrap;">--</h1>
			<table cellpadding="0" cellspacing="1" border="0" style="width: 100%; text-align: center; background-color: brown; margin-top: 50px;">
				<thead>
					<tr>
						<th style="background-color: darkblue; color: white;">Lot</th>
						<th style="background-color: darkblue; color: white;">Eha</th>
						<th style="background-color: darkblue; color: white;">Max</th>
						<th style="background-color: darkblue; color: white;">Flow</th>
						<th style="background-color: darkblue; color: white;">Pr</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td style="background-color: red; color: white;">2</td>
						<td style="background-color: green; color: black;">DO</td>
						<td style="background-color: white; color: darkslateblue;">45</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: yellow; color: black;">Low</td>
					</tr>
					<tr>
						<td style="background-color: deepskyblue; color: black;">5</td>
						<td style="background-color: darkmagenta; color: wheat;">EO</td>
						<td style="background-color: white; color: black;">43</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: white; color: black;">None</td>
					</tr>
					<tr>
						<td style="background-color: darkgoldenrod; color: white;">9</td>
						<td style="background-color: darkgoldenrod; color: black;">FH</td>
						<td style="background-color: white; color: darkblue;">34</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: yellow; color: black;">Low</td>
					</tr>
					<tr>
						<td style="background-color: red; color: white;">4</td>
						<td style="background-color: darkgreen; color: white;">EN</td>
						<td style="background-color: aquamarine; color: black;">54</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: cadetblue; color: black;">New</td>
					</tr>
					<tr>
						<td style="background-color: red; color: white;">4</td>
						<td style="background-color: green; color: black;">DO</td>
						<td style="background-color: white; color: black;">34</td>
						<td style="background-color: green; color: wheat;">H</td>
						<td style="background-color: yellow; color: black;">Low</td>
					</tr>
					<tr>
						<td style="background-color: deepskyblue; color: black;">5</td>
						<td style="background-color: darkkhaki; color: black;">HH</td>
						<td style="background-color: white; color: black;">45</td>
						<td style="background-color: green; color: wheat;">H</td>
						<td style="background-color: green; color: white;">Full</td>
					</tr>
					<tr>
						<td style="background-color: red; color: white;">2</td>
						<td style="background-color: green; color: black;">DO</td>
						<td style="background-color: aquamarine; color: black;">54</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: yellow; color: black;">Low</td>
					</tr>
					<tr>
						<td style="background-color: khaki; color: darkgoldenrod;">7</td>
						<td style="background-color: azure; color: darkblue;">WH</td>
						<td style="background-color: red; color: white;">65</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: yellow; color: black;">Low</td>
					</tr>
					<tr>
						<td style="background-color: lightpink; color: darkmagenta;">3</td>
						<td style="background-color: navajowhite; color: darkorange;">X</td>
						<td style="background-color: aquamarine; color: black;">54</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: darkgreen; color: white;">High</td>
					</tr>
					<tr>
						<td style="background-color: hotpink; color: black;">7</td>
						<td style="background-color: green; color: black;">DO</td>
						<td style="background-color: white; color: black;">45</td>
						<td style="background-color: white; color: black;">F</td>
						<td style="background-color: white; color: black;">None</td>
					</tr>
					<tr>
						<td style="background-color: red; color: white;">3</td>
						<td style="background-color: rebeccapurple; color: yellow;">XH</td>
						<td style="background-color: white; color: darkgreen;">32</td>
						<td style="background-color: green; color: wheat;">H</td>
						<td style="background-color: yellow; color: black;">Low</td>
					</tr>
				</tbody>
			</table>
			<hr />
			<table cellpadding="0" cellspacing="0" border="0" class="video-info">
				<caption><h3 style="text-align:left; padding-left: 100px;">设备状态</h3></caption>
				<tr>
					<th>设备名称：</th>
					<td id="v_Name">-</td>
				</tr>
				<tr>
					<th>当前操作人：</th>
					<td id="sweeper">-</td>
				</tr>
				<tr>
					<th>当前排队人数：</th>
					<td id="sweepers">-</td>
				</tr>
				<tr>
					<th>设备地址：</th>
					<td id="v_Url">-</td>
				</tr>
			</table>
			<table cellpadding="0" cellspacing="1" border="0" style="width: 100%; text-align: center;">
				<thead>
					<tr>
						<th style="background-color: darkblue; color: white;">Name</th>
						<th style="background-color: darkblue; color: white;">Status</th>
					</tr>
				</thead>
				<tbody id="userList"></tbody>
			</table>
			<hr />
			<div id="div-mc-info" class="layui-collapse" lay-filter="test">
				<div class="layui-colla-item">
					<h3 class="layui-colla-title">设备信息 <i class="layui-icon layui-colla-icon"></i></h3>
					<div class="layui-colla-content">
						<table cellpadding="0" cellspacing="0" border="0" class="device-info">
							<caption style="text-align: right;"><a href="javascript://" onclick="javascript: window.do_KVM_MC.call(this, window.video);">Refresh</a></caption>
							<tr>
								<th>产品编号：</th>
								<td id="d_pn">-</td>
							</tr>
							<tr>
								<th>产品序列号：</th>
								<td id="d_sn">-</td>
							</tr>
							<tr>
								<th>固件版本：</th>
								<td id="d_APP_v">-</td>
							</tr>
							<tr>
								<th>编译时间：</th>
								<td id="d_buildtime">-</td>
							</tr>
							<tr>
								<th>BSP 版本：</th>
								<td id="d_BSP_v">-</td>
							</tr>
							<tr>
								<th>CPU 温度：</th>
								<td id="d_cpu_T">-</td>
							</tr>
							<tr>
								<th>CPU 占用率：</th>
								<td id="d_cpurate">-</td>
							</tr>
							<tr>
								<th>内存占用率：</th>
								<td id="d_memrate">-</td>
							</tr>
							<tr>
								<th>硬盘占用率：</th>
								<td id="d_rootfsdiskrate">-</td>
							</tr>
							<tr>
								<th>应用状态：</th>
								<td id="d_APP_status">-</td>
							</tr>
							<tr>
								<th>用户空间占用率：</th>
								<td id="d_userdatafsdiskrate">-</td>
							</tr>
							<tr>
								<th>应用空间占用率：</th>
								<td id="d_appfsdiskrate">-</td>
							</tr>
							<tr>
								<th>视频输入状态：</th>
								<td id="d_video_input">-</td>
							</tr>
							<tr>
								<th>视频分辨率：</th>
								<td id="d_v_resolution">-</td>
							</tr>
							<tr>
								<th>视频帧率：</th>
								<td id="d_v_fps">-</td>
							</tr>
							<tr>
								<th>视频扫描方式：</th>
								<td id="d_v_interlaced">-</td>
							</tr>
							<tr>
								<th>视频图像格式：</th>
								<td id="d_v_colorspace">-</td>
							</tr>
							<tr>
								<th>音频输入：</th>
								<td id="d_audio_input">-</td>
							</tr>
							<tr>
								<th>音频采样：</th>
								<td id="d_audio_sample">-</td>
							</tr>
							<tr>
								<th>主编码器状态：</th>
								<td id="d_encod_status">-</td>
							</tr>
							<tr>
								<th>辅编码器状态：</th>
								<td id="d_sub_encod_status">-</td>
							</tr>
							<tr>
								<th>编码帧率：</th>
								<td id="d_encod_fps">-</td>
							</tr>
							<tr>
								<th>Key Pad 状态：</th>
								<td id="d_interlock_state">-</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-form-item" id="div-button">
			<div style="background-color: darkblue; color: yellow;">DO</div>
			<div style="background-color: darkgreen; color: yellow;">XO</div>
			<div style="background-color: darkgoldenrod; color: yellow;">3</div>
			<div style="background-color: darkkhaki; color: red;">14</div>
			<div style="background-color: aliceblue; color: blue;">87</div>
			<div style="background-color: darkcyan; color: yellow;">EO</div>
			<div style="background-color: darkblue; color: yellow;">55</div>
			<div style="background-color: aqua; color: black;">23</div>
			<div style="background-color: darkcyan; color: yellow;">FHN</div>
			<div style="background-color: khaki; color: orange;">49</div>
			<div style="background-color: cyan; color: darkred;">30</div>
			<div style="background-color: darkmagenta; color: aquamarine;">ESX</div>
		</div>
		<div id="devTool" style="color: red; position: absolute; display: none; right: 0; bottom: 0px; z-index: 999;">
			<span onclick="javascript: this.innerHTML = navigator.userAgent;"></span>
			|
			<a href="javascript: void(0);" onclick="javascript: top.window.Data('Dev', location.href);">Developer Tool</a>
			|
			<a href="javascript: window.location.reload();">Reload</a>
		</div>
</form>
</body>
</html>