﻿<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Test Relay VNC</title>
	<link type="text/css" rel="stylesheet" href="lib/layui/css/layui.css">
	<link type="text/css" rel="stylesheet" href="css/common.css" />
	<link type="text/css" rel="stylesheet" href="../user/loginMenu.docss" />
	<link type="text/css" rel="stylesheet" href="lib/layui/treetable/treetable.css" />
	<script type="text/javascript" src="lib/jquery-1.12.3.min.js"></script>
	<script type="text/javascript" src="lib/layui/layui.js"></script>
	<script type="text/javascript" src="lib/pickgold.js"></script>
	<script type="text/javascript" src="lib/layui/excel.js"></script>
	<script type="text/javascript">
		(function ()
		{
			var t = location.search;
			if (!t)
				t = '**********?Password=123456';//**********?Password=1111
			else
				t = t.substring(1, t.length);
			if (t.indexOf('?') > 0 || t.indexOf('&') > 0)
				t += '&Now=' + Date.now();
			else
				t += '?Now=' + Date.now();
			t = t.replace(/p(wd)?=/img, 'Password=');
			t = t.replace(/:([0-9]+)[\?&]/img, '?Port=$1&');
			if (t.indexOf('/') < 0)
				t = '127.0.0.1:7069/' + t;
			if (t.indexOf('&') > 0 && t.indexOf('?') < 0)
				t = t.substring(0, t.indexOf('&')) + '?' + t.substring(t.indexOf('&') + 1, t.length);
			window.frameWS = new WebSocket('ws://' + t);
			window.frameWS.binaryType = 'blob';//'arraybuffer';//
			window.frameWS.onopen = function (e)
			{
				console.log(e.target.url + ' open.');
			};
			window.frameWS.onerror = function (e)
			{
				console.log(e.target.url + ' error.');
			};
			window.frameWS.onmessage = function (e)
			{
				var old = $('#img').attr('src');
				var src = URL.createObjectURL(e.data);
				$('#img').attr('src', src);
				if (/^blob:/img.test(old))
					URL.revokeObjectURL(old);
			};
			window.frameWS.onclose = arguments.callee;
		}).call(window);
	</script>
</head>
<body>
	<img id="img" />
	<div>
		TestRelayVNC.html?[RelayVNC.ip:port/]vnc-ip?[Password=password/1111]&amp;[Port=port/9700]<br />
		TestRelayVNC.html?********<br />
		TestRelayVNC.html?********?Password=1111<br />
		TestRelayVNC.html?********?Password=1111&Port=5900<br />
		TestRelayVNC.html?********?Password=1111&Port=5900<br />
		TestRelayVNC.html?********:7069/********?Password=1111&Port=9700<br />
		TestRelayVNC.html?********:5900<br />
		TestRelayVNC.html?********:5900?Password=1111<br />
	</div>
</body>
</html>
